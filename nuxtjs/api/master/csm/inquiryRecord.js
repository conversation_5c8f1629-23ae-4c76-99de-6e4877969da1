import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 询价记录模块API
 */
export const inquiryRecordApi = {
  /**
   * 创建询价记录
   * @param {Object} data 询价记录数据
   * @returns {Promise} 响应数据
   */
  createInquiryRecord(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records`,
      method: 'post',
      data
    });
  },

  /**
   * 获取询价记录列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.productName 商品名称
   * @param {string} params.supplierName 供应商名称
   * @param {string} params.inquirer 询价员
   * @param {string} params.startTime 开始时间
   * @param {string} params.endTime 结束时间
   * @param {number} params.recordStatus 记录状态
   * @returns {Promise} 响应数据
   */
  getInquiryRecords(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records`,
      method: 'get',
      params
    });
  },

  /**
   * 获取询价记录详情
   * @param {string} id 记录ID
   * @returns {Promise} 响应数据
   */
  getInquiryRecordDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/${id}`,
      method: 'get'
    });
  },

  /**
   * 更新询价记录
   * @param {string} id 记录ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  updateInquiryRecord(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/${id}`,
      method: 'put',
      data
    });
  },

  /**
   * 删除询价记录
   * @param {string} id 记录ID
   * @returns {Promise} 响应数据
   */
  deleteInquiryRecord(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/${id}`,
      method: 'delete'
    });
  },

  /**
   * 批量删除询价记录
   * @param {Array} ids 记录ID数组
   * @returns {Promise} 响应数据
   */
  batchDeleteInquiryRecords(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/batch-delete`,
      method: 'post',
      data: { ids }
    });
  },

  /**
   * 获取询价记录统计信息
   * @param {Object} params 查询参数
   * @param {string} params.startTime 开始时间
   * @param {string} params.endTime 结束时间
   * @returns {Promise} 响应数据
   */
  getInquiryRecordStats(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/stats/summary`,
      method: 'get',
      params
    });
  },

  /**
   * 根据SKU ID获取关联的供应商信息
   * @param {string} skuId SKU ID（对应product_code字段）
   * @returns {Promise} 响应数据
   */
  getSuppliersBySkuId(skuId) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/inquiry-records/suppliers/by-sku/${skuId}`,
      method: 'get'
    });
  }
};

export default inquiryRecordApi;
