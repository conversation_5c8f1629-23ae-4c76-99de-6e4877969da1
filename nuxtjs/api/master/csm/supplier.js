import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 供应商API
 */
export const supplierApi = {
  /**
   * 获取供应商选择列表（简化版本）
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getSelectList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/select-list`,
      method: 'get',
      params
    })
  },

  /**
   * 获取供应商关联关系选择列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getRelationSelectList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/relation-select-list`,
      method: 'get',
      params
    })
  },

  /**
   * 获取供应商列表
   * @param {Object} params 查询参数
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取供应商详情
   * @param {number} id 供应商ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建供应商
   * @param {Object} data 供应商数据
   * @returns {Promise} 响应数据
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers`,
      method: 'post',
      data
    })
  },

  /**
   * 更新供应商
   * @param {number} id 供应商ID
   * @param {Object} data 更新数据
   * @returns {Promise} 响应数据
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除供应商
   * @param {number} id 供应商ID
   * @returns {Promise} 响应数据
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除供应商
   * @param {Array<number>} ids 供应商ID数组
   * @returns {Promise} 响应数据
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/batch-delete`,
      method: 'post',
      data: { ids }
    })
  },

  /**
   * 更新供应商基础信息
   * @param {number} id 供应商ID
   * @param {Object} data 基础信息数据
   * @returns {Promise} 响应数据
   */
  updateBasicInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/basic-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新供应商账户信息
   * @param {number} id 供应商ID
   * @param {Object} data 账户信息数据
   * @returns {Promise} 响应数据
   */
  updateAccountInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/account-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新供应商品牌信息
   * @param {number} id 供应商ID
   * @param {Object} data 品牌信息数据
   * @returns {Promise} 响应数据
   */
  updateBrandInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/brand-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新供应商联系人信息
   * @param {number} id 供应商ID
   * @param {Object} data 联系人信息数据
   * @returns {Promise} 响应数据
   */
  updateContactInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/contact-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新供应商协议信息
   * @param {number} id 供应商ID
   * @param {Object} data 协议信息数据
   * @returns {Promise} 响应数据
   */
  updateAgreementInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/agreement-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新供应商附件信息
   * @param {number} id 供应商ID
   * @param {Object} data 附件信息数据
   * @returns {Promise} 响应数据
   */
  updateAttachmentInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/attachment-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更新提交人信息
   * @param {number} id 供应商ID
   * @param {Object} data 提交人信息数据
   * @returns {Promise} 响应数据
   */
  updateSubmitterInfo(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/submitter-info`,
      method: 'put',
      data
    })
  },

  /**
   * 更换复核人
   * @param {number} id 供应商ID
   * @param {Object} data 更换复核人数据
   * @returns {Promise} 响应数据
   */
  changeReviewer(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/${id}/change-reviewer`,
      method: 'put',
      data
    })
  },

  /**
   * 获取供应商统计信息
   * @returns {Promise} 响应数据
   */
  getStats() {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/suppliers/stats`,
      method: 'get'
    })
  }
}
