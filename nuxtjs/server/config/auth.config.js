/**
 * 认证相关配置
 * 包括公开路由、JWT设置等
 */
module.exports = {
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'julingcloud-secret-key',
    expire: process.env.JWT_EXPIRE || '24h'
  },
  
  // 公开路径配置（无需JWT认证）
  publicPaths: [
    // 系统级公开路径
    '/api/v1/common/public-key',   // 公钥获取接口
    '/api/v1/common/captcha',      // 验证码接口
    
    
    // 认证相关公开路径
    '/api/v1/master/auth/login',   // 登录接口
    '/api/v1/master/auth/phone-login', // 手机号登录接口
    '/api/v1/master/auth/captcha',  // 验证码接口
    '/api/v1/master/auth/register', // 注册接口
    '/api/v1/master/auth/sms-verification-code', // 短信验证码接口
    
    // 第三方集成相关公开路径
    '/api/v1/master/system/integration/aliyun/sms/verification-code', // 短信验证码接口
    '/api/v1/master/system/configure/open/sms/templates', // 短信模板配置开放接口
    
    // 文件访问相关公开路径
    '/uploads',                    // 本地上传文件访问路径
    '/public/uploads',              // 公共目录下的上传文件访问路径
    
    // 留言板相关公开路径
    '/api/v1/message-board',       // 留言板创建接口

    // 微信支付相关公开路径
    '/api/v1/master/wechat-pay/*', // 微信支付所有接口
    
    // 商城用户相关公开路径
    '/api/v1/mall/user/login',     // 商城用户登录接口
    '/api/v1/mall/user/sms-login', // 商城用户短信登录接口
    '/api/v1/mall/user/register',  // 商城用户注册接口
    '/api/v1/mall/user/captcha',   // 商城用户验证码接口
    '/api/v1/mall/user/test-phone-query', // 测试手机号查询接口
    '/api/v1/mall/user/test-login', // 测试登录接口
    '/api/v1/mall/user/test-password', // 测试密码验证接口
    '/api/v1/mall/user/test-redis', // 测试 Redis 连接接口
    
    // 新模块商城用户相关公开路径
    '/api/mall/user/login',        // 新模块商城用户登录接口
    '/api/mall/user/register',     // 新模块商城用户注册接口
    '/api/mall/user/captcha',      // 新模块商城用户验证码接口
    
    // 服务商用户相关公开路径
    '/api/v1/provider/user/captcha',         // 服务商注册验证码接口
    '/api/v1/provider/user/register',        // 服务商注册接口
    '/api/v1/provider/user/login',           // 服务商登录接口
    '/api/v1/provider/user/phone-login',     // 服务商手机验证码登录接口
    '/api/v1/provider/user/login-captcha',   // 服务商登录验证码接口
    
    // API文档路径
    '/api-docs',                   // Swagger UI
    '/api-docs/*',                 // Swagger 资源
    
    // 其他公开API
    // '/api/v1/merchant/products',    // 产品列表公开接口
    // '/api/v1/merchant/categories',  // 分类列表公开接口
    // '/api/v1/master/orders',  // 分类列表公开接口
    // '/api/v1/master/orders/*',  // 分类列表公开接口

    // 留言管理
    '/api/v1/official/message-management/messages',//添加留言接口

    //案例管理
    '/api/v1/official/case-management/face/cases*', //门户获取案例列表和详情

    //招聘管理
    '/api/v1/official/recruitment/face*', //门户获取招聘信息

    //企业信息管理
    '/api/v1/official/enterprise-information/face*', //门户获取企业信息列表和详情

    // GitLab贡献统计
    '/api/v1/gitlab/contributions', //GitLab贡献统计数据接口

    // 区域管理
    '/api/v1/master/region/tree*', // 获取区域树结构接口（包含查询参数）

    // 商城商品相关公开路径
    '/api/v1/mall/goods/products',        // 商品列表接口
    '/api/v1/mall/goods/product/*',       // 商品详情接口
    '/api/v1/mall/goods/category/tree',   // 商品分类树接口
    '/api/v1/mall/goods/category/*',      // 商品分类详情接口
    '/api/v1/mall/goods/tags',            // 商品标签列表接口
    '/api/v1/mall/goods/brands',          // 商品品牌列表接口
    '/api/v1/mall/goods/services',        // 商品服务列表接口
    '/api/v1/mall/goods/featured-products', // 精选商品列表接口
    '/api/v1/mall/goods/featured-brands',   // 精选品牌列表接口

    // 快递100异步通知回调接口
    '/api/v1/master/system/integration/express100/callback',     // 快递100回调接口
    '/api/v1/master/system/integration/express-track/callback',  // 快递轨迹回调接口

  ]
};
