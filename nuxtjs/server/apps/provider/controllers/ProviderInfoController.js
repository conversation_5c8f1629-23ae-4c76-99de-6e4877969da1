const { v4: uuidv4 } = require('uuid');
const ProviderBaseController = require('./base/ProviderBaseController');

/**
 * 服务商信息控制器
 */
class ProviderInfoController extends ProviderBaseController {
  /**
   * 构造函数
   * @param {Object} prisma - Prisma客户端实例
   */
  constructor(prisma) {
    if (!prisma) {
      throw new Error('Prisma client is required');
    }
    super(prisma);
    console.log('ProviderInfoController initialized with prisma:', !!this.prisma);
  }

  /**
   * 生成唯一ID
   * @returns {bigint} 生成的唯一ID
   */
  generateId() {
    // 生成一个随机的bigint ID，避免使用UUID
    const timestamp = BigInt(Date.now());
    const random = BigInt(Math.floor(Math.random() * 1000000));
    return timestamp * 1000000n + random;
  }

  /**
   * 处理值，确保正确序列化
   * @param {any} value - 要处理的值
   * @returns {string} 处理后的值
   */
  processValue(value) {
    if (value === null || value === undefined) {
      return null;
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return value.toString();
  }

  /**
   * 管理员重置服务商密码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async masterResetPassword(req, res) {
    try {
      console.log('管理员开始处理重置服务商密码请求');
      console.log('请求参数:', req.body);

      const { provider_id, newPassword, confirmPassword } = req.body;

      // 验证必填字段
      if (!provider_id) {
        return this.fail(res, '服务商ID不能为空', 400);
      }

      if (!newPassword) {
        return this.fail(res, '新密码不能为空', 400);
      }

      if (!confirmPassword) {
        return this.fail(res, '确认密码不能为空', 400);
      }

      // 验证新密码和确认密码是否一致
      if (newPassword !== confirmPassword) {
        return this.fail(res, '新密码和确认密码不一致', 400);
      }

      // 验证新密码长度
      if (newPassword.length < 6 || newPassword.length > 20) {
        return this.fail(res, '新密码长度应为6-20个字符', 400);
      }

      console.log('查询服务商信息, 服务商ID:', provider_id);
      // 将服务商ID转换为BigInt类型
      const providerIdBigInt = BigInt(provider_id);
      console.log('转换后的服务商ID(类型):', typeof providerIdBigInt);

      // 查询服务商信息
      const users = await this.prisma.$queryRaw`
        SELECT id, company_name, phone
        FROM "provider"."provider_user"
        WHERE "id" = ${providerIdBigInt}
        AND "deleted_at" IS NULL
      `;

      console.log('查询结果:', users ? `找到${users.length}个服务商` : '未找到服务商');

      if (!users || users.length === 0) {
        return this.fail(res, '服务商不存在', 404);
      }

      const user = users[0];
      console.log('找到服务商:', { id: user.id, company_name: user.company_name, phone: user.phone });

      // 加密新密码
      console.log('开始加密新密码');
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // 更新密码
      console.log('开始更新服务商密码');
      const now = Date.now();
      await this.prisma.$executeRaw`
        UPDATE "provider"."provider_user"
        SET "password" = ${hashedPassword},
            "updated_at" = ${now}
        WHERE "id" = ${providerIdBigInt}
      `;

      console.log('服务商密码重置成功');
      return this.success(res, {
        provider_id: user.id.toString(),
        company_name: user.company_name,
        phone: user.phone
      }, '服务商密码重置成功');
    } catch (error) {
      console.error('重置服务商密码过程中出错:', error);
      return this.fail(res, `重置密码失败: ${error.message}`, 500);
    }
  }

  /**
   * 检查表是否使用字符串类型作为ID
   * @param {string} tableName - 表名
   * @returns {Promise<boolean>} 是否使用字符串ID
   */
  async isUuidTable(tableName) {
    try {
      // 查询表结构
      const tableInfo = await this.prisma.$queryRaw`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'provider'
        AND table_name = ${tableName}
        AND column_name = 'id'
      `;

      // 检查ID列的数据类型
      if (tableInfo && tableInfo.length > 0) {
        const dataType = tableInfo[0].data_type.toLowerCase();
        console.log(`表 ${tableName} 的ID类型为: ${dataType}`);
        return dataType === 'uuid' || dataType === 'text' || dataType === 'varchar' || dataType === 'character varying';
      }
      return false;
    } catch (error) {
      console.error(`检查表 ${tableName} 结构失败:`, error);
      // 默认返回false，使用安全的方式
      return false;
    }
  }

  /**
   * 更新用户状态为审核中
   * @param {bigint} userId - 用户ID
   * @param {number} timestamp - 时间戳
   * @param {Object} businessInfo - 业务信息
   * @returns {Promise<void>}
   */
  async updateUserStatus(userId, timestamp, businessInfo) {
    try {
      // 从businessInfo中获取公司名称、法人姓名和地址信息
      // 优先使用comname字段，如果不存在则使用cardData.name
      const companyName = businessInfo?.comname || businessInfo?.cardData?.name || '';
      const legalPersonName = businessInfo?.legal_representative || businessInfo?.cardData?.legalPerson;
      // 优先使用comaddress字段，如果不存在则使用cardData.address
      const detailAddress = businessInfo?.comaddress || businessInfo?.cardData?.address || '';

      console.log('更新用户信息:', {
        companyName,
        legalPersonName,
        detailAddress
      });

      // 首先查询当前用户的状态
      const currentUser = await this.prisma.$queryRaw`
        SELECT "status" FROM "provider"."provider_user"
        WHERE "id" = ${userId}
        AND "deleted_at" IS NULL
      `;

      // 如果用户存在且状态已经是1，则不更新status字段
      if (currentUser && currentUser.length > 0 && currentUser[0].status === 1) {
        console.log('用户状态已经是1，不更新status字段');
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "company_name" = ${companyName},
              "legal_person_name" = ${legalPersonName},
              "detail_address" = ${detailAddress},
              "updated_at" = ${timestamp},
              "updated_by" = ${userId}
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
      } else {
        // 如果状态不是1，则正常更新包括status
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "status" = 2,
              "company_name" = ${companyName},
              "legal_person_name" = ${legalPersonName},
              "detail_address" = ${detailAddress},
              "updated_at" = ${timestamp},
              "updated_by" = ${userId}
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建服务商信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async create(req, res) {
    try {
      console.log('Creating provider info with prisma:', !!this.prisma);

      const { business_info, user_id } = req.body;

      if (!business_info || !user_id) {
        return this.fail(res, '缺少必要参数', 400);
      }

      // 使用秒级时间戳，避免整数溢出
      const timestamp = Math.floor(Date.now() / 1000);
      const results = [];
      const providerId = BigInt(user_id);

      // 企业名称重复校验
      const companyName = business_info?.comname || business_info?.cardData?.name || '';
      if (companyName) {
        console.log('校验企业名称是否重复:', companyName);

        // 查询是否存在相同企业名称的记录（排除当前用户）
        const existingCompany = await this.prisma.$queryRaw`
          SELECT id, company_name FROM "provider"."provider_user"
          WHERE company_name = ${companyName}
          AND id != ${providerId}
          AND deleted_at IS NULL
        `;

        if (existingCompany && existingCompany.length > 0) {
          console.log('发现重复的企业名称:', existingCompany);
          return this.fail(res, '该公司名称已注册', 400);
        }

        console.log('企业名称校验通过');
      }

      // 更新用户状态为审核中
      await this.updateUserStatus(providerId, timestamp, business_info);

      // 检查provider_info表是否使用字符串ID
      const useStringId = await this.isUuidTable('provider_info');
      console.log('provider_info表是否使用字符串ID:', useStringId);

      for (const [type, value] of Object.entries(business_info)) {
        try {
          // 检查记录是否已存在
          const existingRecords = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_info"
            WHERE "type" = ${type}
            AND "provider_id" = ${providerId}
            AND "deleted_at" IS NULL
          `;

          const processedValue = this.processValue(value);

          if (existingRecords && existingRecords.length > 0) {
            // 如果记录已存在，则更新
            const result = await this.prisma.$executeRaw`
              UPDATE "provider"."provider_info"
              SET "value" = ${processedValue},
                  "updated_by" = ${providerId},
                  "updated_at" = ${timestamp}
              WHERE "type" = ${type}
              AND "provider_id" = ${providerId}
              AND "deleted_at" IS NULL
            `;
            results.push({ type, action: 'update', result });
          } else {
            // 如果记录不存在，则创建
            let result;
            if (useStringId) {
              // 使用UUID作为ID
              const id = uuidv4();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${processedValue},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            } else {
              // 使用bigint作为ID
              const id = this.generateId();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${processedValue},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            }
            results.push({ type, action: 'create', result });
          }
        } catch (error) {
          console.error(`处理 ${type} 失败:`, error);
          results.push({ type, action: 'error', error: error.message });
        }
      }

      return this.success(res, results, '创建成功');
    } catch (error) {
      console.error('创建服务商信息失败:', error);
      return this.fail(res, error.message, 500);
    }
  }

  /**
   * 更新服务商信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async update(req, res) {
    try {
      console.log('Updating provider info with prisma:', !!this.prisma);

      const { business_info, user_id, platform_rate } = req.body;
      console.log('请求参数:', { user_id, business_info_keys: Object.keys(business_info) });

      if (!business_info || !user_id) {
        return this.fail(res, '缺少必要参数', 400);
      }

      // 使用秒级时间戳，避免整数溢出
      const timestamp = Math.floor(Date.now() / 1000);
      const results = [];
      const providerId = BigInt(user_id);
      console.log('转换后的providerId:', providerId.toString());

      // 验证用户是否存在
      const existingUser = await this.prisma.$queryRaw`
        SELECT "id", "username", "status"
        FROM "provider"."provider_user"
        WHERE "id" = ${providerId}
        AND "deleted_at" IS NULL
      `;

      console.log('查询用户结果:', existingUser);

      if (!existingUser || existingUser.length === 0) {
        console.log('用户不存在，尝试创建用户');
        // 用户不存在，创建一个基本用户
        try {
          // 从business_info中获取基本信息
          const companyName = business_info?.comname || business_info?.cardData?.name || '';
          const legalPersonName = business_info?.legalPersonName || business_info?.cardData?.legalPerson || '';
          const detailAddress = business_info?.comaddress || business_info?.cardData?.address || '';
          const phone = business_info?.contacts?.[0]?.phone || '';

          // 创建用户
          await this.prisma.$executeRaw`
            INSERT INTO "provider"."provider_user" (
              "id", "username", "phone", "status", "company_name", 
              "legal_person_name", "detail_address", "created_at", "created_by"
            ) VALUES (
              ${providerId}, ${companyName || '新用户'}, ${phone}, 2,
              ${companyName}, ${legalPersonName}, ${detailAddress},
              ${timestamp}, ${providerId}
            )
          `;
          console.log('创建用户成功');
        } catch (error) {
          console.error('创建用户失败:', error);
          return this.fail(res, '用户不存在且创建失败: ' + error.message, 400);
        }
      } else {
        console.log('用户存在，继续更新');
        // 更新用户状态为审核中
        await this.updateUserStatus(providerId, timestamp, business_info);
      }

      // 检查provider_info表是否使用字符串ID
      const useStringId = await this.isUuidTable('provider_info');
      console.log('provider_info表是否使用字符串ID:', useStringId);

      // 首先查询该用户是否有现有记录
      const existingUserRecords = await this.prisma.$queryRaw`
        SELECT "type", "value", "provider_id"
        FROM "provider"."provider_info"
        WHERE "provider_id" = ${providerId}
        AND "deleted_at" IS NULL
        LIMIT 1
      `;

      console.log('现有用户记录:', existingUserRecords);

      // 如果没有找到记录，尝试查询所有provider_id
      if (!existingUserRecords || existingUserRecords.length === 0) {
        const allProviderIds = await this.prisma.$queryRaw`
          SELECT DISTINCT "provider_id"
          FROM "provider"."provider_info"
          WHERE "deleted_at" IS NULL
          ORDER BY "provider_id"
        `;
        console.log('系统中的所有provider_id:', allProviderIds.map(item => item.provider_id.toString()));
      }

      for (const [type, value] of Object.entries(business_info)) {
        try {
          // 检查记录是否存在
          const existingRecords = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_info"
            WHERE "type" = ${type}
            AND "provider_id" = ${providerId}
            AND "deleted_at" IS NULL
          `;

          console.log(`检查类型 ${type} 是否存在:`, existingRecords);
          const processedValue = this.processValue(value);

          if (existingRecords && existingRecords.length > 0) {
            // 如果记录存在，则更新
            console.log(`更新 ${type} 的值:`, processedValue);
            const result = await this.prisma.$executeRaw`
              UPDATE "provider"."provider_info"
              SET "value" = ${processedValue},
                  "updated_by" = ${providerId},
                  "updated_at" = ${timestamp}
              WHERE "type" = ${type}
              AND "provider_id" = ${providerId}
              AND "deleted_at" IS NULL
            `;
            results.push({ type, action: 'update', result });
          } else {
            // 如果记录不存在，则创建
            console.log(`创建 ${type} 的值:`, processedValue);
            let result;
            if (useStringId) {
              // 使用UUID作为ID
              const id = uuidv4();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${processedValue},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            } else {
              // 使用bigint作为ID
              const id = this.generateId();
              console.log(`为 ${type} 生成的ID:`, id.toString());
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${processedValue},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            }
            results.push({ type, action: 'create', result });
          }
        } catch (error) {
          console.error(`处理 ${type} 失败:`, error);
          results.push({ type, action: 'error', error: error.message });
        }
      }

      // 处理平台费率数据
      if (platform_rate && Array.isArray(platform_rate)) {
        try {
          // 检查provider_platform_rate表是否使用字符串ID
          const useStringId = await this.isUuidTable('provider_platform_rate');
          console.log('provider_platform_rate表是否使用字符串ID:', useStringId);

          for (const rateInfo of platform_rate) {
            const { id: channelId, rate } = rateInfo;

            if (!channelId || rate === undefined) {
              continue; // 跳过无效数据
            }

            const channelIdBigInt = BigInt(channelId);
            const rateDecimal = parseFloat(rate);

            // 检查该服务商该渠道是否已有费率记录
            const existingRates = await this.prisma.$queryRaw`
              SELECT id FROM "provider"."provider_platform_rate"
              WHERE "provider_id" = ${providerId}
              AND "channel_id" = ${channelIdBigInt}
              AND "deleted_at" IS NULL
            `;

            if (existingRates && existingRates.length > 0) {
              // 如果记录存在，则更新
              const result = await this.prisma.$executeRaw`
                UPDATE "provider"."provider_platform_rate"
                SET "rate" = ${rateDecimal},
                    "updated_at" = ${timestamp},
                    "updated_by" = ${providerId}
                WHERE "provider_id" = ${providerId}
                AND "channel_id" = ${channelIdBigInt}
                AND "deleted_at" IS NULL
              `;
              results.push({ type: 'platform_rate', channelId, action: 'update', result });
            } else {
              // 如果记录不存在，则创建
              let result;
              if (useStringId) {
                // 使用UUID作为ID
                const id = uuidv4();
                result = await this.prisma.$executeRaw`
                  INSERT INTO "provider"."provider_platform_rate" (
                    "id", "provider_id", "channel_id", "rate",
                    "created_at", "updated_at", "created_by"
                  ) VALUES (
                    ${id}, ${providerId}, ${channelIdBigInt}, ${rateDecimal},
                    ${timestamp}, ${timestamp}, ${providerId}
                  )
                `;
              } else {
                // 使用bigint作为ID
                const id = this.generateId();
                result = await this.prisma.$executeRaw`
                  INSERT INTO "provider"."provider_platform_rate" (
                    "id", "provider_id", "channel_id", "rate",
                    "created_at", "updated_at", "created_by"
                  ) VALUES (
                    ${id}, ${providerId}, ${channelIdBigInt}, ${rateDecimal},
                    ${timestamp}, ${timestamp}, ${providerId}
                  )
                `;
              }
              results.push({ type: 'platform_rate', channelId, action: 'create', result });
            }
          }
        } catch (error) {
          console.error('处理平台费率失败:', error);
          results.push({ type: 'platform_rate', action: 'error', error: error.message });
        }
      }

      return this.success(res, results, '更新成功');
    } catch (error) {
      console.error('更新服务商信息失败:', error);
      return this.fail(res, error.message, 500);
    }
  }

  /**
   * 获取服务商信息详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDetail(req, res) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      try {
        // 将用户ID转换为BigInt类型
        const userIdBigInt = BigInt(userId);

        // 查询服务商基本信息
        const userInfo = await this.prisma.$queryRaw`
          SELECT 
            "id", "username", "phone", "status", "last_login_ip", 
            "last_login_time", "created_at", "updated_at", "remark", 
            "order_count", "order_amount", "company_name", "legal_person_name", 
            "detail_address", "salesman_id"
          FROM "provider"."provider_user"
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!userInfo || userInfo.length === 0) {
          return this.fail(res, '服务商不存在', 404);
        }

        // 查询服务商信息
        const infoList = await this.prisma.$queryRaw`
          SELECT "type", "value"
          FROM "provider"."provider_info"
          WHERE "provider_id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        // 将信息转换为对象格式
        const businessInfo = {};
        if (infoList && infoList.length > 0) {
          infoList.forEach(info => {
            try {
              // 特殊处理不需要 JSON 解析的字段，直接作为字符串处理
              const stringFields = ['bankNumber', 'id_card_number', 'bank', 'branch', 'comaddress', 'comname', 'legal_representative'];
              if (stringFields.includes(info.type)) {
                businessInfo[info.type] = info.value;
                return;
              }

              const parsedValue = JSON.parse(info.value);

              // 特殊处理包含大数字的字段，避免精度丢失
              if (typeof parsedValue === 'object' && parsedValue !== null) {
                // 如果是对象，检查是否包含 bankNumber 字段
                if (parsedValue.bankNumber && typeof parsedValue.bankNumber === 'number') {
                  // 从原始字符串中提取 bankNumber 的原始值
                  const bankNumberMatch = info.value.match(/"bankNumber"\s*:\s*(\d+)/);
                  if (bankNumberMatch) {
                    parsedValue.bankNumber = bankNumberMatch[1];
                  }
                }
              }

              businessInfo[info.type] = parsedValue;
            } catch (e) {
              // 如果 JSON 解析失败，直接使用原始值
              businessInfo[info.type] = info.value;
            }
          });
        }

        // 查询服务商平台费率
        let platformRate = [];
        try {
          // 首先获取所有可用的渠道
          const allChannels = await this.prisma.$queryRaw`
            SELECT "id", "name"
            FROM "base"."channel"
            WHERE "deleted_at" IS NULL
            ORDER BY "id"
          `;

          // 获取该服务商已设置的费率
          const existingRates = await this.prisma.$queryRaw`
            SELECT ppr."channel_id", ppr."rate"
            FROM "provider"."provider_platform_rate" ppr
            WHERE ppr."provider_id" = ${userIdBigInt}
            AND ppr."deleted_at" IS NULL
            ORDER BY ppr."channel_id"
          `;

          // 创建费率映射
          const rateMap = {};
          if (existingRates && existingRates.length > 0) {
            existingRates.forEach(rate => {
              rateMap[rate.channel_id.toString()] = parseFloat(rate.rate);
            });
          }

          // 构建完整的平台费率列表
          if (allChannels && allChannels.length > 0) {
            platformRate = allChannels.map(channel => ({
              id: channel.id.toString(),
              name: channel.name,
              rate: rateMap[channel.id.toString()] || 0 // 如果没有设置费率，默认为0
            }));
          }
        } catch (error) {
          console.error('获取服务商平台费率失败:', error);
          // 查询失败不影响整体返回
        }

        // 获取业务员信息
        let salesmanNames = null;
        if (userInfo[0].salesman_id) {
          try {
            const salesmanIds = userInfo[0].salesman_id.split(',').map(id => id.trim()).filter(id => id);
            const salesmanList = [];

            for (const salesmanId of salesmanIds) {
              const salesmanResult = await this.prisma.$queryRaw`
                SELECT "username"
                FROM "base"."system_user"
                WHERE "id" = ${BigInt(salesmanId)}
              `;

              if (salesmanResult && salesmanResult.length > 0) {
                salesmanList.push({
                  id: salesmanId,
                  name: salesmanResult[0].username
                });
              } else {
                salesmanList.push({
                  id: salesmanId,
                  name: '未知业务员'
                });
              }
            }

            if (salesmanList.length > 0) {
              salesmanNames = salesmanList;
            }
          } catch (error) {
            console.error('获取业务员信息失败:', error);
          }
        }

        // 构建返回数据
        const user = userInfo[0];
        const result = {
          user_id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          status: user.status,
          last_login_ip: user.last_login_ip,
          last_login_time: user.last_login_time ? user.last_login_time.toString() : null,
          created_at: user.created_at.toString(),
          updated_at: user.updated_at.toString(),
          remark: user.remark || '',
          order_count: user.order_count || 0,
          order_amount: user.order_amount ? parseFloat(user.order_amount) : 0.00,
          company_name: user.company_name || '',
          legal_person_name: user.legal_person_name || '',
          detail_address: user.detail_address || '',
          salesman_id: user.salesman_id || '',
          salesman_info: salesmanNames,
          business_info: businessInfo,
          platform_rate: platformRate
        };

        return this.success(res, result);
      } catch (err) {
        console.error('解析用户ID失败:', err.message);
        return this.fail(res, '用户ID无效', 400);
      }
    } catch (err) {
      console.error('获取服务商信息失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 审核服务商信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async audit(req, res) {
    try {
      const { id, status, remark, salesman_id } = req.body;

      // 验证必填参数
      if (!id || status === undefined) {
        return this.fail(res, '服务商ID和审核状态不能为空', 400);
      }

      try {
        // 将用户ID转换为BigInt类型
        const userIdBigInt = BigInt(id);

        // 验证用户是否存在
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!existingUsers || existingUsers.length === 0) {
          return this.fail(res, '服务商不存在', 404);
        }

        // 更新用户状态、审核意见和业务员ID
        const timestamp = Math.floor(Date.now() / 1000);
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "status" = ${parseInt(status)},
              "remark" = ${remark || null},
              "salesman_id" = ${salesman_id || null},
              "updated_at" = ${timestamp}
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        return this.success(res, null, '审核成功');
      } catch (err) {
        console.error('解析用户ID失败:', err.message);
        return this.fail(res, '用户ID无效', 400);
      }
    } catch (err) {
      console.error('审核服务商信息失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 更新财务信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateFinance(req, res) {
    try {
      const { business_info, user_id } = req.body;

      // 验证必填参数
      if (!business_info || !user_id) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证财务信息字段
      const requiredFields = {
        'bank': '开户银行',
        'branch': '开户网点',
        'bankNumber': '银行账号',
        'license': '开户证明'
      };

      for (const [fieldKey, fieldName] of Object.entries(requiredFields)) {
        if (!business_info[fieldKey]) {
          return this.fail(res, `${fieldName}不能为空`, 400);
        }
      }

      // 使用秒级时间戳，避免整数溢出
      const timestamp = Math.floor(Date.now() / 1000);
      const providerId = BigInt(user_id);

      // 检查provider_info表是否使用字符串ID
      const useStringId = await this.isUuidTable('provider_info');
      console.log('provider_info表是否使用字符串ID:', useStringId);

      // 更新每个财务信息字段
      const results = [];
      for (const [type, value] of Object.entries(business_info)) {
        try {
          // 检查记录是否存在
          const existingRecord = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_info"
            WHERE "type" = ${type}
            AND "provider_id" = ${providerId}
            AND "deleted_at" IS NULL
          `;

          if (existingRecord && existingRecord.length > 0) {
            // 如果记录存在，则更新
            const result = await this.prisma.$executeRaw`
              UPDATE "provider"."provider_info"
              SET "value" = ${value},
                  "updated_by" = ${providerId},
                  "updated_at" = ${timestamp}
              WHERE "type" = ${type}
              AND "provider_id" = ${providerId}
              AND "deleted_at" IS NULL
            `;
            results.push({ type, action: 'update', result });
          } else {
            // 如果记录不存在，则创建
            let result;
            if (useStringId) {
              // 使用UUID作为ID
              const id = uuidv4();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${value},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            } else {
              // 使用bigint作为ID
              const id = this.generateId();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_info" (
                  "id", "type", "value", "provider_id", "created_by", "created_at"
                ) VALUES (
                  ${id}, ${type}, ${value},
                  ${providerId}, ${providerId}, ${timestamp}
                )
              `;
            }
            results.push({ type, action: 'create', result });
          }
        } catch (error) {
          console.error(`处理 ${type} 失败:`, error);
          results.push({ type, action: 'error', error: error.message });
        }
      }

      // 获取更新后的所有财务信息
      const updatedInfo = await this.prisma.$queryRaw`
        SELECT "id", "type", "value"
        FROM "provider"."provider_info"
        WHERE "provider_id" = ${providerId}
        AND "type" IN ('bank', 'branch', 'bankNumber', 'license')
        AND "deleted_at" IS NULL
        ORDER BY "type"
      `;

      return this.success(res, {
        results,
        updated_info: updatedInfo
      }, '更新财务信息成功');
    } catch (error) {
      console.error('更新财务信息失败:', error);
      return this.fail(res, error.message, 500);
    }
  }

  /**
   * 添加联系人
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async addContacts(req, res) {
    try {
      const { business_info, user_id } = req.body;

      // 验证必填参数
      if (!business_info || !user_id || !business_info.contacts) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证联系人信息
      const contacts = business_info.contacts;
      if (!Array.isArray(contacts) || contacts.length === 0) {
        return this.fail(res, '联系人信息不能为空', 400);
      }

      // 验证每个联系人必填字段
      for (const contact of contacts) {
        if (!contact.position || !contact.name || !contact.phone) {
          return this.fail(res, '联系人职务、姓名和电话不能为空', 400);
        }
      }

      const timestamp = Math.floor(Date.now() / 1000);
      const providerId = BigInt(user_id);

      // 检查provider_info表是否使用字符串ID
      const useStringId = await this.isUuidTable('provider_info');
      console.log('provider_info表是否使用字符串ID:', useStringId);

      // 获取现有联系人信息
      const existingContacts = await this.prisma.$queryRaw`
        SELECT "id", "value"
        FROM "provider"."provider_info"
        WHERE "type" = 'contacts'
        AND "provider_id" = ${providerId}
        AND "deleted_at" IS NULL
      `;

      let contactsArray = [];
      if (existingContacts && existingContacts.length > 0) {
        try {
          contactsArray = JSON.parse(existingContacts[0].value);
        } catch (e) {
          contactsArray = [];
        }
      }

      // 合并新旧联系人
      const updatedContacts = [...contactsArray, ...contacts];

      // 更新或插入联系人信息
      if (existingContacts && existingContacts.length > 0) {
        // 更新现有记录
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_info"
          SET "value" = ${JSON.stringify(updatedContacts)},
              "updated_by" = ${providerId},
              "updated_at" = ${timestamp}
          WHERE "type" = 'contacts'
          AND "provider_id" = ${providerId}
          AND "deleted_at" IS NULL
        `;
      } else {
        // 创建新记录
        if (useStringId) {
          // 使用UUID作为ID
          const id = uuidv4();
          await this.prisma.$executeRaw`
            INSERT INTO "provider"."provider_info" (
              "id", "type", "value", "provider_id", "created_by", "created_at"
            ) VALUES (
              ${id}, 'contacts', ${JSON.stringify(updatedContacts)},
              ${providerId}, ${providerId}, ${timestamp}
            )
          `;
        } else {
          // 使用bigint作为ID
          const id = this.generateId();
          await this.prisma.$executeRaw`
            INSERT INTO "provider"."provider_info" (
              "id", "type", "value", "provider_id", "created_by", "created_at"
            ) VALUES (
              ${id}, 'contacts', ${JSON.stringify(updatedContacts)},
              ${providerId}, ${providerId}, ${timestamp}
            )
          `;
        }
      }

      // 获取更新后的联系人信息
      const updatedInfo = await this.prisma.$queryRaw`
        SELECT "id", "type", "value"
        FROM "provider"."provider_info"
        WHERE "type" = 'contacts'
        AND "provider_id" = ${providerId}
        AND "deleted_at" IS NULL
      `;

      return this.success(res, {
        contacts: JSON.parse(updatedInfo[0].value)
      }, '添加联系人成功');
    } catch (error) {
      console.error('添加联系人失败:', error);
      return this.fail(res, error.message, 500);
    }
  }

  /**
   * 更新联系人
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateContacts(req, res) {
    try {
      const { business_info, user_id } = req.body;

      // 验证必填参数
      if (!business_info || !user_id || !business_info.contacts) {
        return this.fail(res, '参数不完整', 400);
      }

      // 验证联系人信息
      const contacts = business_info.contacts;
      if (!Array.isArray(contacts) || contacts.length === 0) {
        return this.fail(res, '联系人信息不能为空', 400);
      }

      // 验证每个联系人必填字段
      for (const contact of contacts) {
        if (!contact.position || !contact.name || !contact.phone) {
          return this.fail(res, '联系人职务、姓名和电话不能为空', 400);
        }
      }

      const timestamp = Math.floor(Date.now() / 1000);
      const providerId = BigInt(user_id);

      // 检查provider_info表是否使用字符串ID
      const useStringId = await this.isUuidTable('provider_info');
      console.log('provider_info表是否使用字符串ID:', useStringId);

      // 更新联系人信息
      const existingContacts = await this.prisma.$queryRaw`
        SELECT "id", "value"
        FROM "provider"."provider_info"
        WHERE "type" = 'contacts'
        AND "provider_id" = ${providerId}
        AND "deleted_at" IS NULL
      `;

      if (existingContacts && existingContacts.length > 0) {
        // 更新现有记录
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_info"
          SET "value" = ${JSON.stringify(contacts)},
              "updated_by" = ${providerId},
              "updated_at" = ${timestamp}
          WHERE "type" = 'contacts'
          AND "provider_id" = ${providerId}
          AND "deleted_at" IS NULL
        `;
      } else {
        // 创建新记录
        if (useStringId) {
          // 使用UUID作为ID
          const id = uuidv4();
          await this.prisma.$executeRaw`
            INSERT INTO "provider"."provider_info" (
              "id", "type", "value", "provider_id", "created_by", "created_at"
            ) VALUES (
              ${id}, 'contacts', ${JSON.stringify(contacts)},
              ${providerId}, ${providerId}, ${timestamp}
            )
          `;
        } else {
          // 使用bigint作为ID
          const id = this.generateId();
          await this.prisma.$executeRaw`
            INSERT INTO "provider"."provider_info" (
              "id", "type", "value", "provider_id", "created_by", "created_at"
            ) VALUES (
              ${id}, 'contacts', ${JSON.stringify(contacts)},
              ${providerId}, ${providerId}, ${timestamp}
            )
          `;
        }
      }

      // 获取更新后的联系人信息
      const updatedInfo = await this.prisma.$queryRaw`
        SELECT "id", "type", "value"
        FROM "provider"."provider_info"
        WHERE "type" = 'contacts'
        AND "provider_id" = ${providerId}
        AND "deleted_at" IS NULL
      `;

      return this.success(res, {
        contacts: JSON.parse(updatedInfo[0].value)
      }, '更新联系人成功');
    } catch (error) {
      console.error('更新联系人失败:', error);
      return this.fail(res, error.message, 500);
    }
  }

  /**
   * 更新服务商业务员
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateSalesman(req, res) {
    try {
      const { id, salesman_id } = req.body;

      // 验证必填参数
      if (!id) {
        return this.fail(res, '服务商ID不能为空', 400);
      }

      try {
        // 将服务商ID转换为BigInt类型
        const providerIdBigInt = BigInt(id);

        // 验证服务商是否存在
        const existingProviders = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE "id" = ${providerIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!existingProviders || existingProviders.length === 0) {
          return this.fail(res, '服务商不存在', 404);
        }

        // 更新业务员ID
        const timestamp = Math.floor(Date.now() / 1000);
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "salesman_id" = ${salesman_id || null},
              "updated_at" = ${timestamp}
          WHERE "id" = ${providerIdBigInt}
          AND "deleted_at" IS NULL
        `;

        return this.success(res, null, '更新业务员成功');
      } catch (err) {
        console.error('解析服务商ID失败:', err.message);
        return this.fail(res, '服务商ID无效', 400);
      }
    } catch (err) {
      console.error('更新业务员失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 更新服务商平台费率
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updatePlatformRate(req, res) {
    try {
      const { provider_id, platform_rate } = req.body;

      // 验证必填参数
      if (!provider_id || !platform_rate || !Array.isArray(platform_rate)) {
        return this.fail(res, '服务商ID和平台费率信息不能为空', 400);
      }

      try {
        // 将服务商ID转换为BigInt类型
        const providerIdBigInt = BigInt(provider_id);

        // 验证服务商是否存在
        const existingProviders = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user"
          WHERE "id" = ${providerIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!existingProviders || existingProviders.length === 0) {
          return this.fail(res, '服务商不存在', 404);
        }

        const timestamp = Math.floor(Date.now() / 1000);
        const results = [];

        // 检查provider_platform_rate表是否使用字符串ID
        const useStringId = await this.isUuidTable('provider_platform_rate');
        console.log('provider_platform_rate表是否使用字符串ID:', useStringId);

        // 处理每个平台费率
        for (const rateInfo of platform_rate) {
          const { id: channelId, rate } = rateInfo;

          if (!channelId || rate === undefined) {
            continue; // 跳过无效数据
          }

          const channelIdBigInt = BigInt(channelId);
          const rateDecimal = parseFloat(rate);

          // 检查该服务商该渠道是否已有费率记录
          const existingRates = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_platform_rate"
            WHERE "provider_id" = ${providerIdBigInt}
            AND "channel_id" = ${channelIdBigInt}
            AND "deleted_at" IS NULL
          `;

          if (existingRates && existingRates.length > 0) {
            // 如果记录存在，则更新
            const result = await this.prisma.$executeRaw`
              UPDATE "provider"."provider_platform_rate"
              SET "rate" = ${rateDecimal},
                  "updated_at" = ${timestamp},
                  "updated_by" = ${providerIdBigInt}
              WHERE "provider_id" = ${providerIdBigInt}
              AND "channel_id" = ${channelIdBigInt}
              AND "deleted_at" IS NULL
            `;
            results.push({ channelId, action: 'update', result });
          } else {
            // 如果记录不存在，则创建
            let result;
            if (useStringId) {
              // 使用UUID作为ID
              const id = uuidv4();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_platform_rate" (
                  "id", "provider_id", "channel_id", "rate",
                  "created_at", "updated_at", "created_by"
                ) VALUES (
                  ${id}, ${providerIdBigInt}, ${channelIdBigInt}, ${rateDecimal},
                  ${timestamp}, ${timestamp}, ${providerIdBigInt}
                )
              `;
            } else {
              // 使用bigint作为ID
              const id = this.generateId();
              result = await this.prisma.$executeRaw`
                INSERT INTO "provider"."provider_platform_rate" (
                  "id", "provider_id", "channel_id", "rate",
                  "created_at", "updated_at", "created_by"
                ) VALUES (
                  ${id}, ${providerIdBigInt}, ${channelIdBigInt}, ${rateDecimal},
                  ${timestamp}, ${timestamp}, ${providerIdBigInt}
                )
              `;
            }
            results.push({ channelId, action: 'create', result });
          }
        }

        // 获取更新后的费率信息，需要关联渠道表获取名称
        const updatedRates = await this.prisma.$queryRaw`
          SELECT
            ppr."channel_id",
            c."name" as channel_name,
            ppr."rate",
            ppr."updated_at"
          FROM "provider"."provider_platform_rate" ppr
          LEFT JOIN "base"."channel" c ON ppr."channel_id" = c."id"
          WHERE ppr."provider_id" = ${providerIdBigInt}
          AND ppr."deleted_at" IS NULL
          ORDER BY ppr."channel_id"
        `;

        return this.success(res, {
          provider_id: provider_id,
          platform_rate: updatedRates.map(rate => ({
            id: rate.channel_id.toString(),
            name: rate.channel_name || '未知渠道',
            rate: parseFloat(rate.rate)
          })),
          results: results
        }, '更新平台费率成功');
      } catch (err) {
        console.error('处理服务商平台费率失败:', err.message);
        return this.fail(res, '服务商ID或费率数据无效: ' + err.message, 400);
      }
    } catch (err) {
      console.error('更新平台费率失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 获取服务商对指定渠道的费率
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProviderChannelRate(req, res) {
    try {
      const { providerId, channelId } = req.params;

      // 验证必填参数
      if (!providerId || !channelId) {
        return this.fail(res, '服务商ID和渠道ID不能为空', 400);
      }

      try {
        console.log('查询费率参数:', {
          providerId,
          channelId,
          providerIdType: typeof providerId,
          channelIdType: typeof channelId
        });

        // 直接使用字符串查询，避免BigInt转换问题
        const rateInfo = await this.prisma.$queryRaw`
          SELECT
            ppr."provider_id",
            ppr."channel_id",
            c."name" as channel_name,
            ppr."rate",
            ppr."status",
            ppr."updated_at"
          FROM "provider"."provider_platform_rate" ppr
          LEFT JOIN "base"."channel" c ON ppr."channel_id" = c."id"
          WHERE ppr."provider_id"::text = ${providerId}
          AND ppr."channel_id"::text = ${channelId}
          AND ppr."deleted_at" IS NULL
          AND ppr."status" = 1
          LIMIT 1
        `;

        console.log('查询费率结果:', rateInfo);

        if (!rateInfo || rateInfo.length === 0) {
          console.log('未找到费率信息，尝试查询所有相关记录...');

          // 调试：查询该服务商的所有费率记录
          const allRates = await this.prisma.$queryRaw`
            SELECT
              ppr."provider_id",
              ppr."channel_id",
              ppr."rate",
              ppr."status",
              ppr."deleted_at"
            FROM "provider"."provider_platform_rate" ppr
            WHERE ppr."provider_id"::text = ${providerId}
          `;
          console.log('该服务商的所有费率记录:', allRates);

          return this.fail(res, '未找到该服务商对应渠道的费率信息', 404);
        }

        const rate = rateInfo[0];

        return this.success(res, {
          provider_id: rate.provider_id.toString(),
          channel_id: rate.channel_id.toString(),
          channel_name: rate.channel_name || '未知渠道',
          rate: parseFloat(rate.rate),
          status: rate.status,
          updated_at: rate.updated_at
        }, '获取费率成功');

      } catch (err) {
        console.error('处理费率查询失败:', err.message);
        return this.fail(res, '服务商ID或渠道ID无效: ' + err.message, 400);
      }
    } catch (err) {
      console.error('获取费率失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }
}

module.exports = ProviderInfoController;