const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');
const redisUtil = require('../../../../../core/utils/RedisUtil');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'julingcloud-secret-key';
const JWT_EXPIRE = process.env.JWT_EXPIRE || '24h'; // 默认24小时过期
// 是否启用单点登录
const ENABLE_SINGLE_LOGIN = process.env.ENABLE_SINGLE_LOGIN !== 'false';

class UserManagementService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  // 支持对象参数，确保 page/pageSize 为数字，避免 skip 为 NaN
  async list(params = {}) {
    let { page = 1, pageSize = 10, ...filters } = params;

    // 确保分页参数是数字类型
    page = Number(page) || 1;
    pageSize = Number(pageSize) || 10;

    const skip = (page - 1) * pageSize;

    // 只保留数据库字段，分页参数不进where
    const where = { deleted_at: null };
    const allowKeys = [
      'id', 'user_type', 'dept_id', 'role_id', 'status', 'created_by', 'updated_by'
    ];
    
    // 精确匹配字段
    for (const key of allowKeys) {
      if (filters[key] !== undefined && filters[key] !== '') {
        where[key] = filters[key];
      }
    }
    
    // 模糊查询字段
    const fuzzyKeys = ['username', 'nickname', 'email', 'phone'];
    for (const key of fuzzyKeys) {
      if (filters[key] !== undefined && filters[key] !== '') {
        where[key] = { contains: filters[key] };
      }
    }
    
    // 关键字模糊搜索（可同时匹配用户名、昵称、邮箱、电话）
    if (filters.keyword && filters.keyword.trim() !== '') {
      const keyword = filters.keyword.trim();
      where.OR = [
        { username: { contains: keyword } },
        { nickname: { contains: keyword } },
        { email: { contains: keyword } },
        { phone: { contains: keyword } }
      ];
    }

    const [total, users] = await Promise.all([
      this.prisma.baseSystemUser.count({ where }),
      this.prisma.baseSystemUser.findMany({
        where,
        skip,
        take: pageSize,
        select: {
          id: true,
          username: true,
          nickname: true,
          email: true,
          status: true,
          phone: true,
          dept_id: true,
          role_id: true,
          created_at: true,
          updated_at: true,
          remark: true
        }
      })
    ]);

    // 关联查询部门和角色信息
    const deptIds = [...new Set(users.filter(u => u.dept_id).map(u => u.dept_id))];
    const roleIds = [...new Set(users.filter(u => u.role_id).map(u => u.role_id))];
    
    let depts = [];
    let roles = [];
    
    if (deptIds.length > 0) {
      depts = await this.prisma.baseSystemDept.findMany({
        where: {
          id: { in: deptIds },
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        }
      });
    }
    
    if (roleIds.length > 0) {
      roles = await this.prisma.baseSystemRole.findMany({
        where: {
          id: { in: roleIds },
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        }
      });
    }
    
    // 将部门和角色信息合并到用户数据中
    const deptMap = new Map(depts.map(d => [d.id.toString(), d.name]));
    const roleMap = new Map(roles.map(r => [r.id.toString(), r.name]));
    
    const enhancedUsers = users.map(user => {
      return {
        ...user,
        dept_name: user.dept_id ? deptMap.get(user.dept_id.toString()) || '未知部门' : null,
        role_name: user.role_id ? roleMap.get(user.role_id.toString()) || '未知角色' : null
      };
    });

    return {
      list: enhancedUsers,
      total,
      page,
      pageSize
    };
  }

  async create(userData) {
    // 检查用户名是否已存在
    const existingUser = await this.prisma.baseSystemUser.findFirst({
      where: { 
        username: userData.username,
        deleted_at: null
      }
    });

    if (existingUser) {
      throw new Error('用户名已存在');
    }

    // 检查邮箱是否已被使用
    if (userData.email) {
      const existingEmail = await this.prisma.baseSystemUser.findFirst({
        where: { 
          email: userData.email,
          deleted_at: null
        }
      });

      if (existingEmail) {
        throw new Error('邮箱已被使用');
      }
    }

    // 检查手机号是否已被使用
    if (userData.phone) {
      const existingPhone = await this.prisma.baseSystemUser.findFirst({
        where: { 
          phone: userData.phone,
          deleted_at: null
        }
      });

      if (existingPhone) {
        throw new Error('手机号已被使用');
      }
    }

    // 获取当前时间戳
    const currentTime = BigInt(Date.now());
    
    // 将驼峰命名转换为下划线命名
    const formattedData = {
      username: userData.username,
      password: bcrypt.hashSync(userData.password, 10),
      nickname: userData.nickname,
      email: userData.email,
      phone: userData.phone,
      dept_id: userData.deptId ? BigInt(userData.deptId) : null,
      role_id: userData.roleId ? BigInt(userData.roleId) : null,
      status: userData.status,
      remark: userData.remark,
      id: generateSnowflakeId(),
      created_at: currentTime,
      updated_at: currentTime,
      created_by: userData.created_by ? BigInt(userData.created_by) : null
    };

    // 创建用户
    const user = await this.prisma.baseSystemUser.create({
      data: formattedData,
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        phone: true,
        dept_id: true,
        role_id: true,
        status: true,
        created_at: true
      }
    });
    
    return user;
  }

  async update(id, userData) {
    const existingUser = await this.prisma.baseSystemUser.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingUser) {
      throw new Error('用户不存在');
    }

    // 检查用户名是否已被其他用户使用
    if (userData.username && userData.username !== existingUser.username) {
      const existingUsername = await this.prisma.baseSystemUser.findFirst({
        where: {
          username: userData.username,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (existingUsername) {
        throw new Error('用户名已被其他用户使用');
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (userData.email && userData.email !== existingUser.email) {
      const existingEmail = await this.prisma.baseSystemUser.findFirst({
        where: {
          email: userData.email,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (existingEmail) {
        throw new Error('邮箱已被其他用户使用');
      }
    }

    // 检查手机号是否已被其他用户使用
    if (userData.phone && userData.phone !== existingUser.phone) {
      const existingPhone = await this.prisma.baseSystemUser.findFirst({
        where: {
          phone: userData.phone,
          id: { not: BigInt(id) },
          deleted_at: null
        }
      });

      if (existingPhone) {
        throw new Error('手机号已被其他用户使用');
      }
    }

    // 获取当前时间戳作为更新时间
    const currentTime = BigInt(Date.now());

    // 将驼峰命名转换为下划线命名
    const formattedData = {
      updated_at: currentTime
    };

    // 处理可选字段的转换
    if (userData.username !== undefined) formattedData.username = userData.username;
    if (userData.nickname !== undefined) formattedData.nickname = userData.nickname;
    if (userData.email !== undefined) formattedData.email = userData.email;
    if (userData.phone !== undefined) formattedData.phone = userData.phone;
    if (userData.dept_id !== undefined) formattedData.dept_id = BigInt(userData.dept_id);
    if (userData.role_id !== undefined) formattedData.role_id = BigInt(userData.role_id);
    if (userData.status !== undefined) formattedData.status = userData.status;
    if (userData.remark !== undefined) formattedData.remark = userData.remark;
    if (userData.password) formattedData.password = bcrypt.hashSync(userData.password, 10);
    if (userData.updated_by) formattedData.updated_by = BigInt(userData.updated_by);

    return this.prisma.baseSystemUser.update({
      where: { id: BigInt(id) },
      data: formattedData,
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        phone: true,
        dept_id: true,
        role_id: true
      }
    });
  }

  async delete(id, updatedBy) {
    const existingUser = await this.prisma.baseSystemUser.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingUser) {
      throw new Error('用户不存在');
    }
    
    // 检查是否为超级管理员用户
    if (existingUser.username === 'admin') {
      throw new Error('超级管理员用户不允许删除');
    }

    // 获取当前时间戳
    const currentTime = BigInt(Date.now());

    // 软删除：更新deleted_at字段而不是物理删除记录
    await this.prisma.baseSystemUser.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: currentTime,
        updated_at: currentTime,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });
  }

  async getById(id) {
    const user = await this.prisma.baseSystemUser.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        phone: true,
        dept_id: true,
        role_id: true
      }
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    return user;
  }

  /**
   * 用户登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @param {string} ip - 客户端IP地址
   * @returns {Object} 登录结果，包含token和用户信息
   */
  async login(username, password, ip = '0.0.0.0') {
    
    try {
      // 查找用户
      const user = await this.prisma.baseSystemUser.findUnique({
        where: { username },
        select: {
          id: true,
          username: true,
          password: true,
          email: true,
          phone: true,
          nickname: true,
          role_id: true,
          dept_id: true,
          status: true
        }
      });

      // 验证用户名和密码
      if (!user || !bcrypt.compareSync(password, user.password)) {
        console.error(`用户 ${username} 登录失败: 用户名或密码错误`);
        throw new Error('用户名或密码错误');
      }

      // 检查用户状态
      if (user.status === 0) {
        console.error(`用户 ${username} 登录失败: 账户已被停用`);
        throw new Error('账户已被停用，请联系管理员');
      }
      
      // 获取角色和部门名称
      let roleName = null;
      let deptName = null;
      
      // 如果用户有角色ID，查询角色名称
      if (user.role_id) {
        try {
          const role = await this.prisma.baseSystemRole.findUnique({
            where: { id: user.role_id },
            select: { name: true }
          });
          roleName = role ? role.name : null;
        } catch (error) {
          console.error(`获取角色信息失败: ${error.message}`);
        }
      }
      
      // 如果用户有部门ID，查询部门名称
      if (user.dept_id) {
        try {
          const dept = await this.prisma.baseSystemDept.findUnique({
            where: { id: user.dept_id },
            select: { name: true }
          });
          deptName = dept ? dept.name : null;
        } catch (error) {
          console.error(`获取部门信息失败: ${error.message}`);
        }
      }
      


      // 生成JWT令牌
      const payload = {
        id: user.id.toString(), // 将BigInt转换为字符串
        username: user.username,
        nickname: user.nickname,
        role: user.role_id ? user.role_id.toString() : null, // 将BigInt转换为字符串
        dept_id: user.dept_id ? user.dept_id.toString() : null // 将BigInt转换为字符串
      };
      
      // 计算过期时间戳
      const now = Date.now();
      const expires = this.getExpirationTime(JWT_EXPIRE);
      
      // 生成JWT令牌
      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRE });
      
      // 计算过期秒数
      const expiresInSeconds = Math.floor((expires - now) / 1000);
      
      // 单点登录：将令牌保存到Redis，并使之前的令牌失效
      await redisUtil.saveUserToken(user.id.toString(), token, expiresInSeconds, ENABLE_SINGLE_LOGIN);
      
      // 更新用户的登录信息
      await this.updateUserLoginInfo(user.id, ip, token, now);
      
      // 返回登录成功结果
      return {
        token,
        expiresAt: expires,
        user: {
          id: user.id.toString(), // 将BigInt转换为字符串
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          role_id: user.role_id ? user.role_id.toString() : null,
          dept_id: user.dept_id ? user.dept_id.toString() : null,
          role_name: roleName,
          dept_name: deptName
        }
      };
    } catch (error) {
      console.error(`用户 ${username} 登录失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 使用手机号登录
   * @param {string} phone - 手机号
   * @param {string} ip - 客户端IP地址
   * @returns {Object} 登录结果，包含token和用户信息
   */
  async loginByPhone(phone, ip = '0.0.0.0') {
    try {
      
      // 获取JWT配置
      const JWT_SECRET = process.env.JWT_SECRET || 'julingcloud-secret-key';
      const JWT_EXPIRE = process.env.JWT_EXPIRE || '24h';
      const ENABLE_SINGLE_LOGIN = process.env.ENABLE_SINGLE_LOGIN === 'true';
      
      // 查询用户
      const user = await this.prisma.baseSystemUser.findFirst({
        where: {
          phone: phone,
          status: 1, // 只查询启用状态的用户
          deleted_at: null // 只查询未删除的用户
        }
      });
      
      if (!user) {
        throw new Error('该手机号未注册');
      }
      
      // 获取用户角色和部门信息
      let roleName = '无角色';
      let deptName = '无部门';
      
      if (user.role_id) {
        try {
          const role = await this.prisma.baseSystemRole.findUnique({
            where: { id: user.role_id }
          });
          if (role) {
            roleName = role.name;
          }
        } catch (roleErr) {
          console.error(`获取角色信息失败: ${roleErr.message}`);
        }
      }
      
      if (user.dept_id) {
        try {
          const dept = await this.prisma.baseSystemDepartment.findUnique({
            where: { id: user.dept_id }
          });
          if (dept) {
            deptName = dept.name;
          }
        } catch (deptErr) {
          console.error(`获取部门信息失败: ${deptErr.message}`);
        }
      }
      


      // 生成JWT令牌
      const payload = {
        id: user.id.toString(), // 将BigInt转换为字符串
        username: user.username,
        nickname: user.nickname,
        role: user.role_id ? user.role_id.toString() : null, // 将BigInt转换为字符串
        dept_id: user.dept_id ? user.dept_id.toString() : null // 将BigInt转换为字符串
      };
      
      // 计算过期时间戳
      const now = Date.now();
      const expires = this.getExpirationTime(JWT_EXPIRE);
      
      // 生成JWT令牌
      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRE });
      
      // 计算过期秒数
      const expiresInSeconds = Math.floor((expires - now) / 1000);
      
      // 单点登录：将令牌保存到Redis，并使之前的令牌失效
      await redisUtil.saveUserToken(user.id.toString(), token, expiresInSeconds, ENABLE_SINGLE_LOGIN);
      
      // 更新用户的登录信息
      await this.updateUserLoginInfo(user.id, ip, token, now);
      
      // 返回登录成功结果
      return {
        token,
        expiresAt: expires,
        user: {
          id: user.id.toString(), // 将BigInt转换为字符串
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          role_id: user.role_id ? user.role_id.toString() : null,
          dept_id: user.dept_id ? user.dept_id.toString() : null,
          role_name: roleName,
          dept_name: deptName
        }
      };
    } catch (error) {
      console.error(`手机号 ${phone} 登录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户的登录信息
   * @param {BigInt} userId - 用户ID
   * @param {string} ip - 登录IP
   * @param {string} token - JWT令牌
   * @param {number} loginTime - 登录时间戳
   * @returns {Promise<Object>} 更新的用户信息
   */
  async updateUserLoginInfo(userId, ip, token, loginTime) {
    return this.prisma.baseSystemUser.update({
      where: { id: userId },
      data: {
        login_ip: ip,
        login_time: BigInt(loginTime), // 确保时间戳是BigInt类型
        login_token: token,
        updated_at: BigInt(loginTime) // 确保时间戳是BigInt类型
      }
    });
  }
  
  /**
   * 获取过期时间戳
   * @param {string} expireString - 过期时间字符串，如 '24h', '7d' 等
   * @returns {number} 过期时间的时间戳
   */
  getExpirationTime(expireString) {
    const now = Date.now();
    const unit = expireString.slice(-1);
    const value = parseInt(expireString.slice(0, -1));

    switch(unit) {
      case 'h': // 小时
        return now + (value * 60 * 60 * 1000);
      case 'd': // 天
        return now + (value * 24 * 60 * 60 * 1000);
      case 'm': // 分钟
        return now + (value * 60 * 1000);
      case 's': // 秒
        return now + (value * 1000);
      default:
        return now + (24 * 60 * 60 * 1000); // 默认24小时
    }
  }

  /**
   * 获取用户选项列表（用于下拉选择）
   * @param {Object} params 查询参数
   * @param {string} params.keyword 搜索关键词
   * @param {string} params.type 用户类型筛选
   * @returns {Promise<Array>} 用户选项列表
   */
  async getUserOptions(params = {}) {
    try {
      const { keyword = '', type = '' } = params;

      // 构建查询条件
      const where = {
        deleted_at: null,
        status: 1 // 只查询启用状态的用户
      };

      if (keyword) {
        where.OR = [
          { username: { contains: keyword } },
          { nickname: { contains: keyword } }
        ];
      }

      // 查询用户数据
      const users = await this.prisma.baseSystemUser.findMany({
        where,
        select: {
          id: true,
          username: true,
          nickname: true,
          email: true,
          phone: true,
          dept_id: true,
          role_id: true
        },
        orderBy: {
          created_at: 'desc'
        },
        take: 100 // 限制返回数量
      });

      // 处理BigInt类型并格式化数据
      return users.map(user => ({
        id: user.id.toString(),
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        dept_id: user.dept_id ? user.dept_id.toString() : null,
        role_id: user.role_id ? user.role_id.toString() : null
      }));
    } catch (error) {
      throw new Error(`获取用户选项失败: ${error.message}`);
    }
  }
}

module.exports = UserManagementService;
