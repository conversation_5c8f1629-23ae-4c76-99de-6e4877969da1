const BaseController = require('../../../../../core/controllers/BaseController');
const RoleService = require('../services/RoleManagementService');
const SystemRoleDto = require('../dto/SystemRoleDto');
const RoleMenuService = require('../services/RoleMenuService');
const RoleDeptService = require('../services/RoleDeptService'); // 新增服务

/**
 * 角色管理控制器
 * 处理角色相关的HTTP请求
 */
class RoleManagementController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.roleService = new RoleService(prisma);
  }

  /**
   * 获取角色列表
   * @route GET /role
   * @group 角色管理
   * @param {string} page.query.optional - 页码
   * @param {string} pageSize.query.optional - 每页条数
   * @param {string} name.query.optional - 角色名称
   * @param {string} status.query.optional - 状态(0-正常，1-停用)
   * @returns {object} 200 - 角色列表
   */
  async list(req, res) {
    try {
      console.log('获取角色列表，请求参数:', JSON.stringify(req.query));
      
      // 验证查询参数（包含分页参数）
      const { error, value } = SystemRoleDto.validateQuery(req.query);
      if (error) {
        console.error('参数验证错误:', error.details[0].message);
        return this.fail(res, error.details[0].message, 200);
      }
      
      // 提取分页参数和过滤条件
      const { page, pageSize, name, status } = value;

      // 组装查询条件
      const filters = {};
      if (name) {
        // 对角色名称进行URL解码，处理特殊字符如"+"
        try {
          filters.name = decodeURIComponent(name);
        } catch (e) {
          // 如果解码失败，使用原始值
          filters.name = name;
        }
      }
      console.log('解码后的角色名称:', filters.name);
      if (status !== undefined && status !== '') filters.status = status;
      
      console.log('过滤条件:', JSON.stringify(filters));
      
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      
      // 调用服务获取角色列表
      const result = await this.roleService.list({ ...filters, ...pagination });
      
      // 获取关联的部门和菜单信息
      const roleMenuService = new RoleMenuService();
      const roleDeptService = new RoleDeptService();
      
      // 为每个角色添加关联的部门和菜单信息
      const itemsWithRelations = await Promise.all(result.items.map(async (role) => {
        const roleId = role.id.toString();
        
        // 获取角色关联的菜单ID
        const menuIds = await roleMenuService.getMenuIdsByRole(roleId);
        
        // 获取角色关联的部门ID
        const deptIds = await roleDeptService.getDeptIdsByRole(roleId);
        
        // 获取部门名称
        let deptNames = [];
        if (deptIds && deptIds.length > 0) {
          try {
            // 查询部门信息
            const depts = await this.prisma.baseSystemDept.findMany({
              where: {
                id: { in: deptIds.map(id => BigInt(id)) },
                deleted_at: null
              },
              select: {
                id: true,
                name: true
              }
            });
            
            // 构建部门ID和名称的映射
            deptNames = depts.map(dept => ({
              id: dept.id.toString(),
              name: dept.name
            }));
            
            console.log(`获取到角色(${roleId})关联的${deptNames.length}个部门信息`);
          } catch (error) {
            console.error(`获取角色(${roleId})关联部门信息失败:`, error.message);
          }
        }
        
        // 转换为前端需要的格式
        return {
          id: roleId,
          name: role.name,
          data_scope: role.data_scope,
          status: role.status,
          sort: role.sort,
          isSystem: role.is_system,
          remark: role.remark,
          createdAt: role.created_at.toString(),
          updatedAt: role.updated_at.toString(),
          // 添加关联的菜单和部门信息
          menuIds: menuIds,
          depts: deptNames
        };
      }));
      
      console.log(`获取角色列表成功，共 ${result.items.length} 条记录`);

      // 使用标准列表响应格式，与部门列表保持一致
      return this.successList(
        res,
        itemsWithRelations,
        result.pagination.total,
        page,
        pageSize,
        '获取角色列表成功'
      );
    } catch (error) {
      console.error('获取角色列表失败:', error.message);
      return this.fail(res, error.message);
    }
  }

  /**
   * 获取角色详情
   * @route GET /role/:id
   * @group 角色管理
   * @param {string} id.path.required - 角色ID
   * @returns {object} 200 - 角色详情
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return this.fail(res, '缺少角色ID参数');
      }

      // 调用服务获取角色详情
      const result = await this.roleService.getById(id);
      
      // 处理服务层返回的结果
      if (!result.success) {
        return this.fail(res, result.message, result.code);
      }
      
      // 返回成功响应
      return this.success(res, result.data, '获取角色详情成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }

  /**
   * 获取角色已分配菜单（回显用）
   * @route GET /role/:id/menus
   * @group 角色管理
   * @param {string} id.path.required - 角色ID
   * @returns {object} 200 - 菜单ID数组
   */
  async getRoleMenus(req, res) {
    try {
      const { id } = req.params;
      if (!id) return this.fail(res, '缺少角色ID参数');
      const roleMenuService = new RoleMenuService();
      const menuIds = await roleMenuService.getMenuIdsByRole(id);
      return this.success(res, menuIds, '获取角色菜单成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }

  /**
   * 获取角色已分配部门（回显用）
   * @route GET /role/:id/depts
   * @group 角色管理
   * @param {string} id.path.required - 角色ID
   * @returns {object} 200 - 部门ID数组
   */
  async getRoleDepts(req, res) {
    try {
      const { id } = req.params;
      if (!id) return this.fail(res, '缺少角色ID参数');
      const roleDeptService = new RoleDeptService();
      const deptIds = await roleDeptService.getDeptIdsByRole(id);
      return this.success(res, deptIds, '获取角色部门成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }

  /**
   * 创建角色（含菜单权限分配）
   * @route POST /role
   * @group 角色管理
   * @param {string} name.body.required - 角色名称
   * @param {string} code.body.required - 角色编码
   * @param {string[]} menu_ids.body.required - 菜单ID数组
   * @param {string[]} dept_ids.body.required - 部门ID数组
   * @returns {object} 201 - 创建结果
   */
  async create(req, res) {
    try {
      // DTO 校验前，打印原始请求体
      console.log('[创建角色] 原始 req.body:', req.body);
      // DTO 校验，menu_ids 也校验
      const { error, value } = SystemRoleDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message);
      }
      // 校验后打印 value
      console.log('[创建角色] DTO 校验后 value:', value);
      // 拆分 menu_ids 和 dept_ids
      const { menu_ids = [], dept_ids = [], ...roleData } = value;
      // 调试输出
      console.log('[创建角色] Controller menu_ids:', menu_ids, '类型:', Array.isArray(menu_ids) ? menu_ids.map(x => typeof x) : typeof menu_ids);
      console.log('[创建角色] Controller dept_ids:', dept_ids, '类型:', Array.isArray(dept_ids) ? dept_ids.map(x => typeof x) : typeof dept_ids);
      // 添加创建者ID
      roleData.created_by = req.user?.id;
      // 角色名唯一性校验
      const exist = await this.roleService.roleModel.findFirst({
        where: { name: roleData.name, deleted_at: null }
      });
      if (exist) {
        return this.fail(res, `角色名称"${roleData.name}"已存在`, 200);
      }
      // 事务创建角色及关联（菜单+部门）
      const roleId = await this.roleService.createRoleWithMenus(roleData, menu_ids, dept_ids);
      return this.success(res, { id: roleId }, '创建角色成功', 200);
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 更新角色（含菜单权限分配）
   * @route PUT /role/:id
   * @group 角色管理
   * @param {string} id.path.required - 角色ID
   * @param {string} name.body.optional - 角色名称
   * @param {string} code.body.optional - 角色编码
   * @param {string[]} menu_ids.body.optional - 菜单ID数组
   * @param {string[]} dept_ids.body.optional - 部门ID数组
   * @returns {object} 200 - 更新结果
   */
  async update(req, res) {
    try {
      // 合并 id，DTO 校验
      const data = { ...req.body, id: req.params.id };
      const { error, value } = SystemRoleDto.validateUpdate(data);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      // 拆分 menu_ids 和 dept_ids
      const { menu_ids = [], dept_ids = [], ...roleData } = value;
      // 调试输出
      console.log('[更新角色] Controller menu_ids:', menu_ids, '类型:', Array.isArray(menu_ids) ? menu_ids.map(x => typeof x) : typeof menu_ids);
      console.log('[更新角色] Controller dept_ids:', dept_ids, '类型:', Array.isArray(dept_ids) ? dept_ids.map(x => typeof x) : typeof dept_ids);
      // 添加更新人
      roleData.updated_by = req.user?.id;
      // 角色名唯一性校验（排除自己）
      if (roleData.name) {
        const exist = await this.roleService.roleModel.findFirst({
          where: {
            name: roleData.name,
            deleted_at: null,
            id: { not: BigInt(value.id) }
          }
        });
        if (exist) {
          return this.fail(res, `角色名称"${roleData.name}"已存在`, 200);
        }
      }
      await this.roleService.updateRoleWithMenus(value.id, roleData, menu_ids, dept_ids);
      return this.success(res, null, '更新角色成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除角色（同步清理菜单关联）
   * @route DELETE /role/:id
   * @group 角色管理
   * @param {string} id.path.required - 角色ID
   * @returns {object} 200 - 删除结果
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return this.fail(res, '缺少角色ID参数');
      }
      // 调用 service 层删除角色（含校验和菜单关联清理）
      const result = await this.roleService.delete(id);
      if (!result.success) {
        return this.fail(res, result.message, result.code);
      }
      return this.success(res, null, '删除角色及关联成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }

  /**
   * 获取角色选项（用于下拉列表）
   * @route GET /role/options
   * @group 角色管理
   * @returns {object} 200 - 角色选项
   */
  async getOptions(req, res) {
    try {
      // 调用服务获取角色选项
      const options = await this.roleService.getOptions();
      
      // 返回成功响应
      return this.success(res, options, '获取角色选项成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }
}

module.exports = RoleManagementController;
