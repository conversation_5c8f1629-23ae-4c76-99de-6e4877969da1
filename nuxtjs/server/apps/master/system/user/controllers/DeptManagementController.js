const BaseController = require('../../../../../core/controllers/BaseController');
const DeptManagementService = require('../services/DeptManagementService');
const SystemDeptDto = require('../dto/SystemDeptDto');

class DeptManagementController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.deptService = new DeptManagementService(prisma);
  }

  /**
   * 创建部门
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      // 使用DTO验证请求数据
      const { error, value } = SystemDeptDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }

      const data = value;
      data.created_by = req.user?.id;
      const result = await this.deptService.create(data);
      this.success(res, result, '创建部门成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新部门
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      // 合并 params.id 到 body
      const data = { ...req.body, id: req.params.id };
      const { error, value } = SystemDeptDto.validateUpdate(data);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      value.updated_by = req.user?.id;
      const result = await this.deptService.update(value.id, value);
      this.success(res, result, '更新部门成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除部门
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      // 传入当前操作用户ID作为更新人
      const result = await this.deptService.delete(id, req.user?.id);
      this.success(res, result, '删除部门成功');
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取部门列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @example
   * 请求示例: /dept?name=研发部&leader=张三&status=1&page=1&pageSize=10
   * 支持的筛选参数:
   * - name: 部门名称（模糊查询）
   * - leader: 负责人（模糊查询）
   * - status: 状态，0-禁用，1-正常
   * - page: 页码
   * - pageSize: 每页条数
   */
  async list(req, res) {
    try {
      // 参数验证，包含分页参数
      const { error, value } = SystemDeptDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      console.log('DTO 验证后的 value:', value);

      // 提取分页参数
      const { page, page_size, ...filters } = value;
      const pageSize = page_size;

      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      // 调用服务获取部门列表
      const result = await this.deptService.list({ ...filters, ...pagination });
      // 使用标准列表响应格式
      this.successList(
        res,
        result.list,
        result.total,
        page,
        pageSize,
        '获取部门列表成功'
      );
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取部门详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async detail(req, res) {
    try {
      const { id } = req.params;
      // 简单数字ID校验，可根据需要扩展到DTO
      if (!id || isNaN(Number(id)) || Number(id) < 1) {
        return this.fail(res, '部门ID无效');
      }
      const result = await this.deptService.getById(id);
      if (!result.success) {
        return this.fail(res, result.message, result.code);
      }
      return this.success(res, result.data, '获取部门详情成功');
    } catch (error) {
      return this.fail(res, error.message);
    }
  }
}

module.exports = DeptManagementController;
