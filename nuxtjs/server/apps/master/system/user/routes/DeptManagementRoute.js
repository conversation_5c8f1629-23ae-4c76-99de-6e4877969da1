const express = require('express');
const router = express.Router();
const { prisma } = require('../../../../../core/database/prisma');
const DeptManagementController = require('../controllers/DeptManagementController');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new DeptManagementController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

// 部门管理路由
/**
 * @swagger
 * /api/v1/master/system/dept:
 *   get:
 *     tags: [系统管理/用户中心/部门管理]
 *     summary: 获取部门列表
 *     description: 分页获取部门列表
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 部门名称（可选查询条件）
 *       - in: query
 *         name: leader
 *         schema:
 *           type: string
 *         description: 负责人（可选查询条件）
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态（0-禁用，1-启用）
 *       - in: query
 *         name: start_time
 *         schema:
 *           type: integer
 *         description: 创建时间开始（Unix时间戳）
 *       - in: query
 *         name: end_time
 *         schema:
 *           type: integer
 *         description: 创建时间结束（Unix时间戳）
 *     responses:
 *       200:
 *         description: 返回部门列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 10
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/SystemDepartment'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/dept/{id}:
 *   get:
 *     tags: [系统管理/用户中心/部门管理]
 *     summary: 获取部门详情
 *     description: 根据ID获取部门详细信息
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         description: 返回部门详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/SystemDepartment'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.get('/:id', controller.detail.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/dept:
 *   post:
 *     tags: [系统管理/用户中心/部门管理]
 *     summary: 创建部门
 *     description: 创建新的部门
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemDepartmentCreateRequest'
 *     responses:
 *       201:
 *         description: 部门创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/SystemDepartment'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/dept/{id}:
 *   put:
 *     tags: [系统管理/用户中心/部门管理]
 *     summary: 更新部门
 *     description: 根据ID更新部门信息
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemDepartmentUpdateRequest'
 *     responses:
 *       200:
 *         description: 部门更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/SystemDepartment'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/dept/{id}:
 *   delete:
 *     tags: [系统管理/用户中心/部门管理]
 *     summary: 删除部门
 *     description: 根据ID删除部门
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         description: 部门删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "删除成功"
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

module.exports = router;

// 导出 paths 对象供动态扫描
module.exports.paths = {
  '/api/v1/master/system/dept': {
    get: {
      tags: ['系统管理/用户中心/部门管理'],
      summary: '获取部门列表',
      parameters: [
        { $ref: '#/components/parameters/PageParam' },
        { $ref: '#/components/parameters/PageSizeParam' },
        {
          in: 'query',
          name: 'name',
          schema: { type: 'string' },
          description: '部门名称（可选查询条件）'
        },
        {
          in: 'query',
          name: 'leader',
          schema: { type: 'string' },
          description: '负责人（可选查询条件）'
        },
        {
          in: 'query',
          name: 'status',
          schema: { type: 'integer', enum: [0, 1] },
          description: '状态（0-禁用，1-启用）'
        },
        {
          in: 'query',
          name: 'start_time',
          schema: { type: 'integer' },
          description: '创建时间开始（Unix时间戳）'
        },
        {
          in: 'query',
          name: 'end_time',
          schema: { type: 'integer' },
          description: '创建时间结束（Unix时间戳）'
        }
      ],
      responses: {
        200: {
          description: '返回部门列表',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  data: {
                    type: 'object',
                    properties: {
                      total: { type: 'integer', example: 10 },
                      items: {
                        type: 'array',
                        items: { $ref: '#/components/schemas/SystemDepartment' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        500: { $ref: '#/components/responses/Error' }
      }
    },
    post: {
      tags: ['系统管理/用户中心/部门管理'],
      summary: '创建部门',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/SystemDepartmentCreateRequest' }
          }
        }
      },
      responses: {
        201: {
          description: '部门创建成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  data: { $ref: '#/components/schemas/SystemDepartment' }
                }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        500: { $ref: '#/components/responses/Error' }
      }
    }
  },
  '/api/v1/master/system/dept/{id}': {
    get: {
      tags: ['系统管理/用户中心/部门管理'],
      summary: '获取部门详情',
      parameters: [{ $ref: '#/components/parameters/IdParam' }],
      responses: {
        200: {
          description: '返回部门详情',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  data: { $ref: '#/components/schemas/SystemDepartment' }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/Error' }
      }
    },
    put: {
      tags: ['系统管理/用户中心/部门管理'],
      summary: '更新部门',
      parameters: [{ $ref: '#/components/parameters/IdParam' }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/SystemDepartmentUpdateRequest' }
          }
        }
      },
      responses: {
        200: {
          description: '部门更新成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  data: { $ref: '#/components/schemas/SystemDepartment' }
                }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/Error' }
      }
    },
    delete: {
      tags: ['系统管理/用户中心/部门管理'],
      summary: '删除部门',
      parameters: [{ $ref: '#/components/parameters/IdParam' }],
      responses: {
        200: {
          description: '部门删除成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  message: { type: 'string', example: '删除成功' }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/Error' }
      }
    }
  }
};
