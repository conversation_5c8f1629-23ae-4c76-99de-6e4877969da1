/**
 * 商品SKU控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const GoodsSkuModel = require('../models/GoodsSkuModel');
const Joi = require('joi');

class GoodsSkuController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
  }

  /**
   * 根据SKU ID数组批量获取商品信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSkusByIds(req, res) {
    try {
      // 验证请求数据
      const schema = Joi.object({
        skuIds: Joi.array().items(Joi.string().required()).min(1).required().description('商品SKU ID数组')
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 获取SKU信息
      const skuInfoList = await GoodsSkuModel.getSkusByIds(value.skuIds);
      
      // 返回成功响应
      this.success(res, skuInfoList, '批量获取商品信息成功');
    } catch (error) {
      console.error('批量获取商品信息失败:', error);
      
      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 编辑商品SKU渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateSkuChannels(req, res) {
    try {
      // 验证请求数据
      const schema = Joi.object({
        skuId: Joi.string().required().description('商品SKU ID'),
        channels: Joi.array().items(
          Joi.object({
            id: Joi.string().allow(null, '').description('渠道关联记录ID，新增时可为空'),
            channelId: Joi.string().required().description('渠道ID'),
            channelName: Joi.string().required().description('渠道名称'),
            channelIcon: Joi.string().allow(null, '').description('渠道图标'),
            thirdPartySkuId: Joi.string().allow(null, '').description('第三方SKU ID'),
            isEnabled: Joi.number().valid(0, 1).default(1).description('是否启用：1-启用，0-禁用，默认启用')
          })
        ).required().description('渠道列表')
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      const { skuId, channels } = value;

      // 检查SKU是否存在
      const existingSku = await this.prisma.goodsSku.findUnique({
        where: {
          id: BigInt(skuId)
        }
      });

      if (!existingSku || existingSku.deleted_at !== null) {
        return this.fail(res, `SKU(ID:${skuId})不存在`, 404);
      }

      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      const now = BigInt(Date.now());

      // 开始事务处理 - 增量更新
      const result = await this.prisma.$transaction(async (prisma) => {
        // 1. 获取当前SKU的所有渠道关联
        const existingChannels = await prisma.goodsSkuChannel.findMany({
          where: {
            goods_sku_id: BigInt(skuId),
            deleted_at: null
          }
        });

        // 2. 分类处理：新增、更新、删除
        const existingChannelIds = existingChannels.map(ch => ch.id.toString());
        const incomingChannelIds = channels.filter(ch => ch.id && ch.id !== '').map(ch => ch.id);

        // 需要删除的渠道（存在于数据库但不在请求中）
        const channelsToDelete = existingChannelIds.filter(id => !incomingChannelIds.includes(id));

        // 需要新增的渠道（没有id或id为空）
        const channelsToCreate = channels.filter(ch => !ch.id || ch.id === '');

        // 需要更新的渠道（有id且存在于数据库中）
        const channelsToUpdate = channels.filter(ch => ch.id && ch.id !== '' && existingChannelIds.includes(ch.id));

        // 3. 执行删除操作（软删除，同时清空third_party_sku_id避免唯一约束冲突）
        if (channelsToDelete.length > 0) {
          await prisma.goodsSkuChannel.updateMany({
            where: {
              id: { in: channelsToDelete.map(id => BigInt(id)) },
              goods_sku_id: BigInt(skuId)
            },
            data: {
              deleted_at: now,
              third_party_sku_id: null, // 清空第三方SKU ID避免唯一约束冲突
              updated_by: userId,
              updated_at: now
            }
          });
        }

        // 4. 执行新增操作
        if (channelsToCreate.length > 0) {
          const createData = channelsToCreate.map(channel => ({
            goods_sku_id: BigInt(skuId),
            channel_id: BigInt(channel.channelId),
            third_party_sku_id: channel.thirdPartySkuId || null,
            is_enabled: channel.isEnabled || 1,
            created_at: now,
            updated_at: now,
            created_by: userId,
            updated_by: userId
          }));

          await prisma.goodsSkuChannel.createMany({
            data: createData
          });
        }

        // 5. 执行更新操作
        for (const channel of channelsToUpdate) {
          await prisma.goodsSkuChannel.update({
            where: {
              id: BigInt(channel.id)
            },
            data: {
              channel_id: BigInt(channel.channelId),
              third_party_sku_id: channel.thirdPartySkuId || null,
              is_enabled: channel.isEnabled || 1,
              updated_at: now,
              updated_by: userId
            }
          });
        }

        // 6. 返回更新后的渠道信息
        const updatedChannels = await prisma.goodsSkuChannel.findMany({
          where: {
            goods_sku_id: BigInt(skuId),
            deleted_at: null
          },
          include: {
            channel: {
              select: {
                id: true,
                name: true,
                icon_url: true
              }
            }
          }
        });

        return updatedChannels;
      });

      // 格式化返回数据
      const formattedChannels = result.map(item => ({
        id: item.id.toString(),
        channelId: item.channel_id.toString(),
        channelName: item.channel.name,
        channelIcon: item.channel.icon_url || '',
        thirdPartySkuId: item.third_party_sku_id || '',
        isEnabled: item.is_enabled
      }));

      // 返回成功响应
      this.success(res, {
        skuId: skuId,
        channels: formattedChannels
      }, '编辑商品SKU渠道成功');
    } catch (error) {
      console.error('编辑商品SKU渠道失败:', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 处理数据库错误
   * @param {Error} error - 数据库错误对象
   * @returns {Object} 标准化的错误信息
   */
  handleDbError(error) {
    // Prisma 错误处理
    if (error.code) {
      switch (error.code) {
        case 'P2002': // 唯一约束冲突
          return {
            message: '记录已存在',
            code: 400
          };
        case 'P2003': // 外键约束失败
          return {
            message: '关联记录不存在',
            code: 400
          };
        case 'P2025': // 记录不存在
          return {
            message: '记录不存在',
            code: 404
          };
        default:
          return {
            message: '数据库操作失败',
            code: 500
          };
      }
    }

    return {
      message: error.message || '未知错误',
      code: 500
    };
  }
}

module.exports = GoodsSkuController;
