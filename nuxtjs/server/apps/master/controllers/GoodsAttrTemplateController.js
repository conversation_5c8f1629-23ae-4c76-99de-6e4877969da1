const BaseController = require('../../../core/controllers/BaseController');
const GoodsAttrTemplateService = require('../services/GoodsAttrTemplateService');
const GoodsAttrTemplateDto = require('../dto/GoodsAttrTemplateDto');

/**
 * 商品属性模板控制器
 */
class GoodsAttrTemplateController extends BaseController {
    constructor(prisma) {
    super(prisma);
    this.goodsAttrTemplateService = new GoodsAttrTemplateService(prisma);
  }

  /**
   * 获取商品属性模板列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getGoodsAttrTemplates(req, res) {
    try {
      // 验证查询参数
      const { error, value } = GoodsAttrTemplateDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, '参数验证失败', 400);
      }

      // 使用基类的分页方法
      const { page, pageSize, skip, take } = this.getPagination(req.query);
      
      // 将分页参数添加到查询参数中
      const options = {
        ...value,
        skip,
        take
      };

      // 获取模板列表
      const result = await this.goodsAttrTemplateService.getAllGoodsAttrTemplates(options);
      
      // 使用基类的标准列表响应方法
      this.successList(
        res,
        result.items,
        result.total,
        page,
        pageSize,
        '获取商品属性模板列表成功'
      );
    } catch (error) {
      console.error('获取商品属性模板列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 根据ID获取商品属性模板
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getGoodsAttrTemplateById(req, res) {
    try {
      const { id } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(id)) {
        return this.fail(res, 'ID必须是数字', 400);
      }

      // 获取模板
      const template = await this.goodsAttrTemplateService.getGoodsAttrTemplateById(id);
      
      this.success(res, template, '获取商品属性模板成功');
    } catch (error) {
      console.error(`获取ID为${req.params.id}的商品属性模板失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 添加商品属性模板
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async addGoodsAttrTemplate(req, res) {
    try {
      // 验证请求体
      const { error, value } = GoodsAttrTemplateDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', 400);
      }

      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      console.log('创建商品属性模板 - 用户ID:', userId);

      // 添加模板
      const newTemplate = await this.goodsAttrTemplateService.addGoodsAttrTemplate(value, userId);
      
      this.success(res, newTemplate, '添加商品属性模板成功', 200);
    } catch (error) {
      console.error('添加商品属性模板失败:', error);

      // 处理名称重复错误
      if (error.message && error.message.includes('已存在')) {
        return this.fail(res, error.message, 400);
      }

      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新商品属性模板
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateGoodsAttrTemplate(req, res) {
    try {
      const { id } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(id)) {
        return this.fail(res, 'ID必须是数字', 400);
      }

      // 验证请求体
      const { error, value } = GoodsAttrTemplateDto.validateUpdate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', 400);
      }

      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      console.log('更新商品属性模板 - 用户ID:', userId);

      // 更新模板
      const updatedTemplate = await this.goodsAttrTemplateService.updateGoodsAttrTemplate(id, value, userId);
      
      this.success(res, updatedTemplate, '更新商品属性模板成功');
    } catch (error) {
      console.error(`更新ID为${req.params.id}的商品属性模板失败:`, error);

      // 处理名称重复错误
      if (error.message && error.message.includes('已存在')) {
        return this.fail(res, error.message, 400);
      }

      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除商品属性模板
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteGoodsAttrTemplate(req, res) {
    try {
      const { id } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(id)) {
        return this.fail(res, 'ID必须是数字', 400);
      }

      // 删除模板
      await this.goodsAttrTemplateService.deleteGoodsAttrTemplate(id);
      
      this.success(res, null, '删除商品属性模板成功');
    } catch (error) {
      console.error(`删除ID为${req.params.id}的商品属性模板失败:`, error);
      
      // 如果是关联商品的错误，单独处理
      if (error.message.includes('关联')) {
        return this.fail(res, error.message, 400);
      }
      
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
  
  /**
   * 添加属性模板参数
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async addGoodsAttrTemplateParam(req, res) {
    try {
      const { templateId } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(templateId)) {
        return res.status(200).json({
          code: 400,
          message: '模板ID必须是数字'
        });
      }

      // 验证请求体
      const { error, value } = GoodsAttrTemplateDto.validateCreateParam(req.body);
      if (error) {
        return res.status(200).json({
          code: 400,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message)
        });
      }

      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      console.log('添加属性模板参数 - 用户ID:', userId);

      // 添加参数
      const newParam = await this.goodsAttrTemplateService.addGoodsAttrTemplateParam(templateId, value, userId);
      
      return res.status(200).json({
        code: 200,
        message: '添加属性模板参数成功',
        data: newParam
      });
    } catch (error) {
      console.error('添加属性模板参数失败:', error);
      
      // 如果是模板不存在的错误，返回404
      if (error.message.includes('不存在')) {
        return res.status(200).json({
          code: 404,
          message: error.message
        });
      }
      
      return res.status(200).json({
        code: 500,
        message: '添加属性模板参数失败',
        error: error.message
      });
    }
  }
  
  /**
   * 获取属性模板的所有参数
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getGoodsAttrTemplateParams(req, res) {
    try {
      const { templateId } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(templateId)) {
        return res.status(200).json({
          code: 400,
          message: '模板ID必须是数字'
        });
      }

      // 获取参数列表
      const params = await this.goodsAttrTemplateService.getGoodsAttrTemplateParams(templateId);
      
      return res.json({
        code: 200,
        message: '获取属性模板参数列表成功',
        data: params
      });
    } catch (error) {
      console.error(`获取模板ID为${req.params.templateId}的参数失败:`, error);
      
      return res.status(200).json({
        code: 500,
        message: '获取属性模板参数失败',
        error: error.message
      });
    }
  }

  /**
   * 更新属性模板参数
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateGoodsAttrTemplateParam(req, res) {
    try {
      const { paramId } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(paramId)) {
        return res.status(200).json({
          code: 400,
          message: '参数ID必须是数字'
        });
      }

      // 验证请求体
      const { error, value } = GoodsAttrTemplateDto.validateUpdateParam(req.body);
      if (error) {
        return res.status(200).json({
          code: 400,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message)
        });
      }

      // 获取用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      console.log('更新属性模板参数 - 用户ID:', userId);

      // 更新参数
      const updatedParam = await this.goodsAttrTemplateService.updateGoodsAttrTemplateParam(paramId, value, userId);
      
      return res.json({
        code: 200,
        message: '更新属性模板参数成功',
        data: updatedParam
      });
    } catch (error) {
      console.error(`更新参数ID为${req.params.paramId}的参数失败:`, error);
      
      // 如果是参数不存在的错误，返回404
      if (error.message.includes('不存在')) {
        return res.status(200).json({
          code: 404,
          message: error.message
        });
      }
      
      return res.status(200).json({
        code: 500,
        message: '更新属性模板参数失败',
        error: error.message
      });
    }
  }
  
  /**
   * 删除属性模板参数
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteGoodsAttrTemplateParam(req, res) {
    try {
      const { paramId } = req.params;
      
      // 验证ID是否为数字
      if (isNaN(paramId)) {
        return res.status(200).json({
          code: 400,
          message: '参数ID必须是数字'
        });
      }

      // 删除参数
      await this.goodsAttrTemplateService.deleteGoodsAttrTemplateParam(paramId);
      
      return res.json({
        code: 200,
        message: '删除属性模板参数成功'
      });
    } catch (error) {
      console.error(`删除参数ID为${req.params.paramId}的参数失败:`, error);
      
      // 如果是参数不存在的错误，返回404
      if (error.message.includes('不存在')) {
        return res.status(200).json({
          code: 404,
          message: error.message
        });
      }
      
      return res.status(200).json({
        code: 500,
        message: '删除属性模板参数失败',
        error: error.message
      });
    }
  }
}

module.exports = GoodsAttrTemplateController;
