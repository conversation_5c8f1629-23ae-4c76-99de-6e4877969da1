const BaseController = require('../../../core/controllers/BaseController');
const GoodsFreightTemplateService = require('../services/GoodsFreightTemplateService');
const ChargeTypeEnum = require('../constants/ChargeTypeEnum');

/**
 * 运费模板控制器
 */
class GoodsFreightTemplateController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.goodsFreightTemplateService = new GoodsFreightTemplateService(prisma);
  }

  /**
   * 获取运费模板列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getGoodsFreightTemplates(req, res) {
    try {
      const { page = 1, pageSize = 10, name, chargeType } = req.query;
      console.log('运费模板列表搜索参数:', { page, pageSize, name, chargeType });
      
      const filters = {};

      if (name) {
        filters.name = name;  // 直接传递字符串，让服务层处理 contains
      }
      if (chargeType) {
        filters.chargeType = parseInt(chargeType);
      }
      
      console.log('构建的过滤条件:', filters);

      const result = await this.goodsFreightTemplateService.getList({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        filters
      });
      
      // 格式化数据为精简的驼峰格式
      const formattedItems = result.items.map(template => {
                 // 构建配送区域字符串
         let deliveryAreas = '';
         if (template.freightConfigs && Array.isArray(template.freightConfigs)) {
           const regionStrings = [];
           let hasDefaultConfig = false;
           
           template.freightConfigs.forEach(config => {
             if (config.isDefault) {
               // 如果有默认配置，标记为全国
               hasDefaultConfig = true;
             } else if (config.regionRelations && config.regionRelations.length > 0) {
               // 处理具体区域
               config.regionRelations.forEach(relation => {
                 if (relation.regionName && relation.parentName) {
                   regionStrings.push(`${relation.parentName}-${relation.regionName}`);
                 } else if (relation.regionName) {
                   regionStrings.push(relation.regionName);
                 }
               });
             }
           });
           
           // 如果有默认配置且没有其他具体区域，显示"全国"
           if (hasDefaultConfig && regionStrings.length === 0) {
             deliveryAreas = '全国';
           } else if (regionStrings.length > 0) {
             // 去重并用"、"连接
             deliveryAreas = [...new Set(regionStrings)].join('、');
             
             // 如果还有默认配置，在前面加上"全国"
             if (hasDefaultConfig) {
               deliveryAreas = `全国、${deliveryAreas}`;
             }
           } else {
             deliveryAreas = '全国';
           }
         }
        
        return {
          id: template.id,
          name: template.name,
          chargeType: template.chargeType,
          chargeTypeText: this.goodsFreightTemplateService.getChargeTypeText(template.chargeType),
          createdAt: template.createdAt,
          createdBy: template.createdBy,
          deliveryAreas: deliveryAreas || '全国', // 新增配送区域字段
          freightConfigs: template.freightConfigs ? template.freightConfigs.map(config => ({
            id: config.id,
            firstItem: config.firstItem,
            firstFee: config.firstFee,
            additionalItem: config.additionalItem,
            additionalFee: config.additionalFee,
            isDefault: config.isDefault,
            regionRelations: config.regionRelations ? config.regionRelations.map(relation => ({
              regionCode: relation.regionCode,
              regionName: relation.regionName,
              parentName: relation.parentName
            })) : []
          })) : []
        };
      });

      // 使用基类的标准列表响应方法，与属性模板列表保持一致
      this.successList(
        res,
        formattedItems,
        result.total,
        parseInt(page),
        parseInt(pageSize),
        '获取运费模板列表成功'
      );
    } catch (error) {
      console.error('获取运费模板列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 将BigInt转换为字符串
   * @param {Object|Array} data - 需要转换的数据
   * @returns {Object|Array} 转换后的数据
   */
  convertBigIntToString(data) {
    if (data === null || data === undefined) {
      return data;
    }
    
    if (typeof data === 'bigint') {
      return data.toString();
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.convertBigIntToString(item));
    }
    
    if (typeof data === 'object') {
      const result = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.convertBigIntToString(data[key]);
        }
      }
      return result;
    }
    
    return data;
  }

  /**
   * 获取指定ID的运费模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getGoodsFreightTemplateById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '运费模板ID不能为空', 400);
      }
      
      // 调用服务获取数据
      const template = await this.goodsFreightTemplateService.getGoodsFreightTemplateById(id);
      
      if (!template) {
        return this.fail(res, `运费模板(ID: ${id})不存在`, 404);
      }

      // 格式化数据为驼峰格式
      const formattedTemplate = {
        id: template.id,
        name: template.name,
        chargeType: template.chargeType,
        chargeTypeText: this.goodsFreightTemplateService.getChargeTypeText(template.chargeType),
        freightConfigs: template.freightConfigs ? template.freightConfigs.map(config => ({
          id: config.id,
          firstItem: config.firstItem,
          firstFee: config.firstFee,
          additionalItem: config.additionalItem,
          additionalFee: config.additionalFee,
          isDefault: config.isDefault,
          regionRelations: config.regionRelations ? config.regionRelations.map(relation => ({
            regionCode: relation.regionCode,
            regionName: relation.regionName,
            parentName: relation.parentName
          })) : []
        })) : []
      };

      this.success(res, formattedTemplate, '获取运费模板成功');
    } catch (error) {
      console.error(`获取运费模板(ID: ${req.params.id})失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 添加运费模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async addGoodsFreightTemplate(req, res) {
    try {
      // 使用驼峰格式接收请求数据
      const { name, chargeType, isDefault, freightConfigs } = req.body;
      console.log('前端传递的新增数据:', JSON.stringify({ name, chargeType, isDefault, freightConfigs }, null, 2));
      
      // 获取当前用户ID (如果没有认证中间件，可能为null)
      const userId = req.user ? req.user.id : null;
      
      // 将驼峰格式转换为下划线格式
      const templateData = {
        name,
        charge_type: parseInt(chargeType),
        is_default: isDefault === true || isDefault === 'true' ? 1 : 0,
        freight_configs: Array.isArray(freightConfigs) ? freightConfigs.map(config => ({
          first_item: config.firstItem,
          first_fee: config.firstFee,
          additional_item: config.additionalItem,
          additional_fee: config.additionalFee,
          is_default: config.isDefault === true || config.isDefault === 1 || config.isDefault === '1' ? 1 : 0,
          areas: Array.isArray(config.areas) ? config.areas : []
        })) : []
      };

      // 调用服务添加数据
      const newTemplate = await this.goodsFreightTemplateService.addGoodsFreightTemplate(templateData, userId);
      
      console.log('服务层返回的模板数据:', JSON.stringify(newTemplate, null, 2));

      // 检查返回数据是否为空
      if (!newTemplate) {
        console.error('服务层返回数据为空');
        return this.fail(res, '创建运费模板失败，请重试', 500);
      }

      // 格式化数据为驼峰格式
      const templateChargeType = newTemplate.chargeType || newTemplate.charge_type;
      console.log('提取的chargeType值:', templateChargeType);
      
              const formattedTemplate = {
          id: newTemplate.id,
          name: newTemplate.name,
          chargeType: templateChargeType,
          chargeTypeText: this.getChargeTypeText(templateChargeType),
        freightConfigs: newTemplate.freightConfigs ? newTemplate.freightConfigs.map(config => ({
          id: config.id,
          firstItem: config.firstItem,
          firstFee: config.firstFee,
          additionalItem: config.additionalItem,
          additionalFee: config.additionalFee,
          isDefault: config.isDefault,
          regionRelations: config.regionRelations ? config.regionRelations.map(relation => ({
            regionCode: relation.regionCode,
            regionName: relation.regionName,
            parentName: relation.parentName
          })) : []
        })) : []
      };

      this.success(res, formattedTemplate, '添加运费模板成功', 200);
    } catch (error) {
      console.error('添加运费模板失败:', error);

      // 处理验证错误
      if (error.name === 'ValidationError') {
        return this.fail(res, '数据验证失败', 400);
      }

      // 处理名称重复错误
      if (error.message && error.message.includes('已存在')) {
        return this.fail(res, error.message, 400);
      }

      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新运费模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateGoodsFreightTemplate(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '运费模板ID不能为空', 400);
      }
      
      // 使用驼峰格式接收请求数据
      const { name, chargeType, isDefault, freightConfigs } = req.body;
      console.log('前端传递的更新数据:', JSON.stringify({ name, chargeType, isDefault, freightConfigs }, null, 2));
      
      // 获取当前用户ID (如果没有认证中间件，可能为null)
      const userId = req.user ? req.user.id : null;
      
      // 将驼峰格式转换为下划线格式，正确处理运费配置数据
      const templateData = {
        name,
        charge_type: parseInt(chargeType),
        is_default: isDefault === true || isDefault === 'true' ? 1 : 0,
        // 将 freightConfigs 转换为 freight_configs 格式
        freight_configs: Array.isArray(freightConfigs) ? freightConfigs.map(config => {
          // 检查是否为全国默认配置
          const isDefaultConfig = config.isDefault === 1 || config.isDefault === true || config.name === '全国';
          
          return {
            id: config.id, // 更新时可能需要ID
            first_item: config.firstItem || 1,
            first_fee: config.firstFee || 0,
            additional_item: config.additionalItem || 1,
            additional_fee: config.additionalFee || 0,
            is_default: isDefaultConfig ? 1 : 0,
            // 如果不是默认配置，处理区域数据
            areas: !isDefaultConfig && config.areas && Array.isArray(config.areas) ? config.areas.map(area => ({
              code: area.code,
              name: area.name || '',
              parentName: area.parentName || ''
            })) : []
          };
        }) : []
      };

      console.log('更新运费模板数据:', JSON.stringify(templateData, null, 2));

      // 调用服务更新数据
      const updatedTemplate = await this.goodsFreightTemplateService.updateGoodsFreightTemplate(id, templateData, userId);
      
      console.log('服务层返回的更新模板数据:', JSON.stringify(updatedTemplate, null, 2));

      // 格式化数据为驼峰格式
      const updateChargeType = updatedTemplate.chargeType || updatedTemplate.charge_type;
      console.log('更新时提取的chargeType值:', updateChargeType);
      
      const formattedTemplate = {
        id: updatedTemplate.id,
        name: updatedTemplate.name,
        chargeType: updateChargeType,
        chargeTypeText: this.getChargeTypeText(updateChargeType),
        freightConfigs: updatedTemplate.freightConfigs ? updatedTemplate.freightConfigs.map(config => ({
          id: config.id,
          firstItem: config.firstItem,
          firstFee: config.firstFee,
          additionalItem: config.additionalItem,
          additionalFee: config.additionalFee,
          isDefault: config.isDefault,
          regionRelations: config.regionRelations ? config.regionRelations.map(relation => ({
            regionCode: relation.regionCode,
            regionName: relation.regionName,
            parentName: relation.parentName
          })) : []
        })) : []
      };

      this.success(res, formattedTemplate, '更新运费模板成功');
    } catch (error) {
      console.error(`更新运费模板(ID: ${req.params.id})失败:`, error);

      // 处理验证错误
      if (error.name === 'ValidationError') {
        return this.fail(res, '数据验证失败', 400);
      }

      // 处理名称重复错误
      if (error.message && error.message.includes('已存在')) {
        return this.fail(res, error.message, 400);
      }

      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除运费模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteGoodsFreightTemplate(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '运费模板ID不能为空', 400);
      }
      
      // 获取当前用户ID (如果没有认证中间件，可能为null)
      const userId = req.user ? req.user.id : null;
      
      // 调用服务删除数据
      const result = await this.goodsFreightTemplateService.deleteGoodsFreightTemplate(id, userId);
      
      this.success(res, result, '删除运费模板成功');
    } catch (error) {
      console.error(`删除运费模板(ID: ${req.params.id})失败:`, error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取计费类型文本
   * @param {number} chargeType - 计费类型
   * @returns {string} 计费类型文本
   */
  getChargeTypeText(chargeType) {
    return ChargeTypeEnum.getChargeTypeText(chargeType);
  }
  
  /**
   * 获取计费单位文本
   * @param {number} chargeType - 计费类型
   * @returns {string} 计费单位文本
   */
  getChargeUnitText(chargeType) {
    return ChargeTypeEnum.getChargeUnitText(chargeType);
  }

  /**
   * 获取计费类型选项
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChargeTypeOptions(req, res) {
    try {
      const options = ChargeTypeEnum.getAllOptions();
      this.success(res, options, '获取计费类型选项成功');
    } catch (error) {
      console.error('获取计费类型选项失败:', error);
      this.fail(res, '获取计费类型选项失败', 500);
    }
  }
}

module.exports = GoodsFreightTemplateController;
