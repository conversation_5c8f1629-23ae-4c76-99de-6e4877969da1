/**
 * 供应商数据传输对象
 */
class SupplierDto {
  /**
   * 创建供应商DTO验证规则 - 支持驼峰命名
   */
  static createRules() {
    return {
      // 基础信息 - 驼峰命名
      supplierName: {
        type: 'string',
        required: true,
        max: 200,
        message: '供应商名称必填，最大长度200字符'
      },
      supplierCode: {
        type: 'string',
        required: true,
        max: 50,
        pattern: /^[A-Z0-9]{3,20}$/,
        message: '供应商代码必填，3-20位大写字母和数字组合'
      },
      companyNature: {
        type: 'string',
        required: true,
        max: 100,
        message: '公司性质必填，最大长度100字符'
      },
      mainProducts: {
        type: 'array',
        required: true,
        message: '主营产品必填'
      },
      address: {
        type: 'string',
        required: true,
        max: 500,
        message: '通信地址必填，最大长度500字符'
      },
      detailedAddress: {
        type: 'string',
        required: false,
        max: 500,
        message: '详细地址最大长度500字符'
      },
      productionAddress: {
        type: 'string',
        required: false,
        max: 500,
        message: '生产地址最大长度500字符'
      },

      // 合作信息 - 驼峰命名
      cooperationType: {
        type: 'string',
        required: false,
        max: 50,
        message: '合作方式最大长度50字符'
      },
      cooperationStatus: {
        type: 'string',
        required: true,
        max: 50,
        message: '合作状态必填，最大长度50字符'
      },
      cooperationAgreement: {
        type: 'string',
        required: false,
        max: 200,
        message: '合作协议名称最大长度200字符'
      },
      supplierRelation: {
        type: 'string',
        required: false,
        max: 100,
        message: '供应商关联最大长度100字符'
      },
      supplierGroup: {
        type: 'string',
        required: false,
        max: 50,
        message: '供应商分组最大长度50字符'
      },

      // 企业信息 - 驼峰命名
      creditCode: {
        type: 'string',
        required: false,
        max: 50,
        message: '统一社会信用代码最大长度50字符'
      },
      registeredCapital: {
        type: 'number',
        required: false,
        min: 0,
        message: '注册资本必须大于等于0'
      },
      registerTime: {
        type: 'string',
        required: false,
        message: '注册时间格式不正确'
      },
      orderCount: {
        type: 'integer',
        required: false,
        min: 0,
        message: '订单数量必须大于等于0'
      },

      // 账户信息 - 驼峰命名
      paymentTerms: {
        type: 'string',
        required: true,
        max: 200,
        message: '付款条件必填，最大长度200字符'
      },
      invoiceType: {
        type: 'string',
        required: true,
        max: 50,
        message: '发票类型必填，最大长度50字符'
      },
      taxRate: {
        type: 'number',
        required: true,
        min: 0,
        max: 100,
        message: '税率必填，必须在0-100之间'
      },
      accountName: {
        type: 'string',
        required: false,
        max: 200,
        message: '账户名称最大长度200字符'
      },
      settlementMethod: {
        type: 'string',
        required: false,
        max: 50,
        message: '结算方式最大长度50字符'
      },
      accountNumber: {
        type: 'string',
        required: false,
        max: 100,
        message: '账户号码最大长度100字符'
      },
      bankName: {
        type: 'string',
        required: false,
        max: 200,
        message: '开户银行最大长度200字符'
      },

      // 操作信息
      department: {
        type: 'string',
        required: true,
        max: 100,
        message: '部门必填，最大长度100字符'
      },
      submitter: {
        type: 'string',
        required: true,
        max: 100,
        message: '提交人必填，最大长度100字符'
      },
      submitDate: {
        type: 'string',
        required: true,
        message: '提交日期必填'
      },
      updater: {
        type: 'string',
        required: false,
        max: 100,
        message: '更新人最大长度100字符'
      },
      updateDate: {
        type: 'string',
        required: false,
        message: '更新日期格式不正确'
      },
      reviewer: {
        type: 'string',
        required: false,
        max: 100,
        message: '复审人最大长度100字符'
      },
      remark: {
        type: 'string',
        required: false,
        message: '备注信息'
      },

      // 关联数据 - 驼峰命名
      brands: {
        type: 'array',
        required: false,
        message: '品牌信息格式不正确'
      },
      contacts: {
        type: 'array',
        required: false,
        message: '联系人信息格式不正确'
      },
      agreements: {
        type: 'array',
        required: false,
        message: '协议信息格式不正确'
      },
      attachments: {
        type: 'object',
        required: false,
        message: '附件信息格式不正确'
      }
    };
  }

  /**
   * 更新供应商DTO验证规则
   */
  static updateRules() {
    const rules = this.createRules();
    // 更新时，大部分字段都不是必填的
    Object.keys(rules).forEach(key => {
      if (rules[key].required) {
        rules[key].required = false;
      }
    });
    return rules;
  }

  /**
   * 查询供应商DTO验证规则
   */
  static queryRules() {
    return {
      page: {
        type: 'integer',
        required: false,
        min: 1,
        message: '页码必须大于0'
      },
      pageSize: {
        type: 'integer',
        required: false,
        min: 1,
        max: 100,
        message: '每页数量必须在1-100之间'
      },
      // 支持驼峰命名
      supplierName: {
        type: 'string',
        required: false,
        message: '供应商名称格式不正确'
      },
      supplierCode: {
        type: 'string',
        required: false,
        message: '供应商代码格式不正确'
      },
      contactPerson: {
        type: 'string',
        required: false,
        message: '联系人格式不正确'
      },
      // 兼容下划线命名
      supplier_name: {
        type: 'string',
        required: false,
        message: '供应商名称格式不正确'
      },
      supplier_code: {
        type: 'string',
        required: false,
        message: '供应商代码格式不正确'
      },
      contact_person: {
        type: 'string',
        required: false,
        message: '联系人格式不正确'
      },
      department: {
        type: 'string',
        required: false,
        message: '部门格式不正确'
      },
      submitter: {
        type: 'string',
        required: false,
        message: '提交人格式不正确'
      },
      // 驼峰命名
      cooperationStatus: {
        type: 'string',
        required: false,
        message: '合作状态格式不正确'
      },
      cooperationType: {
        type: 'string',
        required: false,
        message: '合作方式格式不正确'
      },
      companyNature: {
        type: 'string',
        required: false,
        message: '公司性质格式不正确'
      },
      paymentTerms: {
        type: 'string',
        required: false,
        message: '付款条件格式不正确'
      },
      mainProducts: {
        type: 'string',
        required: false,
        message: '主营产品格式不正确'
      },
      supplierRelation: {
        type: 'string',
        required: false,
        message: '供应商关联格式不正确'
      },
      authorizationStatus: {
        type: 'string',
        required: false,
        enum: ['有', '无'],
        message: '授权情况只能是"有"或"无"'
      },
      // 兼容下划线命名
      cooperation_status: {
        type: 'string',
        required: false,
        message: '合作状态格式不正确'
      },
      cooperation_type: {
        type: 'string',
        required: false,
        message: '合作方式格式不正确'
      },
      company_nature: {
        type: 'string',
        required: false,
        message: '公司性质格式不正确'
      },
      payment_terms: {
        type: 'string',
        required: false,
        message: '付款条件格式不正确'
      },
      brand: {
        type: 'string',
        required: false,
        message: '品牌格式不正确'
      },
      main_products: {
        type: 'string',
        required: false,
        message: '主营产品格式不正确'
      },
      supplier_relation: {
        type: 'string',
        required: false,
        message: '供应商关联格式不正确'
      },
      reviewer: {
        type: 'string',
        required: false,
        message: '复核人格式不正确'
      },
      updater: {
        type: 'string',
        required: false,
        message: '更新人格式不正确'
      },
      address: {
        type: 'string',
        required: false,
        message: '地址格式不正确'
      },
      authorization_status: {
        type: 'string',
        required: false,
        enum: ['有', '无'],
        message: '授权情况只能是"有"或"无"'
      }
    };
  }

  /**
   * 更换复核人DTO验证规则
   */
  static changeReviewerRules() {
    return {
      reviewer: {
        type: 'string',
        required: true,
        max: 100,
        message: '复核人必填，最大长度100字符'
      },
      updater: {
        type: 'string',
        required: true,
        max: 100,
        message: '更新人必填，最大长度100字符'
      }
    };
  }

  /**
   * 更新提交人信息DTO验证规则
   */
  static updateSubmitterInfoRules() {
    return {
      department: {
        type: 'string',
        required: false,
        max: 100,
        message: '部门名称最大长度100字符'
      },
      submitter: {
        type: 'string',
        required: true,
        max: 50,
        message: '提交人必填，最大长度50字符'
      },
      submitTime: {
        type: 'string',
        required: false,
        message: '提交时间格式不正确'
      },
      reviewer: {
        type: 'string',
        required: false,
        max: 50,
        message: '复核人最大长度50字符'
      },
      updater: {
        type: 'string',
        required: false,
        max: 100,
        message: '更新人最大长度100字符'
      },
      updateDate: {
        type: 'string',
        required: false,
        message: '更新时间格式不正确'
      }
    };
  }
}

module.exports = SupplierDto;
