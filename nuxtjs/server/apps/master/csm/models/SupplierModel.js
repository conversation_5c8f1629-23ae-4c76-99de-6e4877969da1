const { prisma } = require('../../../../core/database/prisma');

/**
 * 供应商模型
 */
class SupplierModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取供应商选择列表（简化版本，只返回必要字段）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getSelectList(params = {}) {
    const {
      page = 1,
      pageSize = 10,
      keyword = '',
      status = 1
    } = params;

    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    // 状态过滤（使用合作状态）
    if (status !== undefined && status !== null) {
      // status=1 表示启用状态，对应合作状态为"启用"
      if (parseInt(status) === 1) {
        where.cooperation_status = '启用';
      } else if (parseInt(status) === 0) {
        where.cooperation_status = '不启用';
      }
    }

    // 关键词搜索（供应商名称或编码）
    if (keyword) {
      where.OR = [
        {
          supplier_name: {
            contains: keyword
          }
        },
        {
          supplier_code: {
            contains: keyword
          }
        }
      ];
    }

    try {
      // 查询数据
      const [items, total] = await Promise.all([
        this.prisma.csmSupplier.findMany({
          where,
          select: {
            id: true,
            supplier_name: true,
            supplier_code: true,
            contact_person: true,
            position: true,
            phone: true,
            cooperation_status: true
          },
          skip,
          take: pageSize,
          orderBy: {
            created_at: 'desc'
          }
        }),
        this.prisma.csmSupplier.count({ where })
      ]);

      // 转换数据格式
      const list = items.map(item => ({
        id: item.id,
        name: item.supplier_name,
        code: item.supplier_code,
        contactPerson: item.contact_person,
        position: item.position,
        phone: item.phone,
        status: item.cooperation_status === '启用' ? 1 : 0,
        cooperation_status: item.cooperation_status
      }));

      return {
        list,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('获取供应商选择列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取供应商列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params = {}) {
    const {
      page = 1,
      pageSize = 10,
      // 支持驼峰命名和下划线命名
      supplierName, supplier_name,
      supplierCode, supplier_code,
      contactPerson, contact_person,
      department,
      submitter,
      cooperationStatus, cooperation_status,
      cooperationType, cooperation_type,
      companyNature, company_nature,
      paymentTerms, payment_terms,
      brand,
      mainProducts, main_products,
      supplierRelation, supplier_relation,
      reviewer,
      updater,
      address,
      authorizationStatus, authorization_status
    } = params;
    
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (supplierName || supplier_name) {
      where.supplier_name = {
        contains: supplierName || supplier_name
      };
    }

    if (supplierCode || supplier_code) {
      where.supplier_code = {
        contains: supplierCode || supplier_code
      };
    }

    if (contactPerson || contact_person) {
      where.contact_person = {
        contains: contactPerson || contact_person
      };
    }

    if (department) {
      where.department = {
        contains: department
      };
    }

    if (submitter) {
      where.submitter = {
        contains: submitter
      };
    }

    if (cooperationStatus || cooperation_status) {
      where.cooperation_status = cooperationStatus || cooperation_status;
    }

    if (cooperationType || cooperation_type) {
      where.cooperation_type = cooperationType || cooperation_type;
    }

    if (companyNature || company_nature) {
      where.company_nature = companyNature || company_nature;
    }

    if (paymentTerms || payment_terms) {
      where.payment_terms = {
        contains: paymentTerms || payment_terms
      };
    }

    if (supplierRelation || supplier_relation) {
      where.supplier_relation = {
        contains: supplierRelation || supplier_relation
      };
    }

    if (reviewer) {
      where.reviewer = {
        contains: reviewer
      };
    }

    if (updater) {
      where.updater = {
        contains: updater
      };
    }

    if (address) {
      where.address = {
        contains: address
      };
    }

    // 品牌搜索
    if (brand) {
      where.brands = {
        some: {
          brand_name: {
            contains: brand
          },
          deleted_at: null
        }
      };
    }

    // 主营产品搜索
    if (mainProducts || main_products) {
      where.main_products = {
        some: {
          main_product: {
            product_name: {
              contains: mainProducts || main_products
            }
          },
          deleted_at: null
        }
      };
    }

    // 授权情况搜索
    if (authorizationStatus || authorization_status) {
      const hasAuthorization = (authorizationStatus || authorization_status) === '有';
      where.brands = {
        some: {
          authorized: hasAuthorization,
          deleted_at: null
        }
      };
    }

    // 获取总数
    const total = await this.prisma.csmSupplier.count({ where });

    // 获取列表数据，包含关联数据
    const items = await this.prisma.csmSupplier.findMany({
      where,
      skip,
      take: pageSize,
      include: {
        brands: {
          where: { deleted_at: null },
          select: {
            id: true,
            brand_name: true,
            authorized: true,
            authorization_date: true,
            expiry_date: true,
            authorization_scope: true,
            remark: true
          }
        },
        contacts: {
          where: { deleted_at: null },
          select: {
            id: true,
            name: true,
            phone: true,
            position: true,
            email: true,
            department: true,
            is_primary: true,
            remark: true
          }
        },
        agreements: {
          where: { deleted_at: null },
          select: {
            id: true,
            agreement_type: true,
            agreement_name: true,
            start_date: true,
            end_date: true,
            duration: true,
            status: true,
            sign_date: true,
            file_url: true,
            remark: true
          }
        },
        attachments: {
          where: { deleted_at: null },
          select: {
            id: true,
            attachment_type: true,
            file_name: true,
            file_url: true,
            file_size: true,
            file_type: true,
            sub_type: true,
            remark: true
          }
        },
        main_products: {
          where: { deleted_at: null },
          include: {
            main_product: {
              select: {
                id: true,
                product_name: true
              }
            }
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    return {
      items: items.map(item => this.formatSupplierData(item)),
      pageInfo:{
        total,
        currentPage: page,
        totalPage: Math.ceil(total / pageSize)
      }

    };
  }

  /**
   * 根据ID获取供应商详情
   * @param {number} id 供应商ID
   * @returns {Promise<Object|null>} 供应商信息
   */
  async getById(id) {
    const item = await this.prisma.csmSupplier.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      },
      include: {
        brands: {
          where: { deleted_at: null }
        },
        contacts: {
          where: { deleted_at: null }
        },
        agreements: {
          where: { deleted_at: null }
        },
        attachments: {
          where: { deleted_at: null }
        },
        main_products: {
          where: { deleted_at: null },
          include: {
            main_product: true
          }
        }
      }
    });

    if (!item) return null;

    // 手动查询供应商关联关系（临时解决方案）
    try {
      console.log(`查询供应商ID ${id} 的关联关系...`);
      const supplierRelations = await this.prisma.$queryRaw`
        SELECT
          sr.id,
          sr.supplier_id,
          sr.related_supplier_id,
          sr.relation_type,
          sr.relation_desc,
          sr.status,
          sr.start_date,
          sr.end_date,
          sr.creator,
          sr.created_at,
          sr.remark,
          s.supplier_name,
          s.supplier_code,
          s.cooperation_status
        FROM csm.csm_supplier_relations sr
        LEFT JOIN csm.csm_suppliers s ON sr.related_supplier_id = s.id
        WHERE sr.supplier_id = ${BigInt(id)} and sr.deleted_at IS NULL
      `;

      console.log(`查询到 ${supplierRelations.length} 条关联关系:`, supplierRelations);

      // 将查询结果转换为期望的格式
      item.supplier_relations = supplierRelations.map(relation => ({
        id: relation.id,
        supplier_id: relation.supplier_id,
        related_supplier_id: relation.related_supplier_id,
        relation_type: relation.relation_type,
        relation_desc: relation.relation_desc,
        status: relation.status,
        start_date: relation.start_date,
        end_date: relation.end_date,
        creator: relation.creator,
        created_at: relation.created_at,
        remark: relation.remark,
        related_supplier: {
          id: relation.related_supplier_id,
          supplier_name: relation.supplier_name,
          supplier_code: relation.supplier_code,
          cooperation_status: relation.cooperation_status
        }
      }));
    } catch (error) {
      console.error('查询供应商关联关系失败:', error);
      item.supplier_relations = [];
    }

    return this.formatSupplierData(item);
  }

  /**
   * 创建供应商
   * @param {Object} data 供应商数据
   * @returns {Promise<Object>} 创建的供应商信息
   */
  async create(data) {
    const now = Date.now();

    const {
      brands = [],
      contacts = [],
      agreements = [],
      attachments = {},
      mainProducts = [],
      mainProductIds = [],
      supplierRelation = [],
      ...supplierData
    } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 调试日志 - 原始数据
      console.log('原始供应商数据:', JSON.stringify(supplierData, null, 2));
      console.log('联系人数据:', JSON.stringify(contacts, null, 2));

      // 转换驼峰命名为下划线命名，传入完整数据包括联系人信息
      const completeData = { ...supplierData, contacts };
      const dbSupplierData = this.convertCamelToSnake(completeData);

      // 调试日志 - 转换后数据
      console.log('转换后的供应商数据:', JSON.stringify(dbSupplierData, null, 2));

      // 从供应商数据中排除关联字段，这些字段需要单独创建
      const { contacts: _, brands: __, agreements: ___, attachments: ____, main_products: _____, ...cleanSupplierData } = dbSupplierData;

      // 创建供应商主记录
      const supplier = await tx.csmSupplier.create({
        data: {
          ...cleanSupplierData,
          created_at: now,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 创建品牌授权记录
      if (brands.length > 0) {
        await tx.csmSupplierBrand.createMany({
          data: brands.map(brand => ({
            supplier_id: BigInt(supplierId),
            brand_name: brand.name || brand.brand_name || '默认品牌',
            authorized: brand.authorized !== undefined ? brand.authorized : true,
            authorization_scope: brand.authorizationScope || brand.authorization_scope || null,
            remark: brand.remark || null,
            created_at: now,
            updated_at: now
          }))
        });
      }

      // 创建联系人记录
      if (contacts.length > 0) {
        await tx.csmSupplierContact.createMany({
          data: contacts.map(contact => ({
            supplier_id: BigInt(supplierId),
            name: contact.name || '默认联系人',
            phone: contact.phone || '待补充',
            position: contact.position || null,
            email: contact.email || null,
            department: contact.department || null,
            is_primary: contact.isPrimary !== undefined ? contact.isPrimary : false,
            remark: contact.remark || null,
            created_at: now,
            updated_at: now
          }))
        });
      }

      // 创建协议记录
      if (agreements.length > 0) {
        await tx.csmSupplierAgreement.createMany({
          data: agreements.map(agreement => ({
            supplier_id: BigInt(supplierId),
            agreement_type: agreement.agreementType || agreement.agreement_type || '供货协议',
            agreement_name: agreement.agreementName || agreement.agreement_name || '默认协议',
            start_date: this.convertToDate(agreement.startDate || agreement.start_date),
            end_date: this.convertToDate(agreement.endDate || agreement.end_date),
            sign_date: this.convertToDate(agreement.signDate || agreement.sign_date),
            status: agreement.status || 'pending',
            duration: agreement.duration || null,
            file_url: agreement.fileUrl || agreement.file_url || null,
            remark: agreement.remark || null,
            created_at: now,
            updated_at: now
          }))
        });
      }

      // 创建附件记录
      const attachmentList = [];

      // 处理不同类型的附件
      if (attachments.businessLicense && attachments.businessLicense.length > 0) {
        attachmentList.push(...attachments.businessLicense.map(file => ({
          supplier_id: BigInt(supplierId),
          attachment_type: 'business_license',
          file_name: file.name || '营业执照',
          file_url: file.url || '',
          file_size: file.size ? BigInt(file.size) : BigInt(0),
          file_type: file.type || 'image',
          sub_type: 'business_license',
          created_at: now,
          updated_at: now
        })));
      }

      if (attachments.bankProof && attachments.bankProof.length > 0) {
        attachmentList.push(...attachments.bankProof.map(file => ({
          supplier_id: BigInt(supplierId),
          attachment_type: 'bank_proof',
          file_name: file.name || '开户证明',
          file_url: file.url || '',
          file_size: file.size ? BigInt(file.size) : BigInt(0),
          file_type: file.type || 'image',
          sub_type: 'bank_proof',
          created_at: now,
          updated_at: now
        })));
      }

      if (attachments.idCard && attachments.idCard.length > 0) {
        attachmentList.push(...attachments.idCard.map(file => ({
          supplier_id: BigInt(supplierId),
          attachment_type: 'id_card',
          file_name: file.name || '身份证',
          file_url: file.url || '',
          file_size: file.size ? BigInt(file.size) : BigInt(0),
          file_type: file.type || 'image',
          sub_type: file.type || 'front',
          created_at: now,
          updated_at: now
        })));
      }

      if (attachments.others && attachments.others.length > 0) {
        attachmentList.push(...attachments.others.map(file => ({
          supplier_id: BigInt(supplierId),
          attachment_type: 'others',
          file_name: file.name || '其他附件',
          file_url: file.url || '',
          file_size: file.size ? BigInt(file.size) : BigInt(0),
          file_type: file.type || 'file',
          sub_type: 'others',
          created_at: now,
          updated_at: now
        })));
      }

      if (attachmentList.length > 0) {
        await tx.csmSupplierAttachment.createMany({
          data: attachmentList
        });
      }

      // 创建主营产品关联 - 支持多种字段名，优先选择有数据的字段
      let productIds = [];
      if (mainProducts && mainProducts.length > 0) {
        productIds = mainProducts;
      } else if (mainProductIds && mainProductIds.length > 0) {
        productIds = mainProductIds;
      }

      console.log('主营产品数据 - mainProducts:', mainProducts);
      console.log('主营产品数据 - mainProductIds:', mainProductIds);
      console.log('最终使用的产品ID:', productIds);

      if (productIds.length > 0) {
        // 去重处理，避免重复的产品ID
        const uniqueProductIds = [...new Set(productIds.filter(id => id))];

        // 处理每个主营产品ID
        for (const productId of uniqueProductIds) {
          if (!productId) continue;

          // 验证主营产品是否存在
          const mainProduct = await tx.csmMainProduct.findFirst({
            where: {
              id: BigInt(productId),
              deleted_at: null
            }
          });

          if (mainProduct) {
            // 创建供应商主营产品关联
            await tx.csmSupplierMainProduct.create({
              data: {
                supplier_id: supplier.id,
                main_product_id: mainProduct.id,
                created_at: now,
                updated_at: now
              }
            });
          } else {
            console.warn(`主营产品ID ${productId} 不存在或已被删除`);
          }
        }
      }

      // 创建供应商关联关系
      if (supplierRelation) {
        const creator = supplierData.submitter || '系统用户';

        // 处理不同的数据格式
        let relationIds = [];
        if (typeof supplierRelation === 'string' && supplierRelation.trim() !== '') {
          // 如果是字符串，按逗号分割转换为数组
          relationIds = supplierRelation.split(',').map(id => id.trim()).filter(id => id !== '');
          console.log('字符串格式的供应商关联转换为数组:', relationIds);
        } else if (Array.isArray(supplierRelation)) {
          // 如果已经是数组，直接使用
          relationIds = supplierRelation.filter(id => id && id !== '');
          console.log('数组格式的供应商关联:', relationIds);
        }

        if (relationIds.length > 0) {
          await this.updateSupplierRelations(supplier.id, relationIds, creator, tx);
        }
      }

      return supplier;
    });

    return this.formatSupplierData(result);
  }

  /**
   * 更新供应商
   * @param {number} id 供应商ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async update(id, data) {
    const now = Date.now();

    const {
      brands = [],
      contacts = [],
      agreements = [],
      attachments = [],
      main_product_ids = [],
      supplierRelation = [],
      // 排除联系人相关字段，这些字段只能通过更新联系人信息接口修改
      contactPerson,
      position,
      phone,
      ...supplierData
    } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 转换驼峰命名为下划线命名（只处理供应商主表字段）
      const dbData = this.convertCamelToSnake(supplierData);

      // 更新供应商主记录
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          ...dbData,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 软删除现有的关联记录
      await tx.csmSupplierBrand.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      await tx.csmSupplierContact.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      await tx.csmSupplierAgreement.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      await tx.csmSupplierAttachment.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      await tx.csmSupplierMainProduct.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      // 重新创建关联记录
      if (brands.length > 0) {
        await tx.csmSupplierBrand.createMany({
          data: brands.map(brand => ({
            supplier_id: supplierId,
            brand_name: brand.name || brand.brand_name,
            authorized: brand.authorized || false,
            authorization_date: this.convertToDate(brand.authorization_date || brand.authorizationDate),
            expiry_date: this.convertToDate(brand.expiry_date || brand.expiryDate),
            authorization_scope: brand.authorization_scope || brand.authorizationScope,
            remark: brand.remark,
            created_at: now,
            updated_at: now
          }))
        });
      }

      if (contacts.length > 0) {
        await tx.csmSupplierContact.createMany({
          data: contacts.map(contact => ({
            supplier_id: supplierId,
            ...contact,
            created_at: now,
            updated_at: now
          }))
        });
      }

      if (agreements.length > 0) {
        await tx.csmSupplierAgreement.createMany({
          data: agreements.map(agreement => ({
            supplier_id: supplierId,
            agreement_type: agreement.agreement_type || agreement.agreementType,
            agreement_name: agreement.agreement_name || agreement.agreementName,
            start_date: this.convertToDate(agreement.start_date || agreement.startDate),
            end_date: this.convertToDate(agreement.end_date || agreement.endDate),
            sign_date: this.convertToDate(agreement.sign_date || agreement.signDate),
            status: agreement.status,
            file_url: agreement.file_url || agreement.fileUrl,
            duration: agreement.duration,
            remark: agreement.description || agreement.remark,
            created_at: now,
            updated_at: now
          }))
        });
      }

      if (attachments.length > 0) {
        await tx.csmSupplierAttachment.createMany({
          data: attachments.map(attachment => ({
            supplier_id: BigInt(supplierId),
            ...attachment,
            file_size: attachment.file_size ? BigInt(attachment.file_size) : null,
            created_at: BigInt(now),
            updated_at: BigInt(now)
          }))
        });
      }

      if (main_product_ids.length > 0) {
        await tx.csmSupplierMainProduct.createMany({
          data: main_product_ids.map(productId => ({
            supplier_id: supplierId,
            main_product_id: BigInt(productId),
            created_at: now,
            updated_at: now
          }))
        });
      }

      // 更新供应商关联关系
      if (supplierRelation !== undefined) {
        const updater = supplierData.updater || '系统用户';

        // 处理不同的数据格式
        let relationIds = [];
        if (typeof supplierRelation === 'string' && supplierRelation.trim() !== '') {
          // 如果是字符串，按逗号分割转换为数组
          relationIds = supplierRelation.split(',').map(id => id.trim()).filter(id => id !== '');
          console.log('字符串格式的供应商关联转换为数组:', relationIds);
        } else if (Array.isArray(supplierRelation)) {
          // 如果已经是数组，直接使用
          relationIds = supplierRelation.filter(id => id && id !== '');
          console.log('数组格式的供应商关联:', relationIds);
        } else if (supplierRelation === '' || supplierRelation === null) {
          // 空字符串或null表示清空关联关系
          relationIds = [];
          console.log('清空供应商关联关系');
        }

        await this.updateSupplierRelations(supplierId, relationIds, updater, tx);
      }

      return supplier;
    });

    return this.formatSupplierData(result);
  }

  /**
   * 删除供应商
   * @param {number} id 供应商ID
   * @returns {Promise<boolean>} 删除结果
   */
  async delete(id) {
    const now = Date.now();

    await this.prisma.csmSupplier.update({
      where: { id: BigInt(id) },
      data: { deleted_at: now }
    });

    return true;
  }

  /**
   * 批量删除供应商
   * @param {Array<number>} ids 供应商ID数组
   * @returns {Promise<boolean>} 删除结果
   */
  async batchDelete(ids) {
    const now = Date.now();

    await this.prisma.csmSupplier.updateMany({
      where: {
        id: { in: ids.map(id => BigInt(id)) }
      },
      data: { deleted_at: now }
    });

    return true;
  }

  /**
   * 更新供应商基础信息
   * @param {number} id 供应商ID
   * @param {Object} data 基础信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateBasicInfo(id, data) {
    const now = Date.now();

    // 提取主营产品数据和其他关联字段，排除联系人相关字段
    const {
      mainProducts = [],
      mainProductIds = [],
      brands = [],
      contacts = [],
      agreements = [],
      attachments = [],
      supplierRelation = [],
      ...supplierData
    } = data;

    return await this.prisma.$transaction(async (tx) => {
      // 转换驼峰命名为下划线命名（只处理供应商主表字段）
      const dbData = this.convertCamelToSnake(supplierData);

      // 更新供应商主记录
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          ...dbData,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 处理主营产品关联
      // 使用 mainProducts 或 mainProductIds，优先使用 mainProducts
      const productIds = mainProducts.length > 0 ? mainProducts : mainProductIds;

      // 总是处理主营产品关联，即使数组为空（用于清空关联）
      // 先获取现有的关联记录
      const existingRelations = await tx.csmSupplierMainProduct.findMany({
        where: {
          supplier_id: supplierId,
          deleted_at: null
        }
      });

      // 软删除所有现有的关联
      if (existingRelations.length > 0) {
        await tx.csmSupplierMainProduct.updateMany({
          where: {
            supplier_id: supplierId,
            deleted_at: null
          },
          data: { deleted_at: now }
        });
      }

      // 如果有新的主营产品，创建关联
      if (productIds.length > 0) {
        // 去重处理，避免重复的产品ID
        const uniqueProductIds = [...new Set(productIds.filter(id => id))];

        // 处理每个主营产品ID
        for (const productId of uniqueProductIds) {
          if (!productId) continue;

          // 验证主营产品是否存在
          const mainProduct = await tx.csmMainProduct.findFirst({
            where: {
              id: BigInt(productId),
              deleted_at: null
            }
          });

          if (mainProduct) {
            // 检查是否已存在该关联（包括软删除的）
            const existingRelation = await tx.csmSupplierMainProduct.findFirst({
              where: {
                supplier_id: supplierId,
                main_product_id: mainProduct.id
              }
            });

            if (existingRelation) {
              // 如果存在，更新为未删除状态
              await tx.csmSupplierMainProduct.update({
                where: { id: existingRelation.id },
                data: {
                  deleted_at: null,
                  updated_at: now
                }
              });
            } else {
              // 如果不存在，创建新记录
              await tx.csmSupplierMainProduct.create({
                data: {
                  supplier_id: supplierId,
                  main_product_id: mainProduct.id,
                  created_at: now,
                  updated_at: now
                }
              });
            }
          } else {
            console.warn(`主营产品ID ${productId} 不存在或已被删除`);
          }
        }
      }

      // 更新供应商关联关系
      if (supplierRelation !== undefined) {
        const updater = supplierData.updater || '系统用户';

        // 处理不同的数据格式
        let relationIds = [];
        if (typeof supplierRelation === 'string' && supplierRelation.trim() !== '') {
          // 如果是字符串，按逗号分割转换为数组
          relationIds = supplierRelation.split(',').map(id => id.trim()).filter(id => id !== '');
          console.log('字符串格式的供应商关联转换为数组:', relationIds);
        } else if (Array.isArray(supplierRelation)) {
          // 如果已经是数组，直接使用
          relationIds = supplierRelation.filter(id => id && id !== '');
          console.log('数组格式的供应商关联:', relationIds);
        } else if (supplierRelation === '' || supplierRelation === null) {
          // 空字符串或null表示清空关联关系
          relationIds = [];
          console.log('清空供应商关联关系');
        }

        await this.updateSupplierRelations(supplierId, relationIds, updater, tx);
      }

      return supplier;
    }).then(async (supplier) => {
      // 查询完整的供应商信息（包括主营产品）
      const fullSupplier = await this.getById(Number(supplier.id));
      return fullSupplier;
    });
  }

  /**
   * 更新供应商账户信息
   * @param {number} id 供应商ID
   * @param {Object} data 账户信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAccountInfo(id, data) {
    const now = Date.now();

    // 排除联系人相关字段，这些字段只能通过更新联系人信息接口修改
    const {
      ...accountData
    } = data;

    // 转换驼峰命名为下划线命名
    const dbData = this.convertCamelToSnake(accountData);

    const result = await this.prisma.csmSupplier.update({
      where: { id: BigInt(id) },
      data: {
        ...dbData,
        updated_at: now
      }
    });

    return this.formatSupplierData(result);
  }

  /**
   * 更新供应商品牌信息
   * @param {number} id 供应商ID
   * @param {Object} data 品牌信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateBrandInfo(id, data) {
    const now = Date.now();
    const { brands = [], updater, updateDate } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 更新供应商主记录的更新信息
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          updater,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 软删除现有的品牌记录
      await tx.csmSupplierBrand.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      // 创建新的品牌记录
      if (brands.length > 0) {
        const brandData = brands.map(brand => ({
          supplier_id: supplierId,
          brand_name: brand.name || brand.brand_name,
          authorized: brand.authorized || false,
          authorization_date: this.convertToDate(brand.authorizationDate),
          expiry_date: this.convertToDate(brand.expiryDate),
          authorization_scope: brand.authorizationScope,
          remark: brand.remark,
          created_at: now,
          updated_at: now
        }));

        await tx.csmSupplierBrand.createMany({
          data: brandData
        });
      }

      return supplier;
    });

    return this.formatSupplierData(result);
  }

  /**
   * 更新供应商联系人信息
   * @param {number} id 供应商ID
   * @param {Object} data 联系人信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateContactInfo(id, data) {
    const now = Date.now();
    const { contacts = [], updater, updateDate } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 确定主要联系人信息
      let primaryContactInfo = {
        contact_person: '待补充',
        phone: '待补充',
        position: null
      };

      if (contacts.length > 0) {
        // 查找主要联系人，如果没有则使用第一个联系人
        const primaryContact = contacts.find(c => c.isPrimary) || contacts[0];

        if (primaryContact) {
          primaryContactInfo = {
            contact_person: primaryContact.name || '待补充',
            phone: primaryContact.phone || '待补充',
            position: primaryContact.position || null
          };
        }
      }

      // 更新供应商主记录（包括主要联系人信息）
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          ...primaryContactInfo,
          updater,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 软删除现有的联系人记录
      await tx.csmSupplierContact.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      // 创建新的联系人记录
      if (contacts.length > 0) {
        const contactData = contacts.map(contact => ({
          supplier_id: supplierId,
          name: contact.name,
          phone: contact.phone,
          position: contact.position,
          email: contact.email,
          department: contact.department,
          is_primary: contact.isPrimary || false,
          remark: contact.remark,
          created_at: now,
          updated_at: now
        }));

        await tx.csmSupplierContact.createMany({
          data: contactData
        });
      }

      return supplier;
    });

    // 返回完整的供应商信息
    return await this.getById(Number(result.id));
  }

  /**
   * 更新供应商协议信息
   * @param {number} id 供应商ID
   * @param {Object} data 协议信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAgreementInfo(id, data) {
    const now = Date.now();
    const { agreements = [], updater, updateDate } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 更新供应商主记录的更新信息
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          updater,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 软删除现有的协议记录
      await tx.csmSupplierAgreement.updateMany({
        where: { supplier_id: supplierId },
        data: { deleted_at: now }
      });

      // 创建新的协议记录
      if (agreements.length > 0) {
        const agreementData = agreements.map(agreement => ({
          supplier_id: supplierId,
          agreement_type: agreement.agreementType,
          agreement_name: agreement.agreementName,
          start_date: this.convertToDate(agreement.startDate),
          end_date: this.convertToDate(agreement.endDate),
          sign_date: this.convertToDate(agreement.signDate),
          status: agreement.status,
          file_url: agreement.fileUrl,
          duration: agreement.duration,
          remark: agreement.description || agreement.remark,
          created_at: now,
          updated_at: now
        }));

        await tx.csmSupplierAgreement.createMany({
          data: agreementData
        });
      }

      return supplier;
    });

    return this.formatSupplierData(result);
  }

  /**
   * 更新供应商附件信息
   * @param {number} id 供应商ID
   * @param {Object} data 附件信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAttachmentInfo(id, data) {
    const now = Date.now();
    const { attachments = {}, updater, updateDate } = data;

    const result = await this.prisma.$transaction(async (tx) => {
      // 更新供应商主记录的更新信息和备注
      const supplier = await tx.csmSupplier.update({
        where: { id: BigInt(id) },
        data: {
          updater,
          remark: attachments.remark || null,
          updated_at: now
        }
      });

      const supplierId = supplier.id;

      // 获取现有的附件记录
      const existingAttachments = await tx.csmSupplierAttachment.findMany({
        where: {
          supplier_id: supplierId,
          deleted_at: null
        }
      });

      // 收集所有前端传来的附件ID
      const frontendAttachmentIds = new Set();
      const attachmentUpdates = [];
      const attachmentCreates = [];

      // 处理营业执照
      if (attachments.businessLicense && attachments.businessLicense.length > 0) {
        attachments.businessLicense.forEach(file => {
          if (file.id) {
            // 已存在的附件，进行更新
            frontendAttachmentIds.add(Number(file.id));
            attachmentUpdates.push({
              id: BigInt(file.id),
              data: {
                file_name: file.name,
                file_url: file.url,
                file_size: file.size ? BigInt(file.size) : null,
                updated_at: BigInt(now)
              }
            });
          } else {
            // 新附件，进行创建
            attachmentCreates.push({
              supplier_id: BigInt(supplierId),
              attachment_type: 'business_license',
              file_name: file.name,
              file_url: file.url,
              file_size: file.size ? BigInt(file.size) : null,
              created_at: BigInt(now),
              updated_at: BigInt(now)
            });
          }
        });
      }

      // 处理开户证明
      if (attachments.bankProof && attachments.bankProof.length > 0) {
        attachments.bankProof.forEach(file => {
          if (file.id) {
            frontendAttachmentIds.add(Number(file.id));
            attachmentUpdates.push({
              id: BigInt(file.id),
              data: {
                file_name: file.name,
                file_url: file.url,
                file_size: file.size ? BigInt(file.size) : null,
                updated_at: BigInt(now)
              }
            });
          } else {
            attachmentCreates.push({
              supplier_id: BigInt(supplierId),
              attachment_type: 'bank_proof',
              file_name: file.name,
              file_url: file.url,
              file_size: file.size ? BigInt(file.size) : null,
              created_at: BigInt(now),
              updated_at: BigInt(now)
            });
          }
        });
      }

      // 处理身份证
      if (attachments.idCard && attachments.idCard.length > 0) {
        attachments.idCard.forEach(file => {
          if (file.id) {
            frontendAttachmentIds.add(Number(file.id));
            attachmentUpdates.push({
              id: BigInt(file.id),
              data: {
                file_name: file.name,
                file_url: file.url,
                file_size: file.size ? BigInt(file.size) : null,
                attachment_type: file.type === 'front' ? 'id_card_front' : 'id_card_back',
                updated_at: BigInt(now)
              }
            });
          } else {
            attachmentCreates.push({
              supplier_id: BigInt(supplierId),
              attachment_type: file.type === 'front' ? 'id_card_front' : 'id_card_back',
              file_name: file.name,
              file_url: file.url,
              file_size: file.size ? BigInt(file.size) : null,
              created_at: BigInt(now),
              updated_at: BigInt(now)
            });
          }
        });
      }

      // 处理其他附件
      if (attachments.others && attachments.others.length > 0) {
        attachments.others.forEach(file => {
          if (file.id) {
            frontendAttachmentIds.add(Number(file.id));
            attachmentUpdates.push({
              id: BigInt(file.id),
              data: {
                file_name: file.name,
                file_url: file.url,
                file_size: file.size ? BigInt(file.size) : null,
                updated_at: BigInt(now)
              }
            });
          } else {
            attachmentCreates.push({
              supplier_id: BigInt(supplierId),
              attachment_type: 'others',
              file_name: file.name,
              file_url: file.url,
              file_size: file.size ? BigInt(file.size) : null,
              created_at: BigInt(now),
              updated_at: BigInt(now)
            });
          }
        });
      }

      // 执行更新操作
      for (const update of attachmentUpdates) {
        await tx.csmSupplierAttachment.update({
          where: { id: update.id },
          data: update.data
        });
      }

      // 执行创建操作
      if (attachmentCreates.length > 0) {
        await tx.csmSupplierAttachment.createMany({
          data: attachmentCreates
        });
      }

      // 软删除不在前端数据中的附件
      const attachmentsToDelete = existingAttachments.filter(
        attachment => !frontendAttachmentIds.has(Number(attachment.id))
      );

      if (attachmentsToDelete.length > 0) {
        await tx.csmSupplierAttachment.updateMany({
          where: {
            id: { in: attachmentsToDelete.map(att => att.id) }
          },
          data: { deleted_at: BigInt(now) }
        });
      }

      return supplier;
    });

    // 获取更新后的完整供应商数据（包含附件信息）
    const updatedSupplier = await this.getById(id);
    return updatedSupplier;
  }

  /**
   * 更换复核人
   * @param {number} id 供应商ID
   * @param {string} reviewer 复核人
   * @param {string} updater 更新人
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async changeReviewer(id, reviewer, updater) {
    const now = Date.now();

    const supplier = await this.prisma.csmSupplier.update({
      where: { id: BigInt(id) },
      data: {
        reviewer,
        updater,
        updated_at: now
      }
    });

    return this.formatSupplierData(supplier);
  }

  /**
   * 转换日期字符串为Date对象或null
   * @param {string|Date|null} dateValue 日期值
   * @returns {Date|null} 转换后的日期
   */
  convertToDate(dateValue) {
    if (!dateValue || dateValue === '') {
      return null;
    }

    try {
      const date = new Date(dateValue);
      return isNaN(date.getTime()) ? null : date;
    } catch (e) {
      return null;
    }
  }

  /**
   * 将驼峰命名转换为下划线命名
   * @param {Object} data 驼峰命名的数据
   * @returns {Object} 下划线命名的数据
   */
  convertCamelToSnake(data) {
    const converted = {};
    const fieldMapping = {
      supplierName: 'supplier_name',
      supplierCode: 'supplier_code',
      contactPerson: 'contact_person',
      companyNature: 'company_nature',
      cooperationType: 'cooperation_type',
      cooperationStatus: 'cooperation_status',
      cooperationAgreement: 'cooperation_agreement',
      supplierGroup: 'supplier_group',
      detailedAddress: 'detailed_address',
      productionAddress: 'production_address',
      registerTime: 'register_time',
      creditCode: 'credit_code',
      registeredCapital: 'registered_capital',
      orderCount: 'order_count',
      paymentTerms: 'payment_terms',
      invoiceType: 'invoice_type',
      taxRate: 'tax_rate',
      accountName: 'account_name',
      settlementMethod: 'settlement_method',
      accountNumber: 'account_number',
      bankName: 'bank_name',
      submitDate: 'submit_date',
      updateDate: 'updated_at'  // 修正字段名
    };

    // 需要排除的关联字段，这些字段不属于主表
    const excludedFields = [
      'mainProducts', 'main_products',
      'brands', 'contacts', 'agreements', 'attachments',
      'main_product_ids', 'supplierRelation', 'supplier_relation'
    ];

    for (const [key, value] of Object.entries(data)) {
      // 跳过关联字段
      if (excludedFields.includes(key)) {
        continue;
      }

      const dbKey = fieldMapping[key] || key;

      // 处理特殊字段的数据转换
      let convertedValue = value;

      // 处理日期字段
      if (dbKey === 'register_time' || dbKey === 'submit_date') {
        if (value && value !== '') {
          try {
            // 转换为Date对象，Prisma会自动处理
            convertedValue = new Date(value);
            // 检查日期是否有效
            if (isNaN(convertedValue.getTime())) {
              convertedValue = null;
            }
          } catch (e) {
            convertedValue = null;
          }
        } else {
          convertedValue = null;
        }
      }

      // 处理数字字段
      if (dbKey === 'registered_capital') {
        if (value && value !== '') {
          convertedValue = parseFloat(value);
        } else {
          convertedValue = null;
        }
      }

      if (dbKey === 'order_count') {
        if (value && value !== '') {
          convertedValue = parseInt(value);
        } else {
          convertedValue = null;
        }
      }

      if (dbKey === 'tax_rate') {
        if (value !== undefined && value !== null && value !== '') {
          convertedValue = parseFloat(value);
        } else {
          convertedValue = null;
        }
      }

      // 处理空字符串，转换为null
      if (convertedValue === '') {
        convertedValue = null;
      }

      converted[dbKey] = convertedValue;
    }

    // 从联系人信息中提取主要联系人信息到供应商主表
    if (data.contacts && data.contacts.length > 0) {
      // 查找主要联系人
      const primaryContact = data.contacts.find(c => c.isPrimary) || data.contacts[0];
      console.log('找到主要联系人:', primaryContact);

      if (primaryContact) {
        converted.contact_person = primaryContact.name || '待补充';
        converted.phone = primaryContact.phone || '待补充';
        converted.position = primaryContact.position || null;

        console.log('设置联系人信息:', {
          contact_person: converted.contact_person,
          phone: converted.phone,
          position: converted.position
        });
      }
    } else {
      console.log('没有联系人信息，使用供应商信息中的联系人信息',data);
      // 如果没有联系人信息，使用默认值
      converted.contact_person = data.contactPerson || '待补充';
      converted.phone = data.phone || '待补充';
      converted.position = data.position || null;
    }

    // 确保所有必填字段都有值
    const requiredDefaults = {
      supplier_name: converted.supplier_name || '待补充',
      supplier_code: converted.supplier_code || 'SUP' + Date.now(),
      contact_person: converted.contact_person || '待补充',
      phone: converted.phone || '待补充',
      company_nature: converted.company_nature || '有限责任公司',
      address: converted.address || '待补充',
      cooperation_type: converted.cooperation_type || '长期合作',
      cooperation_status: converted.cooperation_status || '待审核',
      payment_terms: converted.payment_terms || '月结30天',
      department: converted.department || '采购部',
      submitter: converted.submitter || '系统用户',
      submit_date: converted.submit_date || new Date()
    };

    // 应用默认值
    Object.assign(converted, requiredDefaults);

    return converted;
  }

  /**
   * 格式化附件数据，按类型分组
   * @param {Array} attachments 附件数组
   * @returns {Object} 按类型分组的附件对象
   */
  formatAttachments(attachments) {
    const result = {
      businessLicense: [],
      bankProof: [],
      idCard: [],
      others: [],
      remark: ''
    };

    attachments.forEach(attachment => {
      const formattedAttachment = {
        id: Number(attachment.id),
        name: attachment.file_name,
        url: attachment.file_url,
        size: attachment.file_size ? Number(attachment.file_size) : 0,
        type: attachment.sub_type || attachment.file_type,
        fileType: attachment.file_type
      };

      switch (attachment.attachment_type) {
        case 'business_license':
          result.businessLicense.push(formattedAttachment);
          break;
        case 'bank_proof':
          result.bankProof.push(formattedAttachment);
          break;
        case 'id_card':
        case 'id_card_front':
          // 身份证正面
          formattedAttachment.type = 'front';
          result.idCard.push(formattedAttachment);
          break;
        case 'id_card_back':
          // 身份证反面
          formattedAttachment.type = 'back';
          result.idCard.push(formattedAttachment);
          break;
        case 'others':
        default:
          result.others.push(formattedAttachment);
          break;
      }
    });

    return result;
  }

  /**
   * 格式化供应商数据
   * @param {Object} item 原始数据
   * @returns {Object} 格式化后的数据
   */
  formatSupplierData(item) {
    return {
      id: Number(item.id),
      supplierName: item.supplier_name,
      supplierCode: item.supplier_code,
      contactPerson: item.contact_person,
      position: item.position,
      phone: item.phone,
      companyNature: item.company_nature,
      address: item.address,
      detailedAddress: item.detailed_address,
      productionAddress: item.production_address,
      cooperationType: item.cooperation_type,
      cooperationStatus: item.cooperation_status,
      paymentTerms: item.payment_terms,
      supplierGroup: item.supplier_group,
      creditCode: item.credit_code,
      registeredCapital: item.registered_capital,
      registerTime: item.register_time,
      cooperationAgreement: item.cooperation_agreement,
      orderCount: item.order_count,
      invoiceType: item.invoice_type,
      taxRate: item.tax_rate,
      accountName: item.account_name,
      settlementMethod: item.settlement_method,
      accountNumber: item.account_number,
      bankName: item.bank_name,
      department: item.department,
      submitter: item.submitter,
      submitDate: item.submit_date,
      updater: item.updater,
      reviewer: item.reviewer,
      remark: item.remark,
      createdAt: Number(item.created_at),
      updatedAt: Number(item.updated_at),
      deletedAt: item.deleted_at ? Number(item.deleted_at) : null,
      // 格式化关联数据
      brands: item.brands ? item.brands.map(brand => ({
        id: Number(brand.id),
        supplierId: brand.supplier_id ? Number(brand.supplier_id) : null,
        name: brand.brand_name,
        authorized: brand.authorized,
        authorizationDate: brand.authorization_date,
        expiryDate: brand.expiry_date,
        authorizationScope: brand.authorization_scope,
        remark: brand.remark,
        createdAt: brand.created_at ? Number(brand.created_at) : null,
        updatedAt: brand.updated_at ? Number(brand.updated_at) : null,
        deletedAt: brand.deleted_at ? Number(brand.deleted_at) : null
      })) : [],
      contacts: item.contacts ? item.contacts.map(contact => ({
        id: Number(contact.id),
        supplierId: contact.supplier_id ? Number(contact.supplier_id) : null,
        name: contact.name,
        phone: contact.phone,
        position: contact.position,
        email: contact.email,
        department: contact.department,
        isPrimary: contact.is_primary,
        remark: contact.remark,
        createdAt: contact.created_at ? Number(contact.created_at) : null,
        updatedAt: contact.updated_at ? Number(contact.updated_at) : null,
        deletedAt: contact.deleted_at ? Number(contact.deleted_at) : null
      })) : [],
      agreements: item.agreements ? item.agreements.map(agreement => ({
        id: Number(agreement.id),
        supplierId: agreement.supplier_id ? Number(agreement.supplier_id) : null,
        agreementType: agreement.agreement_type,
        agreementName: agreement.agreement_name,
        startDate: agreement.start_date,
        endDate: agreement.end_date,
        duration: agreement.duration,
        status: agreement.status,
        signDate: agreement.sign_date,
        fileUrl: agreement.file_url,
        remark: agreement.remark,
        createdAt: agreement.created_at ? Number(agreement.created_at) : null,
        updatedAt: agreement.updated_at ? Number(agreement.updated_at) : null,
        deletedAt: agreement.deleted_at ? Number(agreement.deleted_at) : null
      })) : [],
      attachments: this.formatAttachments(item.attachments || []),
      mainProducts: item.main_products ? item.main_products.map(mp => ({
        id: mp.main_product ? Number(mp.main_product.id) : null,
        name: mp.main_product ? mp.main_product.product_name : '未知产品'
      })).filter(product => product.id !== null) : [],
      // 为了兼容前端，同时提供ID数组
      mainProductIds: item.main_products ? item.main_products.map(mp =>
        mp.main_product ? Number(mp.main_product.id) : null
      ).filter(id => id !== null) : [],
      // 供应商关联关系
      supplierRelations: item.supplier_relations ? item.supplier_relations.map(relation => ({
        id: Number(relation.id),
        relatedSupplierId: Number(relation.related_supplier_id),
        relatedSupplierName: relation.related_supplier ? relation.related_supplier.supplier_name : '',
        relatedSupplierCode: relation.related_supplier ? relation.related_supplier.supplier_code : '',
        relationType: relation.relation_type,
        relationDesc: relation.relation_desc,
        status: relation.status,
        startDate: relation.start_date,
        endDate: relation.end_date,
        creator: relation.creator,
        createdAt: relation.created_at ? Number(relation.created_at) : null,
        remark: relation.remark
      })) : [],
      // 为了兼容前端，提供关联供应商ID数组（字符串格式，便于前端select组件使用）
      supplierRelation: item.supplier_relations ? item.supplier_relations.map(relation =>
        String(relation.related_supplier_id)
      ) : []
    };
  }

  /**
   * 更新提交人信息
   * @param {number} id 供应商ID
   * @param {Object} data 提交人信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateSubmitterInfo(id, data) {
    const now = BigInt(Date.now());

    // 转换驼峰命名为下划线命名
    const updateData = {};

    if (data.department !== undefined) {
      updateData.department = data.department;
    }

    if (data.submitter !== undefined) {
      updateData.submitter = data.submitter;
    }

    if (data.submitTime !== undefined) {
      updateData.submit_date = data.submitTime ? new Date(data.submitTime) : null;
    }

    if (data.reviewer !== undefined) {
      updateData.reviewer = data.reviewer;
    }

    // 注意：根据Prisma模式，reviewer_id字段不存在，所以我们只更新reviewer字段
    // 如果需要存储reviewer_id，需要先在数据库模式中添加该字段

    // 添加更新时间和更新人
    if (data.updater) {
      updateData.updater = data.updater;
    }

    if (data.updateDate) {
      updateData.updated_at = BigInt(new Date(data.updateDate).getTime());
    } else {
      updateData.updated_at = now;
    }

    // 更新供应商记录
    const updatedSupplier = await this.prisma.csmSupplier.update({
      where: { id: BigInt(id) },
      data: updateData
    });

    // 返回格式化的数据
    return this.formatSupplierData(updatedSupplier);
  }

  /**
   * 获取供应商统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      // 获取各状态的供应商数量
      const stats = await this.prisma.csmSupplier.groupBy({
        by: ['cooperation_status'],
        where: {
          deleted_at: null
        },
        _count: {
          id: true
        }
      });

      // 格式化统计结果
      const result = {
        total: 0,
        pending: 0,  // 待审核
        approved: 0, // 启用
        disabled: 0, // 不启用
        blacklist: 0 // 黑名单
      };

      // 计算各状态数量
      stats.forEach(stat => {
        const count = stat._count.id;
        result.total += count;

        switch (stat.cooperation_status) {
          case '待审核':
            result.pending = count;
            break;
          case '启用':
            result.approved = count;
            break;
          case '不启用':
            result.disabled = count;
            break;
          case '黑名单':
            result.blacklist = count;
            break;
        }
      });

      return result;
    } catch (error) {
      console.error('获取供应商统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商关联关系
   * @param {number} supplierId 供应商ID
   * @param {Array} relationIds 关联供应商ID数组
   * @param {string} creator 创建人
   * @param {Object} tx 事务对象（可选）
   * @returns {Promise<void>}
   */
  async updateSupplierRelations(supplierId, relationIds = [], creator = '系统用户', tx = null) {
    const prisma = tx || this.prisma;
    const now = Date.now();

    console.log(`更新供应商 ${supplierId} 的关联关系:`, relationIds);

    try {
      // 先软删除现有的关联关系
      const deleteResult = await prisma.csmSupplierRelation.updateMany({
        where: {
          supplier_id: BigInt(supplierId),
          deleted_at: null
        },
        data: {
          deleted_at: now,
          updated_at: now
        }
      });
      console.log(`软删除现有关联关系，影响行数:`, deleteResult.count);

      // 创建新的关联关系
      if (relationIds && relationIds.length > 0) {
        const validRelationIds = relationIds.filter(id => id && id !== '' && !isNaN(id));
        if (validRelationIds.length > 0) {

          // 对于每个关联关系，先尝试恢复已软删除的记录，如果不存在则创建新记录
          for (const relatedId of validRelationIds) {
            try {
              // 先尝试恢复已软删除的记录
              const updateResult = await prisma.$executeRaw`
                UPDATE csm.csm_supplier_relations
                SET deleted_at = NULL, updated_at = ${now}, updater = ${creator}
                WHERE supplier_id = ${BigInt(supplierId)}
                AND related_supplier_id = ${BigInt(relatedId)}
                AND deleted_at IS NOT NULL
              `;

              if (updateResult > 0) {
                console.log(`恢复已软删除的关联关系: ${supplierId} -> ${relatedId}`);
              } else {
                // 如果没有已软删除的记录，创建新记录
                const insertResult = await prisma.$executeRaw`
                  INSERT INTO csm.csm_supplier_relations
                  (supplier_id, related_supplier_id, relation_type, status, creator, created_at, updated_at)
                  VALUES (${BigInt(supplierId)}, ${BigInt(relatedId)}, '关联供应商', 'active', ${creator}, ${now}, ${now})
                `;
                console.log(`创建新的关联关系: ${supplierId} -> ${relatedId}, 影响行数:`, insertResult);
              }
            } catch (sqlError) {
              console.error(`处理关联关系失败 ${supplierId} -> ${relatedId}:`, sqlError);
            }
          }
        }
      } else {
        console.log(`供应商 ${supplierId} 的关联关系已清空（传入空数组或无效数组）`);
      }

      // 验证创建结果
      const finalRelations = await prisma.$queryRaw`
        SELECT COUNT(*) as count FROM csm.csm_supplier_relations
        WHERE supplier_id = ${BigInt(supplierId)} AND deleted_at IS NULL
      `;
      console.log(`最终的关联关系数量:`, finalRelations[0]?.count || 0);

    } catch (error) {
      console.error('更新供应商关联关系失败:', error);
      throw error;
    }
  }

  /**
   * 获取供应商的关联关系
   * @param {number} supplierId 供应商ID
   * @returns {Promise<Array>} 关联关系列表
   */
  async getSupplierRelations(supplierId) {
    try {
      const relations = await this.prisma.csmSupplierRelation.findMany({
        where: {
          supplier_id: BigInt(supplierId),
          deleted_at: null
        },
        include: {
          related_supplier: {
            select: {
              id: true,
              supplier_name: true,
              supplier_code: true,
              cooperation_status: true
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      return relations.map(relation => ({
        id: relation.id,
        relatedSupplierId: relation.related_supplier_id,
        relatedSupplierName: relation.related_supplier.supplier_name,
        relatedSupplierCode: relation.related_supplier.supplier_code,
        relationType: relation.relation_type,
        relationDesc: relation.relation_desc,
        status: relation.status,
        startDate: relation.start_date,
        endDate: relation.end_date,
        creator: relation.creator,
        createdAt: relation.created_at,
        remark: relation.remark
      }));
    } catch (error) {
      console.error('获取供应商关联关系失败:', error);
      throw error;
    }
  }
}

module.exports = SupplierModel;
