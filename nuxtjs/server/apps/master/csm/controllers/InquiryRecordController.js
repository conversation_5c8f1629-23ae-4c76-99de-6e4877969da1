const InquiryRecordService = require('../services/InquiryRecordService');
const BaseController = require('../../../../core/controllers/BaseController');

/**
 * 询价记录控制器
 */
class InquiryRecordController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.inquiryRecordService = new InquiryRecordService(prisma);
  }

  /**
   * 创建询价记录
   */
  async createInquiryRecord(req, res) {
    try {
      const data = req.body;

      // 验证必填字段
      if (!data.productName) {
        return res.status(400).json({
          code: 400,
          message: '商品名称不能为空'
        });
      }

      if (!data.currentPrice) {
        return res.status(400).json({
          code: 400,
          message: '报价不能为空'
        });
      }

      if (!data.supplier) {
        return res.status(400).json({
          code: 400,
          message: '供应商不能为空'
        });
      }

      if (!data.inquirer) {
        return res.status(400).json({
          code: 400,
          message: '询价员不能为空'
        });
      }

      if (data.includeTax === undefined || data.includeTax === null) {
        return res.status(400).json({
          code: 400,
          message: '请选择是否含税'
        });
      }

      if (data.includeShipping === undefined || data.includeShipping === null) {
        return res.status(400).json({
          code: 400,
          message: '请选择是否含运费'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const result = await this.inquiryRecordService.createInquiryRecord(data, userId);
      res.json(result);
    } catch (error) {
      console.error('创建询价记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '创建询价记录失败'
      });
    }
  }

  /**
   * 获取询价记录列表
   */
  async getInquiryRecords(req, res) {
    try {
      const params = req.query;
      console.log('Controller接收到的查询参数:', params);
      const result = await this.inquiryRecordService.getInquiryRecords(params);
      res.json(result);
    } catch (error) {
      console.error('获取询价记录列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取询价记录列表失败'
      });
    }
  }

  /**
   * 获取询价记录详情
   */
  async getInquiryRecordDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '询价记录ID不能为空'
        });
      }

      const result = await this.inquiryRecordService.getInquiryRecordDetail(id);
      res.json(result);
    } catch (error) {
      console.error('获取询价记录详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取询价记录详情失败'
      });
    }
  }

  /**
   * 更新询价记录
   */
  async updateInquiryRecord(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '询价记录ID不能为空'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const result = await this.inquiryRecordService.updateInquiryRecord(id, data, userId);
      res.json(result);
    } catch (error) {
      console.error('更新询价记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新询价记录失败'
      });
    }
  }

  /**
   * 删除询价记录
   */
  async deleteInquiryRecord(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '询价记录ID不能为空'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const result = await this.inquiryRecordService.deleteInquiryRecord(id, userId);
      res.json(result);
    } catch (error) {
      console.error('删除询价记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除询价记录失败'
      });
    }
  }

  /**
   * 批量删除询价记录
   */
  async batchDeleteInquiryRecords(req, res) {
    try {
      const { ids } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '请选择要删除的记录'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const results = [];
      for (const id of ids) {
        try {
          const result = await this.inquiryRecordService.deleteInquiryRecord(id, userId);
          results.push({ id, success: true, result });
        } catch (error) {
          results.push({ id, success: false, error: error.message });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      res.json({
        code: 200,
        message: `批量删除完成，成功：${successCount}条，失败：${failCount}条`,
        data: {
          total: ids.length,
          success: successCount,
          failed: failCount,
          results
        }
      });
    } catch (error) {
      console.error('批量删除询价记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '批量删除询价记录失败'
      });
    }
  }

  /**
   * 根据SKU ID获取关联的供应商信息
   */
  async getSuppliersBySkuId(req, res) {
    try {
      const { skuId } = req.params;

      if (!skuId) {
        return res.status(400).json({
          code: 400,
          message: 'SKU ID不能为空'
        });
      }

      // 查询询价记录，只查询必要字段
      const inquiryRecords = await this.prisma.csm_inquiry_records.findMany({
        where: {
          product_code: skuId,
          deleted_at: null
        },
        select: {
          id: true,
          product_name: true,
          product_code: true,
          product_image: true,
          current_price: true,
          include_tax: true,
          include_shipping: true,
          supplier_id: true,
          supplier_name: true,
          supplier_code: true
        },
        orderBy: {
          current_price: 'asc'  // 按报价金额升序排序
        }
      });

      if (inquiryRecords.length === 0) {
        // 构建标准分页响应格式
        const response = {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: 1,
            totalPage: 0
          }
        };

        // 直接使用success方法返回数据
        return this.success(res, response, '未找到相关询价记录');
      }

      // 格式化报价信息
      const quotationList = inquiryRecords.map(record => ({
        // 报价信息
        quotationId: Number(record.id),
        productName: record.product_name,
        productCode: record.product_code,
        productImage: record.product_image,
        currentPrice: Number(record.current_price),
        includeTax: record.include_tax,
        includeShipping: record.include_shipping,
        priceDescription: `${Number(record.current_price)}元${record.include_tax ? '(含税)' : '(不含税)'}${record.include_shipping ? '(含运费)' : '(不含运费)'}`,

        // 供应商基本信息
        supplierInfo: {
          id: record.supplier_id ? Number(record.supplier_id) : null,
          supplierCode: record.supplier_code,
          supplierName: record.supplier_name
        }
      }));

      // 构建标准分页响应格式
      const response = {
        items: quotationList,
        pageInfo: {
          total: inquiryRecords.length,
          currentPage: 1,
          totalPage: 1
        }
      };

      // 直接使用success方法返回数据
      this.success(res, response, '获取SKU关联报价信息成功');
    } catch (error) {
      console.error('根据SKU ID获取供应商信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '根据SKU ID获取供应商信息失败'
      });
    }
  }

  /**
   * 获取询价记录统计信息
   */
  async getInquiryRecordStats(req, res) {
    try {
      const { startTime, endTime } = req.query;

      // 构建查询条件
      const where = {
        deleted_at: null
      };

      if (startTime && endTime) {
        where.inquiry_time = {
          gte: BigInt(new Date(startTime).getTime()),
          lte: BigInt(new Date(endTime).getTime())
        };
      }

      // 统计总数
      const total = await this.prisma.csm_inquiry_records.count({ where });

      // 按状态统计
      const statusStats = await this.prisma.csm_inquiry_records.groupBy({
        by: ['record_status'],
        where,
        _count: {
          id: true
        }
      });

      // 按供应商统计
      const supplierStats = await this.prisma.csm_inquiry_records.groupBy({
        by: ['supplier_name'],
        where,
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 10
      });

      res.json({
        code: 200,
        message: '获取统计信息成功',
        data: {
          total,
          statusStats: statusStats.map(item => ({
            status: item.record_status,
            count: item._count.id
          })),
          supplierStats: supplierStats.map(item => ({
            supplierName: item.supplier_name,
            count: item._count.id
          }))
        }
      });
    } catch (error) {
      console.error('获取询价记录统计信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取询价记录统计信息失败'
      });
    }
  }
}

module.exports = InquiryRecordController;
