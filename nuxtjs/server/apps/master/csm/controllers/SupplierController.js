const SupplierService = require('../services/SupplierService');
const SupplierDto = require('../dto/SupplierDto');

/**
 * 供应商控制器
 */
class SupplierController {
  constructor() {
    this.supplierService = new SupplierService();
  }

  /**
   * 获取供应商列表
   */
  async getList(req, res) {
    try {
      const params = req.query;

      // 转换数值类型参数
      if (params.page) params.page = parseInt(params.page);
      if (params.pageSize) params.pageSize = parseInt(params.pageSize);

      // 参数验证
      const errors = this.validateParams(params, SupplierDto.queryRules());
      if (errors.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors
        });
      }

      const result = await this.supplierService.getList(params);
      
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取供应商列表失败'
      });
    }
  }

  /**
   * 根据ID获取供应商详情
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      const result = await this.supplierService.getById(parseInt(id));
      
      if (!result) {
        return res.status(404).json({
          code: 404,
          message: '供应商不存在'
        });
      }

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取供应商详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取供应商详情失败'
      });
    }
  }

  /**
   * 创建供应商
   */
  async create(req, res) {
    try {
      const data = req.body;

      // 获取当前用户信息
      const currentUser = req.user; // 假设用户信息在req.user中

      // 设置提交人信息
      if (currentUser) {
        data.submitter = currentUser.nickname || currentUser.username || '系统用户';
        data.submitDate = new Date().toISOString();
      } else {
        data.submitter = '系统用户';
        data.submitDate = new Date().toISOString();
      }

      // 参数验证
      const errors = this.validateParams(data, SupplierDto.createRules());
      if (errors.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors
        });
      }

      const result = await this.supplierService.create(data);
      
      res.status(201).json({
        code: 201,
        message: '创建成功',
        data: result
      });
    } catch (error) {
      console.error('创建供应商失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '创建供应商失败'
      });
    }
  }

  /**
   * 更新供应商
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      // 参数验证
      const errors = this.validateParams(data, SupplierDto.updateRules());
      if (errors.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors
        });
      }

      const result = await this.supplierService.update(parseInt(id), data);
      
      res.json({
        code: 200,
        message: '更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新供应商失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新供应商失败'
      });
    }
  }

  /**
   * 删除供应商
   */
  async delete(req, res) {
    try {
      const { id } = req.params;
      
      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      await this.supplierService.delete(parseInt(id));
      
      res.json({
        code: 200,
        message: '删除成功'
      });
    } catch (error) {
      console.error('删除供应商失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除供应商失败'
      });
    }
  }

  /**
   * 批量删除供应商
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID数组不能为空'
        });
      }

      // 验证ID格式
      const invalidIds = ids.filter(id => isNaN(id));
      if (invalidIds.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '存在格式不正确的供应商ID'
        });
      }

      await this.supplierService.batchDelete(ids.map(id => parseInt(id)));
      
      res.json({
        code: 200,
        message: '批量删除成功'
      });
    } catch (error) {
      console.error('批量删除供应商失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '批量删除供应商失败'
      });
    }
  }

  /**
   * 更新供应商基础信息
   */
  async updateBasicInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }
      console.log('更新基础信息:', data);
      const result = await this.supplierService.updateBasicInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '基础信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新基础信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新基础信息失败'
      });
    }
  }

  /**
   * 更新供应商账户信息
   */
  async updateAccountInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      const result = await this.supplierService.updateAccountInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '账户信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新账户信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新账户信息失败'
      });
    }
  }

  /**
   * 更新供应商品牌信息
   */
  async updateBrandInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      const result = await this.supplierService.updateBrandInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '品牌信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新品牌信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新品牌信息失败'
      });
    }
  }

  /**
   * 更新供应商联系人信息
   */
  async updateContactInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      const result = await this.supplierService.updateContactInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '联系人信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新联系人信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新联系人信息失败'
      });
    }
  }

  /**
   * 更新供应商协议信息
   */
  async updateAgreementInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      const result = await this.supplierService.updateAgreementInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '协议信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新协议信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新协议信息失败'
      });
    }
  }

  /**
   * 更新供应商附件信息
   */
  async updateAttachmentInfo(req, res) {
    try {
      const { id } = req.params;
      const requestData = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 构造正确的数据结构
      const data = {
        attachments: requestData.attachments || {},
        updater: '系统管理员',
        updateDate: new Date().toISOString()
      };

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
      }

      const result = await this.supplierService.updateAttachmentInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '附件信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新附件信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新附件信息失败'
      });
    }
  }

  /**
   * 更新提交人信息
   */
  async updateSubmitterInfo(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (currentUser) {
        data.updater = currentUser.nickname || currentUser.username || '系统用户';
        data.updateDate = new Date().toISOString();
      }

      // 参数验证
      const errors = this.validateParams(data, SupplierDto.updateSubmitterInfoRules());
      if (errors.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors
        });
      }

      const result = await this.supplierService.updateSubmitterInfo(parseInt(id), data);

      res.json({
        code: 200,
        message: '提交人信息更新成功',
        data: result
      });
    } catch (error) {
      console.error('更新提交人信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新提交人信息失败'
      });
    }
  }

  /**
   * 获取供应商选择列表（简化版本，用于下拉选择等场景）
   */
  async getSelectList(req, res) {
    try {
      const params = req.query;

      // 转换数值类型参数
      if (params.page) params.page = parseInt(params.page);
      if (params.pageSize) params.pageSize = parseInt(params.pageSize);

      // 设置默认值
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        keyword: params.keyword || '',
        status: params.status || 1 // 默认只获取启用状态的供应商
      };

      const result = await this.supplierService.getSelectList(queryParams);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取供应商选择列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取供应商选择列表失败'
      });
    }
  }

  /**
   * 获取供应商统计信息
   */
  async getStats(req, res) {
    try {
      const result = await this.supplierService.getStats();

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取供应商统计信息失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取供应商统计信息失败'
      });
    }
  }

  /**
   * 更换复核人
   */
  async changeReviewer(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id || isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '供应商ID格式不正确'
        });
      }

      // 参数验证
      const errors = this.validateParams(data, SupplierDto.changeReviewerRules());
      if (errors.length > 0) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors
        });
      }

      const result = await this.supplierService.changeReviewer(
        parseInt(id),
        data.reviewer,
        data.updater
      );

      res.json({
        code: 200,
        message: '更换复核人成功',
        data: result
      });
    } catch (error) {
      console.error('更换复核人失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更换复核人失败'
      });
    }
  }

  /**
   * 参数验证
   * @param {Object} data 待验证数据
   * @param {Object} rules 验证规则
   * @returns {Array} 错误信息数组
   */
  validateParams(data, rules) {
    const errors = [];
    
    Object.keys(rules).forEach(field => {
      const rule = rules[field];
      const value = data[field];
      
      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      // 如果值为空且非必填，跳过其他验证
      if (value === undefined || value === null || value === '') {
        return;
      }
      
      // 类型验证
      if (rule.type === 'string' && typeof value !== 'string') {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      if (rule.type === 'number' && (typeof value !== 'number' || isNaN(value))) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      if (rule.type === 'integer' && (!Number.isInteger(value))) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      if (rule.type === 'array' && !Array.isArray(value)) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      // 长度验证
      if (rule.max && typeof value === 'string' && value.length > rule.max) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      // 数值范围验证
      if (rule.min !== undefined && typeof value === 'number' && value < rule.min) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      if (rule.max !== undefined && typeof value === 'number' && value > rule.max) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      // 正则验证
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
      
      // 枚举验证
      if (rule.enum && !rule.enum.includes(value)) {
        errors.push(`${field}: ${rule.message}`);
        return;
      }
    });

    return errors;
  }

  /**
   * 获取供应商关联关系选择列表
   */
  async getRelationSelectList(req, res) {
    try {
      const params = req.query;

      // 转换数值类型参数
      if (params.page) params.page = parseInt(params.page);
      if (params.pageSize) params.pageSize = parseInt(params.pageSize);

      // 设置默认值
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 1000, // 获取足够多的数据
        keyword: params.keyword || '',
        status: params.status !== undefined ? params.status : 1 // 默认只获取启用状态的供应商
      };

      const result = await this.supplierService.getSelectList(queryParams);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取供应商关联关系选择列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取供应商关联关系选择列表失败'
      });
    }
  }
}

module.exports = SupplierController;
