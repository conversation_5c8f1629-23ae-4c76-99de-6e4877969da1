const SupplierModel = require('../models/SupplierModel');

/**
 * 供应商服务
 */
class SupplierService {
  constructor() {
    this.supplierModel = new SupplierModel();
  }

  /**
   * 获取供应商列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getList(params = {}) {
    try {
      return await this.supplierModel.getList(params);
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      throw new Error('获取供应商列表失败');
    }
  }

  /**
   * 获取供应商选择列表（简化版本）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getSelectList(params = {}) {
    try {
      return await this.supplierModel.getSelectList(params);
    } catch (error) {
      console.error('获取供应商选择列表失败:', error);
      throw new Error('获取供应商选择列表失败');
    }
  }

  /**
   * 根据ID获取供应商详情
   * @param {number} id 供应商ID
   * @returns {Promise<Object|null>} 供应商信息
   */
  async getById(id) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }
      return await this.supplierModel.getById(id);
    } catch (error) {
      console.error('获取供应商详情失败:', error);
      throw new Error('获取供应商详情失败');
    }
  }

  /**
   * 创建供应商
   * @param {Object} data 供应商数据
   * @returns {Promise<Object>} 创建的供应商信息
   */
  async create(data) {
    try {
      // 数据验证
      this.validateSupplierData(data);

      // 检查供应商代码是否重复
      await this.checkSupplierCodeUnique(data.supplierCode || data.supplier_code);

      return await this.supplierModel.create(data);
    } catch (error) {
      console.error('创建供应商失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商
   * @param {number} id 供应商ID
   * @param {Object} data 更新数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async update(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      // 如果更新了供应商代码，检查是否重复
      const supplierCode = data.supplierCode || data.supplier_code;
      if (supplierCode && supplierCode !== existingSupplier.supplier_code) {
        await this.checkSupplierCodeUnique(supplierCode, id);
      }

      // 数据验证
      this.validateSupplierData(data, false);
      
      return await this.supplierModel.update(id, data);
    } catch (error) {
      console.error('更新供应商失败:', error);
      throw error;
    }
  }

  /**
   * 删除供应商
   * @param {number} id 供应商ID
   * @returns {Promise<boolean>} 删除结果
   */
  async delete(id) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.delete(id);
    } catch (error) {
      console.error('删除供应商失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除供应商
   * @param {Array<number>} ids 供应商ID数组
   * @returns {Promise<boolean>} 删除结果
   */
  async batchDelete(ids) {
    try {
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        throw new Error('供应商ID数组不能为空');
      }

      return await this.supplierModel.batchDelete(ids);
    } catch (error) {
      console.error('批量删除供应商失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商基础信息
   * @param {number} id 供应商ID
   * @param {Object} data 基础信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateBasicInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      // 如果更新了供应商代码，检查是否重复
      const supplierCode = data.supplierCode || data.supplier_code;
      if (supplierCode && supplierCode !== existingSupplier.supplierCode) {
        await this.checkSupplierCodeUnique(supplierCode, id);
      }

      return await this.supplierModel.updateBasicInfo(id, data);
    } catch (error) {
      console.error('更新基础信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新提交人信息
   * @param {number} id 供应商ID
   * @param {Object} data 提交人信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateSubmitterInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      // 验证提交人信息数据
      this.validateSubmitterInfo(data);

      return await this.supplierModel.updateSubmitterInfo(id, data);
    } catch (error) {
      console.error('更新提交人信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商账户信息
   * @param {number} id 供应商ID
   * @param {Object} data 账户信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAccountInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.updateAccountInfo(id, data);
    } catch (error) {
      console.error('更新账户信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商品牌信息
   * @param {number} id 供应商ID
   * @param {Object} data 品牌信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateBrandInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.updateBrandInfo(id, data);
    } catch (error) {
      console.error('更新品牌信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商联系人信息
   * @param {number} id 供应商ID
   * @param {Object} data 联系人信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateContactInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.updateContactInfo(id, data);
    } catch (error) {
      console.error('更新联系人信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商协议信息
   * @param {number} id 供应商ID
   * @param {Object} data 协议信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAgreementInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.updateAgreementInfo(id, data);
    } catch (error) {
      console.error('更新协议信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新供应商附件信息
   * @param {number} id 供应商ID
   * @param {Object} data 附件信息数据
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async updateAttachmentInfo(id, data) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.updateAttachmentInfo(id, data);
    } catch (error) {
      console.error('更新附件信息失败:', error);
      throw error;
    }
  }

  /**
   * 更换复核人
   * @param {number} id 供应商ID
   * @param {string} reviewer 复核人
   * @param {string} updater 更新人
   * @returns {Promise<Object>} 更新后的供应商信息
   */
  async changeReviewer(id, reviewer, updater) {
    try {
      if (!id) {
        throw new Error('供应商ID不能为空');
      }
      if (!reviewer) {
        throw new Error('复核人不能为空');
      }
      if (!updater) {
        throw new Error('更新人不能为空');
      }

      // 检查供应商是否存在
      const existingSupplier = await this.supplierModel.getById(id);
      if (!existingSupplier) {
        throw new Error('供应商不存在');
      }

      return await this.supplierModel.changeReviewer(id, reviewer, updater);
    } catch (error) {
      console.error('更换复核人失败:', error);
      throw error;
    }
  }

  /**
   * 验证供应商数据 - 支持驼峰命名
   * @param {Object} data 供应商数据
   * @param {boolean} isCreate 是否为创建操作
   */
  validateSupplierData(data, isCreate = true) {
    // 支持驼峰命名的必填字段映射（根据Prisma schema）
    const requiredFieldsMap = [
      { camel: 'supplierName', snake: 'supplier_name', display: '供应商名称' },
      { camel: 'supplierCode', snake: 'supplier_code', display: '供应商代码' },
      { camel: 'companyNature', snake: 'company_nature', display: '公司性质' },
      { camel: 'address', snake: 'address', display: '通信地址' },
      { camel: 'cooperationType', snake: 'cooperation_type', display: '合作方式' },
      { camel: 'cooperationStatus', snake: 'cooperation_status', display: '合作状态' },
      { camel: 'paymentTerms', snake: 'payment_terms', display: '付款条件' },
      { camel: 'department', snake: 'department', display: '部门' },
      { camel: 'submitter', snake: 'submitter', display: '提交人' },
      { camel: 'submitDate', snake: 'submit_date', display: '提交日期' }
    ];

    if (isCreate) {
      for (const fieldMap of requiredFieldsMap) {
        const value = data[fieldMap.camel] || data[fieldMap.snake];
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          throw new Error(`${fieldMap.display}不能为空`);
        }
      }

      // 主营产品验证 - 支持多种字段名，优先选择有数据的字段
      let mainProducts = null;
      if (data.mainProducts && Array.isArray(data.mainProducts) && data.mainProducts.length > 0) {
        mainProducts = data.mainProducts;
      } else if (data.main_products && Array.isArray(data.main_products) && data.main_products.length > 0) {
        mainProducts = data.main_products;
      } else if (data.mainProductIds && Array.isArray(data.mainProductIds) && data.mainProductIds.length > 0) {
        mainProducts = data.mainProductIds;
      }

      console.log('主营产品验证 - mainProducts:', data.mainProducts);
      console.log('主营产品验证 - main_products:', data.main_products);
      console.log('主营产品验证 - mainProductIds:', data.mainProductIds);
      console.log('主营产品验证 - 最终值:', mainProducts);

      if (!mainProducts || !Array.isArray(mainProducts) || mainProducts.length === 0) {
        throw new Error('主营产品不能为空');
      }
    }

    // 验证手机号格式（如果有联系人信息）
    if (data.contacts && Array.isArray(data.contacts)) {
      for (const contact of data.contacts) {
        if (contact.phone && !/^1[3-9]\d{9}$/.test(contact.phone)) {
          throw new Error('联系人手机号格式不正确');
        }
        if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
          throw new Error('联系人邮箱格式不正确');
        }
      }
    }

    // 验证供应商代码格式
    const supplierCode = data.supplierCode || data.supplier_code;
    if (supplierCode && !/^[A-Z0-9]{3,20}$/.test(supplierCode)) {
      throw new Error('供应商代码格式不正确，应为3-20位大写字母和数字组合');
    }

    // 验证税率范围
    const taxRate = data.taxRate || data.tax_rate;
    if (taxRate !== undefined && taxRate !== null && (taxRate < 0 || taxRate > 100)) {
      throw new Error('税率必须在0-100之间');
    }
  }

  /**
   * 检查供应商代码唯一性
   * @param {string} supplierCode 供应商代码
   * @param {number} excludeId 排除的ID（用于更新时检查）
   */
  async checkSupplierCodeUnique(supplierCode, excludeId = null) {
    const existingSupplier = await this.supplierModel.getList({
      supplier_code: supplierCode,
      pageSize: 1
    });

    if (existingSupplier.items.length > 0) {
      const existing = existingSupplier.items[0];
      if (!excludeId || existing.id !== excludeId) {
        throw new Error('供应商代码已存在');
      }
    }
  }

  /**
   * 验证提交人信息
   * @param {Object} data 提交人信息数据
   */
  validateSubmitterInfo(data) {
    // 验证必填字段
    if (!data.submitter) {
      throw new Error('提交人不能为空');
    }

    // 验证字段长度
    if (data.department && data.department.length > 100) {
      throw new Error('部门名称不能超过100个字符');
    }

    if (data.submitter && data.submitter.length > 50) {
      throw new Error('提交人姓名不能超过50个字符');
    }

    if (data.reviewer && data.reviewer.length > 50) {
      throw new Error('复核人姓名不能超过50个字符');
    }

    // 验证提交时间格式（如果提供）
    if (data.submitTime) {
      const submitTime = new Date(data.submitTime);
      if (isNaN(submitTime.getTime())) {
        throw new Error('提交时间格式不正确');
      }
    }
  }

  /**
   * 获取供应商统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      return await this.supplierModel.getStats();
    } catch (error) {
      console.error('获取供应商统计信息失败:', error);
      throw new Error('获取供应商统计信息失败');
    }
  }

  /**
   * 获取字段显示名称
   * @param {string} field 字段名
   * @returns {string} 显示名称
   */
  getFieldDisplayName(field) {
    const fieldNames = {
      supplier_name: '供应商名称',
      supplier_code: '供应商代码',
      contact_person: '联系人',
      phone: '联系电话',
      company_nature: '公司性质',
      address: '通信地址',
      cooperation_type: '合作方式',
      cooperation_status: '合作状态',
      payment_terms: '付款条件',
      department: '部门',
      submitter: '提交人',
      submit_date: '提交日期'
    };
    return fieldNames[field] || field;
  }
}

module.exports = SupplierService;
