const express = require('express');
const InquiryRecordController = require('../controllers/InquiryRecordController');
const authMiddleware = require('../../../../core/middleware/AuthMiddleware');

// 创建路由函数，接收 prisma 实例
function createInquiryRecordRouter(prisma) {
  const router = express.Router();
  const inquiryRecordController = new InquiryRecordController(prisma);

  /**
   * 询价记录路由
   */

  // 创建询价记录
  router.post('/', authMiddleware, (req, res) => {
    inquiryRecordController.createInquiryRecord(req, res);
  });

  // 获取询价记录列表
  router.get('/', authMiddleware, (req, res) => {
    inquiryRecordController.getInquiryRecords(req, res);
  });

  // 获取询价记录详情
  router.get('/:id', authMiddleware, (req, res) => {
    inquiryRecordController.getInquiryRecordDetail(req, res);
  });

  // 更新询价记录
  router.put('/:id', authMiddleware, (req, res) => {
    inquiryRecordController.updateInquiryRecord(req, res);
  });

  // 删除询价记录
  router.delete('/:id', authMiddleware, (req, res) => {
    inquiryRecordController.deleteInquiryRecord(req, res);
  });

  // 批量删除询价记录
  router.post('/batch-delete', authMiddleware, (req, res) => {
    inquiryRecordController.batchDeleteInquiryRecords(req, res);
  });

  // 获取询价记录统计信息
  router.get('/stats/summary', authMiddleware, (req, res) => {
    inquiryRecordController.getInquiryRecordStats(req, res);
  });

  // 根据SKU ID获取关联的供应商信息
  router.get('/suppliers/by-sku/:skuId', authMiddleware, (req, res) => {
    inquiryRecordController.getSuppliersBySkuId(req, res);
  });

  return router;
}

module.exports = createInquiryRecordRouter;
