const express = require('express');
const SupplierController = require('../controllers/SupplierController');

const router = express.Router();
const supplierController = new SupplierController();

/**
 * 供应商路由
 */

// 获取供应商选择列表（简化版本）
router.get('/select-list', (req, res) => {
  supplierController.getSelectList(req, res);
});

// 获取供应商关联关系选择列表
router.get('/relation-select-list', (req, res) => {
  supplierController.getRelationSelectList(req, res);
});

// 获取供应商统计信息
router.get('/stats', (req, res) => {
  supplierController.getStats(req, res);
});

// 获取供应商列表
router.get('/', (req, res) => {
  supplierController.getList(req, res);
});

// 根据ID获取供应商详情
router.get('/:id', (req, res) => {
  supplierController.getById(req, res);
});

// 创建供应商
router.post('/', (req, res) => {
  supplierController.create(req, res);
});

// 更新供应商
router.put('/:id', (req, res) => {
  supplierController.update(req, res);
});

// 更新提交人信息
router.put('/:id/submitter-info', (req, res) => {
  supplierController.updateSubmitterInfo(req, res);
});

// 删除供应商
router.delete('/:id', (req, res) => {
  supplierController.delete(req, res);
});

// 批量删除供应商
router.post('/batch-delete', (req, res) => {
  supplierController.batchDelete(req, res);
});

// 更新供应商基础信息
router.put('/:id/basic-info', (req, res) => {
  supplierController.updateBasicInfo(req, res);
});

// 更新供应商账户信息
router.put('/:id/account-info', (req, res) => {
  supplierController.updateAccountInfo(req, res);
});

// 更新供应商品牌信息
router.put('/:id/brand-info', (req, res) => {
  supplierController.updateBrandInfo(req, res);
});

// 更新供应商联系人信息
router.put('/:id/contact-info', (req, res) => {
  supplierController.updateContactInfo(req, res);
});

// 更新供应商协议信息
router.put('/:id/agreement-info', (req, res) => {
  supplierController.updateAgreementInfo(req, res);
});

// 更新供应商附件信息
router.put('/:id/attachment-info', (req, res) => {
  supplierController.updateAttachmentInfo(req, res);
});

// 更换复核人
router.put('/:id/change-reviewer', (req, res) => {
  supplierController.changeReviewer(req, res);
});

module.exports = router;
