/// 供应商关联表，存储供应商之间的关联关系（多对多）
model CsmSupplierRelation {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 关联ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 主供应商ID
  related_supplier_id   BigInt                                        /// 关联供应商ID
  relation_type         String    @db.VarChar(50)                     /// 关联类型：独立供应商、关联供应商、子公司、分公司、战略合作伙伴等
  
  // 关联详情
  relation_desc         String?   @db.VarChar(500)                    /// 关联描述
  start_date            DateTime? @db.Date                            /// 关联开始日期
  end_date              DateTime? @db.Date                            /// 关联结束日期
  status                String    @default("active") @db.VarChar(20)  /// 关联状态：active=有效、inactive=无效、suspended=暂停
  
  // 操作信息
  creator               String    @db.VarChar(100)                    /// 创建人
  updater               String?   @db.VarChar(100)                    /// 更新人
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  supplier              CsmSupplier @relation("SupplierRelations", fields: [supplier_id], references: [id])
  related_supplier      CsmSupplier @relation("RelatedSupplierRelations", fields: [related_supplier_id], references: [id])

  // 索引
  @@index([supplier_id])
  @@index([related_supplier_id])
  @@index([relation_type])
  @@index([status])
  @@index([deleted_at])
  
  // 联合唯一索引，防止重复关联
  @@unique([supplier_id, related_supplier_id])
  
  @@map("csm_supplier_relations")
  @@schema("csm")
}
