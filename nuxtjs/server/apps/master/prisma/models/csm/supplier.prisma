/// 供应商主表，存储供应商基本信息
model CsmSupplier {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 供应商ID，自增主键
  
  // 基础信息
  supplier_name         String    @db.VarChar(200)                    /// 供应商名称，必填
  supplier_code         String    @unique @db.VarChar(50)             /// 供应商代码，必填，唯一
  contact_person        String    @db.VarChar(100)                    /// 主要联系人，必填
  position              String?   @db.VarChar(100)                    /// 联系人职位
  phone                 String    @db.VarChar(50)                     /// 联系电话，必填
  company_nature        String    @db.VarChar(100)                    /// 公司性质
  address               String    @db.VarChar(500)                    /// 通信地址
  detailed_address      String?   @db.VarChar(500)                    /// 详细地址
  production_address    String?   @db.VarChar(500)                    /// 生产地址
  
  // 合作信息
  cooperation_type      String    @db.VarChar(50)                     /// 合作方式：授权、代加工、代加工+授权、独家代理等
  cooperation_status    String    @db.VarChar(50)                     /// 合作状态：启用、待启用、黑名单、不启用等
  payment_terms         String    @db.VarChar(200)                    /// 付款条件
  supplier_relation     String?   @db.VarChar(100)                    /// 供应商关联：独立供应商、战略合作伙伴、授权代理商等
  supplier_group        String?   @db.VarChar(50)                     /// 供应商分组：A类供应商、B类供应商等
  
  // 企业信息
  credit_code           String?   @db.VarChar(50)                     /// 统一社会信用代码
  registered_capital    Decimal?  @db.Decimal(15,2)                   /// 注册资本（万元）
  register_time         DateTime? @db.Date                            /// 注册时间
  cooperation_agreement String?   @db.VarChar(200)                    /// 合作协议名称
  order_count          Int?      @default(0)                         /// 订单数量
  
  // 账户信息
  invoice_type          String?   @db.VarChar(50)                     /// 发票类型
  tax_rate              Decimal?  @db.Decimal(5,2)                    /// 税率
  account_name          String?   @db.VarChar(200)                    /// 账户名称
  settlement_method     String?   @db.VarChar(50)                     /// 结算方式
  account_number        String?   @db.VarChar(100)                    /// 账户号码
  bank_name             String?   @db.VarChar(200)                    /// 开户银行
  
  // 操作信息
  department            String    @db.VarChar(100)                    /// 部门
  submitter             String    @db.VarChar(100)                    /// 提交人，必填
  submit_date           DateTime  @db.Date                            /// 提交日期，必填
  updater               String?   @db.VarChar(100)                    /// 更新人
  reviewer              String?   @db.VarChar(100)                    /// 复审人
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  brands                CsmSupplierBrand[]                            /// 品牌授权信息
  contacts              CsmSupplierContact[]                          /// 联系人信息
  agreements            CsmSupplierAgreement[]                        /// 协议信息
  attachments           CsmSupplierAttachment[]                       /// 附件信息
  main_products         CsmSupplierMainProduct[]                      /// 主营产品关联
  supplier_relations    CsmSupplierRelation[] @relation("SupplierRelations")        /// 作为主供应商的关联关系
  related_supplier_relations CsmSupplierRelation[] @relation("RelatedSupplierRelations") /// 作为关联供应商的关联关系

  // 索引
  @@index([deleted_at])
  @@index([supplier_name])
  @@index([supplier_code])
  @@index([submitter])
  @@index([cooperation_status])
  @@index([cooperation_type])
  
  @@map("csm_suppliers")
  @@schema("csm")
}
