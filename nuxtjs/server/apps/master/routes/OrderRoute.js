/**
 * 订单路由
 * 处理订单相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const OrderController = require('../controllers/OrderController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new OrderController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/orders:
 *   get:
 *     summary: 获取订单列表
 *     description: 获取订单列表，支持多种筛选条件和分页
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 当前页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: orderNumber
 *         schema:
 *           type: string
 *         description: 订单编号
 *       - in: query
 *         name: thirdPartyOrderSn
 *         schema:
 *           type: string
 *         description: 第三方订单号筛选
 *       - in: query
 *         name: orderStatus
 *         schema:
 *           type: string
 *           enum: [pending, processing, shipped, delivered, completed, returned, cancelled]
 *         description: 订单状态
 *       - in: query
 *         name: paymentStatus
 *         schema:
 *           type: string
 *           enum: [unpaid, partial_paid, paid, refunding, refunded]
 *         description: 支付状态
 *       - in: query
 *         name: shippingStatus
 *         schema:
 *           type: string
 *           enum: [unshipped, shipped, delivered, returned]
 *         description: 配送状态
 *       - in: query
 *         name: customerName
 *         schema:
 *           type: string
 *         description: 客户名称
 *       - in: query
 *         name: buyerPhone
 *         schema:
 *           type: string
 *         description: 买家手机号
 *       - in: query
 *         name: orderType
 *         schema:
 *           type: string
 *           enum: [normal, wholesale, gift, sample, replacement]
 *         description: 订单类型
 *       - in: query
 *         name: orderSource
 *         schema:
 *           type: string
 *           enum: [system, web, app, wechat, third_party, offline]
 *         description: 订单来源
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 订单创建开始时间（毫秒时间戳）
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 订单创建结束时间（毫秒时间戳）
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [recent6m, before6m]
 *         description: 时间范围筛选（recent6m：近6个月，before6m：6个月前）
 *       - in: query
 *         name: sortField
 *         schema:
 *           type: string
 *           enum: [created_at, total_amount, paid_amount]
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方式
 *       - in: query
 *         name: channelId
 *         schema:
 *           type: string
 *         description: 渠道ID筛选
 *       - in: query
 *         name: address
 *         schema:
 *           type: string
 *         description: 收货地址
 *       - in: query
 *         name: addressKeyword
 *         schema:
 *           type: string
 *         description: 收货地址关键词
 *       - in: query
 *         name: hasFollower
 *         schema:
 *           type: string
 *           enum: ['true', 'false', '1', '0']
 *         description: 跟单员筛选 - true/1表示有跟单员，false/0表示无跟单员
 *     responses:
 *       200:
 *         description: 成功获取订单列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取订单列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "175160355448819712"
 *                           orderSn:
 *                             type: string
 *                             example: "JL202504290001"
 *                           thirdPartyOrderSn:
 *                             type: string
 *                             example: "WX202504290001"
 *                           customerName:
 *                             type: string
 *                             example: "张三"
 *                           customerPhone:
 *                             type: string
 *                             example: "13800138000"
 *                           orderStatus:
 *                             type: string
 *                             example: "processing"
 *                           orderStatusText:
 *                             type: string
 *                             example: "处理中"
 *                           paymentStatus:
 *                             type: string
 *                             example: "paid"
 *                           paymentStatusText:
 *                             type: string
 *                             example: "已支付"
 *                           shippingStatus:
 *                             type: string
 *                             example: "unshipped"
 *                           shippingStatusText:
 *                             type: string
 *                             example: "未发货"
 *                           orderType:
 *                             type: string
 *                             example: "normal"
 *                           orderTypeText:
 *                             type: string
 *                             example: "普通订单"
 *                           orderSource:
 *                             type: string
 *                             example: "web"
 *                           orderSourceText:
 *                             type: string
 *                             example: "网站"
 *                           totalAmount:
 *                             type: string
 *                             example: "199.00"
 *                           paidAmount:
 *                             type: string
 *                             example: "199.00"
 *                           totalQuantity:
 *                             type: integer
 *                             example: 2
 *                           totalWeight:
 *                             type: number
 *                             example: 1.5
 *                           totalVolume:
 *                             type: number
 *                             example: 0.01
 *                           createdAt:
 *                             type: string
 *                             example: "1714352400000"
 *                           updatedAt:
 *                             type: string
 *                             example: "1714352400000"
 *                           items:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "175160355448819713"
 *                                 orderId:
 *                                   type: string
 *                                   example: "175160355448819712"
 *                                 productName:
 *                                   type: string
 *                                   example: "高端商务笔记本电脑"
 *                                 productImage:
 *                                   type: string
 *                                   example: "https://example.com/images/laptop.jpg"
 *                                 unitPrice:
 *                                   type: string
 *                                   example: "99.50"
 *                                 quantity:
 *                                   type: integer
 *                                   example: 2
 *                                 totalPrice:
 *                                   type: string
 *                                   example: "199.00"
 *                                 spuNameSnapshot:
 *                                   type: string
 *                                   example: "高端商务笔记本电脑"
 *                                 skuSpecifications:
 *                                   type: string
 *                                   example: "黑色,16GB内存,512GB硬盘"
 *                                 thirdPartyProductCode:
 *                                   type: string
 *                                   example: "SKU12345"
 *                           shipping:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 example: "175160355448819714"
 *                               shippingCompany:
 *                                 type: string
 *                                 example: "顺丰速运"
 *                               trackingNumber:
 *                                 type: string
 *                                 example: "SF1234567890"
 *                               shippingStatus:
 *                                 type: string
 *                                 example: "shipped"
 *                           followers:
 *                             type: array
 *                             description: 订单跟单员列表
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "174802161258074112"
 *                                   description: 跟单员ID
 *                                 name:
 *                                   type: string
 *                                   example: "部门B-用户A"
 *                                   description: 跟单员用户名
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 100
 *                         currentPage:
 *                           type: integer
 *                           example: 1
 *                         totalPage:
 *                           type: integer
 *                           example: 10
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/', controller.getOrders.bind(controller));

/**
 * @swagger
 * /api/master/orders/badge-stats:
 *   get:
 *     summary: 获取订单角标统计
 *     description: 获取全部订单、待付款、待发货、已发货、已关闭、交易成功等状态的订单数量
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 订单角标统计数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "获取订单角标统计成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     all:
 *                       type: integer
 *                       example: 100
 *                       description: 全部订单数量
 *                     unpaid:
 *                       type: integer
 *                       example: 20
 *                       description: 待付款订单数量
 *                     toBeShipped:
 *                       type: integer
 *                       example: 30
 *                       description: 待发货订单数量
 *                     shipped:
 *                       type: integer
 *                       example: 25
 *                       description: 已发货订单数量
 *                     closed:
 *                       type: integer
 *                       example: 10
 *                       description: 已关闭订单数量
 *                     success:
 *                       type: integer
 *                       example: 15
 *                       description: 交易成功订单数量
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "获取订单角标统计失败"
 */
protectedRouter.get('/badge-stats', controller.getOrderBadgeStats.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/remark:
 *   post:
 *     summary: 添加订单标注
 *     description: 添加管理员备注到订单，修改orders表中的admin_remark字段
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - remark
 *             properties:
 *               remark:
 *                 type: string
 *                 description: 订单备注内容
 *                 example: "客户要求送货时电话联系"
 *     responses:
 *       200:
 *         description: 添加订单标注成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "添加订单标注成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 订单ID
 *                       example: "1234567890123456789"
 *                     admin_remark:
 *                       type: string
 *                       description: 管理员备注内容
 *                       example: "客户要求送货时电话联系"
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "订单ID不能为空" 
 *       404:
 *         description: 订单不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 *                   example: "订单不存在"
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "添加订单标注失败"
 */
protectedRouter.post('/:id/remark', controller.addOrderRemark.bind(controller));

/**
 * @swagger
 * /api/master/orders:
 *   post:
 *     summary: 创建订单
 *     description: 创建新订单，包括订单基本信息、订单项、配送信息等
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerName
 *               - customerPhone
 *               - items
 *             properties:
 *               customerName:
 *                 type: string
 *                 description: 客户姓名
 *               customerPhone:
 *                 type: string
 *                 description: 客户手机号
 *               items:
 *                 type: array
 *                 description: 订单项
 *                 items:
 *                   type: object
 *                   required:
 *                     - goodsSpuId
 *                     - goodsSkuId
 *                     - productName
 *                     - unitPrice
 *                     - quantity
 *                   properties:
 *                     goodsSpuId:
 *                       type: string
 *                       description: 商品SPU ID
 *                     goodsSkuId:
 *                       type: string
 *                       description: 商品SKU ID
 *                     productName:
 *                       type: string
 *                       description: 商品名称
 *                     unitPrice:
 *                       type: number
 *                       description: 单价
 *                     quantity:
 *                       type: integer
 *                       description: 数量
 *     responses:
 *       201:
 *         description: 订单创建成功
 *       400:
 *         description: 请求数据验证失败
 *       401:
 *         description: 未授权
 *       409:
 *         description: 库存不足
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/', controller.createOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/third-party:
 *   post:
 *     summary: 第三方创建订单
 *     description: 第三方系统创建订单，支持客户信息和收货地址信息入库，收货人信息直接使用客户信息
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerName
 *               - customerPhone
 *               - channelId
 *               - thirdPartyOrderSn
 *               - items
 *             properties:
 *               customerName:
 *                 type: string
 *                 example: "张三"
 *                 description: "客户姓名（同时作为收货人姓名）"
 *               customerPhone:
 *                 type: string
 *                 example: "13800138000"
 *                 description: "客户电话（同时作为收货人电话）"
 *               channelId:
 *                 type: string
 *                 example: "186008285441298432"
 *                 description: "渠道ID，雪花ID格式"
 *               thirdPartyOrderSn:
 *                 type: string
 *                 example: "TP20250529001-1"
 *                 description: "第三方订单号，字符串类型，必填参数"
 *               platformId:
 *                 type: string
 *                 example: "188941490268016640"
 *                 description: "平台ID，可选"
 *               storeId:
 *                 type: string
 *                 example: "189579695501742080"
 *                 description: "店铺ID，可选"
 *               address:
 *                 type: string
 *                 example: "北京市东城区王府井大街1号"
 *                 description: "收货地址，可以是完整地址"
 *               postalCode:
 *                 type: string
 *                 example: "100006"
 *                 description: "邮政编码"
 *               discountAmount:
 *                 type: number
 *                 example: 0
 *                 description: "优惠金额，默认0"
 *               couponAmount:
 *                 type: number
 *                 example: 0
 *                 description: "优惠券金额，默认0"
 *               shippingFee:
 *                 type: number
 *                 example: 0
 *                 description: "运费，默认0"
 *               paidAmount:
 *                 type: number
 *                 example: 0
 *                 description: "已支付金额，默认0"
 *               paymentMethod:
 *                 type: string
 *                 example: "微信支付"
 *                 description: "支付方式"
 *               transactionId:
 *                 type: string
 *                 example: "wx123456789"
 *                 description: "交易ID"
 *               paymentTime:
 *                 type: number
 *                 example: 1640995200000
 *                 description: "支付时间戳"
 *               remark:
 *                 type: string
 *                 example: "客户备注信息"
 *                 description: "订单备注"
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - productName
 *                     - unitPrice
 *                     - quantity
 *                   properties:
 *                     goodsSpuId:
 *                       type: string
 *                       example: "123456"
 *                       description: "系统商品SPU ID，可选"
 *                     goodsSkuId:
 *                       type: string
 *                       example: "789012"
 *                       description: "系统商品SKU ID，可选"
 *                     productName:
 *                       type: string
 *                       example: "测试商品"
 *                       description: "商品名称，必填"
 *                     productImage:
 *                       type: string
 *                       example: "https://example.com/image.jpg"
 *                       description: "商品图片URL，可选"
 *                     unitPrice:
 *                       type: number
 *                       example: 99.50
 *                       description: "商品单价，必填"
 *                     quantity:
 *                       type: integer
 *                       example: 2
 *                       description: "商品数量，必填"
 *                     thirdPartySpuId:
 *                       type: string
 *                       example: "SPU123456"
 *                       description: "第三方商品SPU ID，可选"
 *                     thirdPartySkuId:
 *                       type: string
 *                       example: "SKU123456"
 *                       description: "第三方商品SKU ID，可选"
 *                     thirdPartyProductCode:
 *                       type: string
 *                       example: "PC123456"
 *                       description: "第三方商品编码，可选"
 *           example:
 *             customerName: "张三"
 *             customerPhone: "13800138000"
 *             platformId: "188941490268016640"
 *             storeId: "189579695501742080"
 *             channelId: "186008285441298432"
 *             thirdPartyOrderSn: "TP20250529001-1"
 *             items:
 *               - productName: "测试商品"
 *                 productImage: "https://example.com/image.jpg"
 *                 unitPrice: 99.5
 *                 quantity: 2
 *                 thirdPartySpuId: "SPU123456"
 *                 thirdPartySkuId: "SKU123456"
 *                 thirdPartyProductCode: "PC123456"
 *             address: "北京市东城区王府井大街1号"
 *             postalCode: "100006"
 *             remark: "客户备注信息"
 *     responses:
 *       200:
 *         description: 第三方订单创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "第三方订单创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "194040022117257216"
 *                       description: "订单ID"
 *                     orderNumber:
 *                       type: string
 *                       example: "JLY2505291234567"
 *                       description: "订单号"
 *                     platformId:
 *                       type: string
 *                       example: "188941490268016640"
 *                       description: "平台ID"
 *                     storeId:
 *                       type: string
 *                       example: "189579695501742080"
 *                       description: "店铺ID"
 *       400:
 *         description: 请求数据验证失败或第三方订单号已存在
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/third-party', controller.createThirdPartyOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/manual:
 *   post:
 *     summary: 手动录入订单
 *     description: 管理员手动录入订单，必须提供渠道ID和商品信息
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - channelId
 *               - orderItems
 *               - shippingInfo
 *             properties:
 *               channelId:
 *                 type: string
 *                 example: "1234567890"
 *                 description: "渠道ID，雪花ID格式"
 *               orderItems:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - productName
 *                     - unitPrice
 *                     - quantity
 *                   properties:
 *                     productName:
 *                       type: string
 *                       example: "测试商品"
 *                     productImage:
 *                       type: string
 *                       example: "https://example.com/image.jpg"
 *                     unitPrice:
 *                       type: number
 *                       example: 99.50
 *                     quantity:
 *                       type: integer
 *                       example: 2
 *               shippingInfo:
 *                 type: object
 *                 required:
 *                   - recipientName
 *                   - recipientPhone
 *                   - regionPath
 *                   - streetAddress
 *                 properties:
 *                   recipientName:
 *                     type: string
 *                     example: "张三"
 *                   recipientPhone:
 *                     type: string
 *                     example: "13800138000"
 *                   regionPath:
 *                     type: string
 *                     example: "北京市,北京市,海淀区"
 *                   streetAddress:
 *                     type: string
 *                     example: "中关村南大街5号"
 *     responses:
 *       200:
 *         description: 订单录入成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "订单录入成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "1234567890"
 *                     orderNumber:
 *                       type: string
 *                       example: "JLY2505281234567"
 *       400:
 *         description: 请求数据验证失败
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/manual', controller.createManualOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}:
 *   get:
 *     summary: 获取订单详情
 *     description: 根据订单ID获取订单详情，包括订单基本信息、订单项、配送信息、销售人员信息和订单日志
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 成功获取订单详情
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/:id', controller.getOrderDetail.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/ship:
 *   post:
 *     summary: 订单发货
 *     description: 更新订单物流信息并将订单状态更新为已发货
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - shippingCompanyCode
 *               - shippingCompanyName
 *               - trackingNumber
 *             properties:
 *               shippingCompanyCode:
 *                 type: string
 *                 description: 物流公司编码
 *               shippingCompanyName:
 *                 type: string
 *                 description: 物流公司名称
 *               trackingNumber:
 *                 type: string
 *                 description: 物流单号
 *               recipientName:
 *                 type: string
 *                 description: 收件人姓名
 *               recipientPhone:
 *                 type: string
 *                 description: 收件人电话
 *     responses:
 *       200:
 *         description: 订单发货成功
 *       400:
 *         description: 请求数据验证失败或订单状态不允许发货
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/ship', controller.shipOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/cancel:
 *   post:
 *     summary: 取消订单
 *     description: 取消订单并恢复库存
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cancelReason
 *             properties:
 *               cancelReason:
 *                 type: string
 *                 description: 取消原因
 *     responses:
 *       200:
 *         description: 订单取消成功
 *       400:
 *         description: 请求数据验证失败或订单状态不允许取消
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/cancel', controller.cancelOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/refund:
 *   post:
 *     summary: 订单退款
 *     description: 对已支付的订单进行退款操作，支持微信支付退款
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refundReason:
 *                 type: string
 *                 description: 退款原因
 *                 example: "客户申请退款"
 *     responses:
 *       200:
 *         description: 退款申请成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "退款申请成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     orderId:
 *                       type: string
 *                       example: "175160355448819712"
 *                     refundSn:
 *                       type: string
 *                       example: "RF1750174724818ABCD"
 *                     refundAmount:
 *                       type: number
 *                       example: 199.00
 *                     wechatRefundId:
 *                       type: string
 *                       example: "50000000382019052709732678859"
 *       400:
 *         description: 请求参数错误或订单状态不允许退款
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/refund', controller.refundOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/mock-cancel:
 *   post:
 *     summary: 模拟取消订单（无需用户验证）
 *     description: 将订单状态更新为已取消，不需要用户验证
 *     tags: [订单管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cancelReason
 *             properties:
 *               cancelReason:
 *                 type: string
 *                 description: 取消原因
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/mock-cancel', controller.mockCancelOrder.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/receipt:
 *   post:
 *     summary: 确认收货
 *     description: 将订单状态更新为已收货
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 确认收货成功
 *       400:
 *         description: 订单状态不允许确认收货
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/receipt', controller.confirmReceipt.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/mock-receipt:
 *   post:
 *     summary: 模拟确认收货（无需用户验证）
 *     description: 将订单状态更新为已收货，不需要用户验证
 *     tags: [订单管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求错误
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/:id/mock-receipt', controller.mockConfirmReceipt.bind(controller));


/**
 * @swagger
 * /api/master/orders/{id}/followers:
 *   get:
 *     summary: 获取订单跟单员列表
 *     description: 获取指定订单的跟单员列表
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取跟单员列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取跟单员列表成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "7245245754573824"
 *                       orderId:
 *                         type: string
 *                         example: "7239245744573123"
 *                       followerId:
 *                         type: string
 *                         example: "7123456789012345"
 *                       followerName:
 *                         type: string
 *                         example: "张三"
 *                       followerAvatar:
 *                         type: string
 *                         example: "https://example.com/avatar.jpg"
 *                       createdAt:
 *                         type: string
 *                         example: "1718280518000"
 *                       updatedAt:
 *                         type: string
 *                         example: "1718280518000"
 *                       createdBy:
 *                         type: string
 *                         example: "7123456789054321"
 *                       updatedBy:
 *                         type: string
 *                         example: "7123456789054321"
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/:id/followers', controller.getOrderFollowers.bind(controller));

/**
 * @swagger
 * /api/master/orders/{id}/followers:
 *   put:
 *     summary: 批量添加/编辑订单跟单员
 *     description: 批量添加或编辑订单的跟单员列表，支持添加、恢复和软删除跟单员
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - followerIds
 *             properties:
 *               followerIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 跟单员ID数组，表示新的完整跟单员列表。不在此列表中的现有跟单员将被软删除
 *                 example: ["7123456789012345", "7123456789012346"]
 *     responses:
 *       200:
 *         description: 编辑跟单员成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 编辑跟单员成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "7245245754573824"
 *                       orderId:
 *                         type: string
 *                         example: "7239245744573123"
 *                       followerId:
 *                         type: string
 *                         example: "7123456789012345"
 *                       followerName:
 *                         type: string
 *                         example: "张三"
 *                       followerAvatar:
 *                         type: string
 *                         example: "https://example.com/avatar.jpg"
 *                       createdAt:
 *                         type: string
 *                         example: "1718280518000"
 *                       updatedAt:
 *                         type: string
 *                         example: "1718280518000"
 *                       createdBy:
 *                         type: string
 *                         example: "7123456789054321"
 *                       updatedBy:
 *                         type: string
 *                         example: "7123456789054321"
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.put('/:id/followers', controller.addOrderFollowers.bind(controller));

/**
 * @swagger
 * /api/v1/master/order/logistics/track:
 *   post:
 *     summary: 查询物流轨迹
 *     description: 根据快递单号和快递公司编码查询物流轨迹信息
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - trackingNumber
 *             properties:
 *               trackingNumber:
 *                 type: string
 *                 description: 快递单号
 *                 example: "78512231510393"
 *               companyCode:
 *                 type: string
 *                 description: 快递公司编码（可选）
 *                 example: "zhongtong"
 *     responses:
 *       200:
 *         description: 查询物流轨迹成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "查询物流轨迹成功"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       context:
 *                         type: string
 *                         description: 物流状态描述
 *                         example: "快件已到达【北京转运中心】"
 *                       time:
 *                         type: string
 *                         description: 时间
 *                         example: "2024-01-15 10:30:00"
 *                       location:
 *                         type: string
 *                         description: 位置
 *                         example: "北京"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/logistics/track', controller.queryLogisticsTrack.bind(controller));

module.exports = router;
