/**
 * 商品SKU路由
 */
const express = require('express');
const router = express.Router();
const GoodsSkuController = require('../controllers/GoodsSkuController');
const prismaManager = require('../../../core/prisma');
const prisma = prismaManager.getClient('base');
const goodsSkuController = new GoodsSkuController(prisma);

/**
 * @swagger
 * /api/master/goods/sku/batch:
 *   post:
 *     tags:
 *       - 商品管理
 *     summary: 批量获取商品SKU信息
 *     description: 根据SKU ID数组批量获取商品SKU信息
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skuIds
 *             properties:
 *               skuIds:
 *                 type: array
 *                 description: 商品SKU ID数组
 *                 items:
 *                   type: string
 *                 example: ["189302806161788928", "189302806161788929"]
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取商品信息成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       skuId:
 *                         type: string
 *                         example: "189302806161788928"
 *                       spuId:
 *                         type: string
 *                         example: "189302806161788900"
 *                       skuCode:
 *                         type: string
 *                         example: "SKU001"
 *                       skuName:
 *                         type: string
 *                         example: "商品名称-红色-M"
 *                       spuName:
 *                         type: string
 *                         example: "商品名称"
 *                       salesPrice:
 *                         type: number
 *                         example: 99.9
 *                       stock:
 *                         type: integer
 *                         example: 100
 */
router.post('/batch', (req, res) => goodsSkuController.getSkusByIds(req, res));

/**
 * @swagger
 * /api/master/goods-sku/channels:
 *   put:
 *     tags:
 *       - 商品管理
 *     summary: 编辑商品SKU渠道
 *     description: 编辑商品SKU的渠道关联信息，支持批量更新
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skuId
 *               - channels
 *             properties:
 *               skuId:
 *                 type: string
 *                 description: 商品SKU ID
 *                 example: "197982752010801152"
 *               channels:
 *                 type: array
 *                 description: 渠道列表
 *                 items:
 *                   type: object
 *                   required:
 *                     - channelId
 *                     - channelName
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 渠道关联记录ID，新增时可为空
 *                       example: "19"
 *                     channelId:
 *                       type: string
 *                       description: 渠道ID
 *                       example: "185653478876647424"
 *                     channelName:
 *                       type: string
 *                       description: 渠道名称
 *                       example: "商城"
 *                     channelIcon:
 *                       type: string
 *                       description: 渠道图标
 *                       example: "icon-tianmao"
 *                     thirdPartySkuId:
 *                       type: string
 *                       description: 第三方SKU ID
 *                       example: "sku_123456"
 *                     isEnabled:
 *                       type: integer
 *                       description: 是否启用：1-启用，0-禁用，默认启用
 *                       default: 1
 *                       example: 1
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 编辑商品SKU渠道成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     skuId:
 *                       type: string
 *                       example: "197982752010801152"
 *                     channels:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "19"
 *                           channelId:
 *                             type: string
 *                             example: "185653478876647424"
 *                           channelName:
 *                             type: string
 *                             example: "商城"
 *                           channelIcon:
 *                             type: string
 *                             example: "icon-tianmao"
 *                           thirdPartySkuId:
 *                             type: string
 *                             example: "sku_123456"
 *                           isEnabled:
 *                             type: integer
 *                             example: 1
 */
router.put('/channels', (req, res) => goodsSkuController.updateSkuChannels(req, res));

module.exports = router;
