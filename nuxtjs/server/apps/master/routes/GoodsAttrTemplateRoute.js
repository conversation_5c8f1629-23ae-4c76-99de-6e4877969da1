const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const GoodsAttrTemplateController = require('../controllers/GoodsAttrTemplateController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const goodsAttrTemplateController = new GoodsAttrTemplateController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-attr-template:
 *   get:
 *     summary: 获取商品属性模板列表
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 模板名称（模糊搜索）
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: integer
 *         description: 关联的商品分类ID
 *     responses:
 *       200:
 *         description: 成功获取商品属性模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取商品属性模板列表成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: 服装属性模板
 *                       sort:
 *                         type: integer
 *                         example: 0
 *                       categoryId:
 *                         type: integer
 *                         example: 1
 *                         nullable: true
 *                       category:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           name:
 *                             type: string
 *                             example: 服装
 *                         nullable: true
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 */
protectedRouter.get('/', goodsAttrTemplateController.getGoodsAttrTemplates.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/{id}:
 *   get:
 *     summary: 获取指定商品属性模板
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品属性模板ID
 *     responses:
 *       200:
 *         description: 成功获取商品属性模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取商品属性模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: 服装属性模板
 *                     sort:
 *                       type: integer
 *                       example: 0
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 */
protectedRouter.get('/:id', goodsAttrTemplateController.getGoodsAttrTemplateById.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template:
 *   post:
 *     summary: 添加商品属性模板
 *     tags: [商品属性模板]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 example: 服装属性模板
 *               sort:
 *                 type: integer
 *                 example: 0
 *               categoryId:
 *                 type: integer
 *                 example: 1
 *                 nullable: true
 *     responses:
 *       201:
 *         description: 成功添加商品属性模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 添加商品属性模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: 服装属性模板
 *                     sort:
 *                       type: integer
 *                       example: 0
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: 请求参数错误或模板名称已存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "商品属性模板名称\"服装属性模板\"已存在"
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "服务器内部错误"
 */
protectedRouter.post('/', goodsAttrTemplateController.addGoodsAttrTemplate.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/{id}:
 *   put:
 *     summary: 更新商品属性模板
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品属性模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 更新后的服装属性模板
 *               sort:
 *                 type: integer
 *                 example: 1
 *               categoryId:
 *                 type: integer
 *                 example: 2
 *                 nullable: true
 *     responses:
 *       200:
 *         description: 成功更新商品属性模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新商品属性模板成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: 更新后的服装属性模板
 *                     sort:
 *                       type: integer
 *                       example: 1
 *                     categoryId:
 *                       type: integer
 *                       example: 2
 *                       nullable: true
 *                     category:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 2
 *                         name:
 *                           type: string
 *                           example: 电子产品
 *                       nullable: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: 请求参数错误或模板名称已存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "商品属性模板名称\"更新后的服装属性模板\"已存在"
 *       404:
 *         description: 属性模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 message:
 *                   type: string
 *                   example: "属性模板不存在"
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "服务器内部错误"
 */
protectedRouter.put('/:id', goodsAttrTemplateController.updateGoodsAttrTemplate.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/{id}:
 *   delete:
 *     summary: 删除商品属性模板
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品属性模板ID
 *     responses:
 *       200:
 *         description: 成功删除商品属性模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除商品属性模板成功
 */
protectedRouter.delete('/:id', goodsAttrTemplateController.deleteGoodsAttrTemplate.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/{templateId}/param:
 *   post:
 *     summary: 添加属性模板参数
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 属性模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *                 example: 颜色
 *               type:
 *                 type: string
 *                 enum: [text, number, radio, checkbox, select]
 *                 example: radio
 *               value:
 *                 type: string
 *                 example: "红色\n蓝色\n绿色"
 *                 description: 当类型为radio、checkbox或select时必填，用换行符分隔不同选项；当类型为text或number时可不填
 *               unit:
 *                 type: string
 *                 example: 厘米
 *                 nullable: true
 *               isRequired:
 *                 type: boolean
 *                 example: false
 *               isFilterable:
 *                 type: boolean
 *                 example: true
 *               sort:
 *                 type: integer
 *                 example: 0
 *               status:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       201:
 *         description: 成功添加属性模板参数
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 添加属性模板参数成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: 颜色
 *                     type:
 *                       type: string
 *                       example: radio
 *                     options:
 *                       type: object
 *                       example: [红色, 蓝色, 绿色]
 *                     unit:
 *                       type: string
 *                       example: 厘米
 *                       nullable: true
 *                     isRequired:
 *                       type: boolean
 *                       example: false
 *                     isFilterable:
 *                       type: boolean
 *                       example: true
 *                     sort:
 *                       type: integer
 *                       example: 0
 *                     isEnabled:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: integer
 *                       example: 1681881600000
 *                     updatedAt:
 *                       type: integer
 *                       example: 1681881600000
 */
protectedRouter.post('/:templateId/param', goodsAttrTemplateController.addGoodsAttrTemplateParam.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/{templateId}/param:
 *   get:
 *     summary: 获取属性模板的所有参数
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 属性模板ID
 *     responses:
 *       200:
 *         description: 成功获取属性模板参数列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取属性模板参数列表成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: 颜色
 *                       type:
 *                         type: string
 *                         example: radio
 *                       options:
 *                         type: object
 *                         example: [红色, 蓝色, 绿色]
 *                       unit:
 *                         type: string
 *                         example: 厘米
 *                         nullable: true
 *                       isRequired:
 *                         type: boolean
 *                         example: false
 *                       isFilterable:
 *                         type: boolean
 *                         example: true
 *                       sort:
 *                         type: integer
 *                         example: 0
 *                       isEnabled:
 *                         type: boolean
 *                         example: true
 *                       createdAt:
 *                         type: integer
 *                         example: 1681881600000
 *                       updatedAt:
 *                         type: integer
 *                         example: 1681881600000
 */
protectedRouter.get('/:templateId/param', goodsAttrTemplateController.getGoodsAttrTemplateParams.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/param/{paramId}:
 *   put:
 *     summary: 更新属性模板参数
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: paramId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 属性参数ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 颜色
 *               type:
 *                 type: string
 *                 enum: [text, number, radio, checkbox, select]
 *                 example: radio
 *               value:
 *                 type: string
 *                 example: "黑色\n白色\n红色"
 *               isRequired:
 *                 type: boolean
 *                 example: false
 *               isFilterable:
 *                 type: boolean
 *                 example: true
 *               sort:
 *                 type: integer
 *                 example: 0
 *               status:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: 成功更新属性模板参数
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新属性模板参数成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: 颜色
 *                     type:
 *                       type: string
 *                       example: radio
 *                     value:
 *                       type: string
 *                       example: "黑色\n白色\n红色"
 *                     isRequired:
 *                       type: boolean
 *                       example: false
 *                     isFilterable:
 *                       type: boolean
 *                       example: true
 *                     sort:
 *                       type: integer
 *                       example: 0
 *                     isEnabled:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: integer
 *                       example: 1681881600000
 *                     updatedAt:
 *                       type: integer
 *                       example: 1681881600000
 */
protectedRouter.put('/param/:paramId', goodsAttrTemplateController.updateGoodsAttrTemplateParam.bind(goodsAttrTemplateController));

/**
 * @swagger
 * /api/master/goods-attr-template/param/{paramId}:
 *   delete:
 *     summary: 删除属性模板参数
 *     tags: [商品属性模板]
 *     parameters:
 *       - in: path
 *         name: paramId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 属性参数ID
 *     responses:
 *       200:
 *         description: 成功删除属性模板参数
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除属性模板参数成功
 */
protectedRouter.delete('/param/:paramId', goodsAttrTemplateController.deleteGoodsAttrTemplateParam.bind(goodsAttrTemplateController));

module.exports = router;
