/**
 * 第三方录单商品处理服务
 *
 * 功能说明：
 * 1. 根据渠道ID+平台ID+店铺ID+第三方SKU/SPU ID查询现有商品
 * 2. 如果找到现有商品，直接使用其goodsSkuId和goodsSpuId
 * 3. 如果没找到，自动创建新的单规格商品（SPU + SKU）
 * 4. 将第三方ID存储到goods_sku_channel表中建立关联
 * 5. 返回处理后的商品数据，包含系统商品ID
 *
 * 处理逻辑：
 * - 优先根据thirdPartySkuId查询现有商品
 * - 如果没有thirdPartySkuId，则根据thirdPartySpuId查询
 * - 如果都没有，则直接创建新商品（每次都会创建新的）
 * - 新创建的商品标记为第三方来源，不设置库存
 */
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');

class ThirdPartyOrderGoodsService {
  /**
   * 构造函数
   * @param {Object} prisma - Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
    console.log('[ThirdPartyOrderGoodsService] 服务初始化完成');
  }

  /**
   * 处理第三方录单中的商品
   *
   * 主要功能：
   * 1. 遍历订单中的每个商品项
   * 2. 根据第三方ID查询是否已存在对应的系统商品
   * 3. 如果存在则复用，如果不存在则创建新商品
   * 4. 返回包含系统商品ID的商品项数组
   *
   * @param {Array} items - 订单商品项数组，每项包含：
   *   - productName: 商品名称
   *   - unitPrice: 单价
   *   - quantity: 数量
   *   - thirdPartySkuId: 第三方SKU ID（可选）
   *   - thirdPartySpuId: 第三方SPU ID（可选）
   * @param {string} channelId - 渠道ID（必填）
   * @param {string} platformId - 平台ID（可选，非必传）
   * @param {string} storeId - 店铺ID（可选，非必传）
   * @param {Object} tx - 数据库事务对象
   * @returns {Promise<Array>} - 处理后的商品项数组，每项新增：
   *   - goodsSkuId: 系统SKU ID
   *   - goodsSpuId: 系统SPU ID
   */
  async processOrderItems(items, channelId, platformId, storeId, tx) {
    const startTime = Date.now();
    console.log('[ThirdPartyOrderGoodsService] ========== 开始处理第三方录单商品 ==========');
    console.log('[ThirdPartyOrderGoodsService] 处理参数:', {
      itemsCount: items.length,
      channelId: channelId,
      platformId: platformId || '未提供',
      storeId: storeId || '未提供'
    });

    // 验证必填参数
    if (!channelId) {
      throw new Error('渠道ID是必填参数');
    }
    if (!items || !Array.isArray(items) || items.length === 0) {
      throw new Error('商品项数组不能为空');
    }

    const processedItems = [];
    let existingCount = 0; // 使用现有商品的数量
    let createdCount = 0;  // 创建新商品的数量

    // 逐个处理每个商品项
    for (let index = 0; index < items.length; index++) {
      const item = items[index];
      const itemStartTime = Date.now();

      try {
        console.log(`[ThirdPartyOrderGoodsService] ---------- 处理商品项 ${index + 1}/${items.length} ----------`);
        console.log('[ThirdPartyOrderGoodsService] 商品项信息:', {
          productName: item.productName || '未提供商品名称',
          unitPrice: item.unitPrice || 0,
          quantity: item.quantity || 1,
          thirdPartySkuId: item.thirdPartySkuId || '无',
          thirdPartySpuId: item.thirdPartySpuId || '无'
        });

        // 步骤1：查询现有商品
        console.log('[ThirdPartyOrderGoodsService] 步骤1: 查询现有商品...');
        const existingGoods = await this.findExistingGoods(
          item,
          channelId,
          platformId,
          storeId,
          tx
        );

        let goodsSkuId, goodsSpuId;

        if (existingGoods) {
          // 使用现有商品
          goodsSkuId = existingGoods.goodsSkuId;
          goodsSpuId = existingGoods.goodsSpuId;
          existingCount++;
          console.log('[ThirdPartyOrderGoodsService] ✅ 找到现有商品，直接使用:', {
            goodsSkuId: goodsSkuId.toString(),
            goodsSpuId: goodsSpuId.toString(),
            处理方式: '复用现有商品'
          });
        } else {
          // 创建新商品
          console.log('[ThirdPartyOrderGoodsService] 步骤2: 未找到现有商品，开始创建新商品...');
          const newGoods = await this.createNewGoods(
            item,
            channelId,
            platformId,
            storeId,
            tx
          );
          goodsSkuId = newGoods.goodsSkuId;
          goodsSpuId = newGoods.goodsSpuId;
          createdCount++;
          console.log('[ThirdPartyOrderGoodsService] ✅ 新商品创建完成:', {
            goodsSkuId: goodsSkuId.toString(),
            goodsSpuId: goodsSpuId.toString(),
            处理方式: '创建新商品'
          });
        }

        // 步骤3：准备处理后的商品项
        const processedItem = {
          ...item, // 保留原有字段
          goodsSkuId: goodsSkuId,   // 添加系统SKU ID
          goodsSpuId: goodsSpuId    // 添加系统SPU ID
        };

        processedItems.push(processedItem);

        const itemEndTime = Date.now();
        console.log(`[ThirdPartyOrderGoodsService] 商品项 ${index + 1} 处理完成，耗时: ${itemEndTime - itemStartTime}ms`);

      } catch (error) {
        console.error(`[ThirdPartyOrderGoodsService] ❌ 处理商品项 ${index + 1} 失败:`, {
          productName: item.productName,
          error: error.message,
          stack: error.stack
        });
        throw new Error(`处理商品 ${item.productName || '未知商品'} 失败: ${error.message}`);
      }
    }

    const endTime = Date.now();
    console.log('[ThirdPartyOrderGoodsService] ========== 商品处理完成 ==========');
    console.log('[ThirdPartyOrderGoodsService] 处理结果统计:', {
      总商品数: items.length,
      使用现有商品: existingCount,
      创建新商品: createdCount,
      处理成功: processedItems.length,
      总耗时: `${endTime - startTime}ms`
    });

    return processedItems;
  }

  /**
   * 查询现有商品
   *
   * 查询逻辑：
   * 1. 优先根据thirdPartySkuId查询goods_sku_channel表
   * 2. 如果没有thirdPartySkuId，则根据thirdPartySpuId查询
   * 3. 如果都没有，则返回null（无法查询现有商品）
   * 4. 查询条件包括：渠道ID + 第三方ID + 未删除 + 已启用
   *
   * @param {Object} item - 商品项，包含第三方ID信息
   * @param {string} channelId - 渠道ID（必填）
   * @param {string} platformId - 平台ID（可选，当前未用于查询条件）
   * @param {string} storeId - 店铺ID（可选，当前未用于查询条件）
   * @param {Object} tx - 数据库事务对象
   * @returns {Promise<Object|null>} - 现有商品信息或null
   *   - goodsSkuId: 系统SKU ID
   *   - goodsSpuId: 系统SPU ID
   *   - channelRecord: 渠道关联记录
   */
  async findExistingGoods(item, channelId, platformId, storeId, tx) {
    const startTime = Date.now();

    try {
      console.log('[ThirdPartyOrderGoodsService] 开始查询现有商品...');

      // 构建基础查询条件
      const whereCondition = {
        channel_id: BigInt(channelId),  // 渠道ID（必须匹配）
        deleted_at: null,               // 未删除
        is_enabled: 1                   // 已启用
      };

      // 确定查询策略：优先使用thirdPartySkuId，其次使用thirdPartySpuId
      let queryStrategy = '';
      if (item.thirdPartySkuId) {
        whereCondition.third_party_sku_id = item.thirdPartySkuId;
        queryStrategy = 'thirdPartySkuId';
        console.log('[ThirdPartyOrderGoodsService] 查询策略: 使用第三方SKU ID');
      } else if (item.thirdPartySpuId) {
        whereCondition.third_party_spu_id = item.thirdPartySpuId;
        queryStrategy = 'thirdPartySpuId';
        console.log('[ThirdPartyOrderGoodsService] 查询策略: 使用第三方SPU ID');
      } else {
        console.log('[ThirdPartyOrderGoodsService] 无第三方ID，跳过查询现有商品');
        return null;
      }

      console.log('[ThirdPartyOrderGoodsService] 查询条件:', {
        渠道ID: channelId,
        查询字段: queryStrategy,
        查询值: queryStrategy === 'thirdPartySkuId' ? item.thirdPartySkuId : item.thirdPartySpuId,
        完整条件: whereCondition
      });

      // 执行查询
      const existingChannel = await tx.goodsSkuChannel.findFirst({
        where: whereCondition,
        include: {
          goods_sku: {
            include: {
              goods_spu: true
            }
          }
        }
      });

      const endTime = Date.now();

      if (existingChannel) {
        console.log('[ThirdPartyOrderGoodsService] ✅ 找到现有商品:', {
          渠道关联ID: existingChannel.id.toString(),
          系统SKU_ID: existingChannel.goods_sku_id.toString(),
          系统SPU_ID: existingChannel.goods_sku.goods_spu_id.toString(),
          商品名称: existingChannel.goods_sku.goods_spu.name,
          查询耗时: `${endTime - startTime}ms`
        });

        return {
          goodsSkuId: existingChannel.goods_sku_id,
          goodsSpuId: existingChannel.goods_sku.goods_spu_id,
          channelRecord: existingChannel
        };
      } else {
        console.log('[ThirdPartyOrderGoodsService] ❌ 未找到现有商品:', {
          查询策略: queryStrategy,
          查询值: queryStrategy === 'thirdPartySkuId' ? item.thirdPartySkuId : item.thirdPartySpuId,
          查询耗时: `${endTime - startTime}ms`
        });
        return null;
      }

    } catch (error) {
      console.error('[ThirdPartyOrderGoodsService] ❌ 查询现有商品失败:', {
        error: error.message,
        商品名称: item.productName,
        第三方SKU_ID: item.thirdPartySkuId,
        第三方SPU_ID: item.thirdPartySpuId,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * 创建新商品（单规格）
   *
   * 创建流程：
   * 1. 生成雪花ID作为SPU和SKU的主键
   * 2. 创建SPU记录（标准产品单位）
   * 3. 创建SKU记录（库存保持单位）
   * 4. 创建goods_sku_channel关联记录
   *
   * 字段映射规则：
   * - SPU名称：使用item.productName
   * - SKU销售价：使用item.unitPrice
   * - 库存：设置为0（不管理库存）
   * - 来源类型：标记为第三方（GoodsSourceTypeEnum.THIRD_PARTY）
   * - 其他字段：保持为空或默认值
   *
   * @param {Object} item - 商品项，包含商品基本信息
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID（可选）
   * @param {string} storeId - 店铺ID（可选）
   * @param {Object} tx - 数据库事务对象
   * @returns {Promise<Object>} - 新创建的商品信息
   *   - goodsSkuId: 新创建的SKU ID
   *   - goodsSpuId: 新创建的SPU ID
   *   - spu: SPU记录对象
   *   - sku: SKU记录对象
   */
  async createNewGoods(item, channelId, platformId, storeId, tx) {
    const startTime = Date.now();

    try {
      console.log('[ThirdPartyOrderGoodsService] 开始创建新商品...');

      // 准备基础数据
      const now = BigInt(Date.now());
      const userId = BigInt(1); // 第三方系统默认用户ID

      console.log('[ThirdPartyOrderGoodsService] 基础数据准备:', {
        创建时间: new Date(Number(now)).toLocaleString(),
        创建用户ID: userId.toString(),
        商品名称: item.productName || '第三方商品',
        销售价格: item.unitPrice || 0
      });

      // 步骤1：创建SPU（标准产品单位）
      console.log('[ThirdPartyOrderGoodsService] 步骤1: 创建SPU...');
      const spuId = generateSnowflakeId();
      const spuCode = `TPG_${spuId}`;  // 第三方商品SPU编码前缀
      const slug = `third-party-goods-${spuId}`; // URL友好的商品标识

      console.log('[ThirdPartyOrderGoodsService] SPU创建参数:', {
        SPU_ID: spuId.toString(),
        SPU编码: spuCode,
        商品名称: item.productName || '第三方商品',
        URL标识: slug,
        来源类型: GoodsSourceTypeEnum.THIRD_PARTY,
        渠道ID: channelId,
        平台ID: platformId || '未提供',
        店铺ID: storeId || '未提供'
      });

      const spu = await tx.goodsSpu.create({
        data: {
          // 基础标识信息
          id: spuId,                                    // 主键ID（雪花ID）
          spu_code: spuCode,                           // SPU编码（TPG_前缀）
          name: item.productName || '第三方商品',        // 商品名称
          slug: slug,                                  // URL友好标识

          // 商品描述信息（第三方商品暂不填充）
          subtitle: null,                              // 副标题
          description: null,                           // 商品描述

          // 分类和品牌信息（第三方商品暂不关联）
          goods_brand_id: null,                        // 品牌ID
          goods_freight_template_id: null,             // 运费模板ID

          // SEO信息（第三方商品暂不填充）
          meta_title: null,                            // SEO标题
          meta_keywords: null,                         // SEO关键词
          meta_description: null,                      // SEO描述

          // 状态和排序
          sort_order: 0,                               // 排序权重
          status: 1,                                   // 商品状态（1=上架）
          published_at: now,                           // 发布时间
          is_virtual: 0,                               // 是否虚拟商品（0=否）
          is_shipping_required: 1,                     // 是否需要物流（1=是）
          is_free_shipping: 1,                         // 是否包邮（1=是）
          delivery_area: null,                         // 配送区域
          delivery_time: null,                         // 配送时间

          // 销量和库存
          total_sales: 0,                              // 总销量
          total_stock: 0,                              // 总库存

          // 来源信息（重要：标识为第三方商品）
          source_type: GoodsSourceTypeEnum.THIRD_PARTY, // 来源类型：第三方
          created_channel_id: BigInt(channelId),       // 创建渠道ID
          created_platform_id: platformId ? BigInt(platformId) : null, // 创建平台ID
          created_store_id: storeId ? BigInt(storeId) : null,          // 创建店铺ID

          // 审计字段
          created_at: now,                             // 创建时间
          updated_at: now,                             // 更新时间
          created_by: userId,                          // 创建人ID
          updated_by: userId                           // 更新人ID
        }
      });

      console.log('[ThirdPartyOrderGoodsService] ✅ SPU创建成功:', {
        SPU_ID: spu.id.toString(),
        SPU编码: spu.spu_code,
        商品名称: spu.name,
        来源类型: spu.source_type,
        状态: spu.status
      });

      // 步骤2：创建SKU（库存保持单位）
      console.log('[ThirdPartyOrderGoodsService] 步骤2: 创建SKU...');
      const skuId = generateSnowflakeId();
      const skuCode = `TPSKU_${skuId}`;  // 第三方商品SKU编码前缀

      console.log('[ThirdPartyOrderGoodsService] SKU创建参数:', {
        SKU_ID: skuId.toString(),
        SKU编码: skuCode,
        关联SPU_ID: spuId.toString(),
        销售价格: item.unitPrice || 0,
        库存数量: 0,
        说明: '第三方商品不设置库存'
      });

      const sku = await tx.goodsSku.create({
        data: {
          // 基础标识信息
          id: skuId,                                   // 主键ID（雪花ID）
          goods_spu_id: spuId,                         // 关联的SPU ID
          sku_code: skuCode,                           // SKU编码（TPSKU_前缀）
          barcode: null,                               // 商品条形码

          // 价格信息
          sales_price: item.unitPrice || 0,            // 销售价格（来自订单项）
          market_price: null,                          // 市场价（第三方商品暂不填充）
          cost_price: null,                            // 成本价（第三方商品暂不填充）

          // 物理属性（第三方商品暂不填充）
          weight: null,                                // 重量
          volume: null,                                // 体积
          unit: null,                                  // 单位

          // 库存信息
          stock: 0,                                    // 库存数量（第三方商品不设置库存）
          low_stock_threshold: null,                   // 库存预警阈值
          sales_volume: 0,                             // 历史销售数量

          // 状态信息
          is_enabled: 1,                               // 是否启用（1=启用）

          // 审计字段
          created_at: now,                             // 创建时间
          updated_at: now                              // 更新时间
        }
      });

      console.log('[ThirdPartyOrderGoodsService] ✅ SKU创建成功:', {
        SKU_ID: sku.id.toString(),
        SKU编码: sku.sku_code,
        销售价格: sku.sales_price,
        库存数量: sku.stock,
        关联SPU: spuId.toString()
      });

      // 步骤3：创建渠道关联记录（重要：建立第三方ID与系统ID的映射关系）
      console.log('[ThirdPartyOrderGoodsService] 步骤3: 创建渠道关联记录...');

      console.log('[ThirdPartyOrderGoodsService] 渠道关联参数:', {
        系统SKU_ID: skuId.toString(),
        渠道ID: channelId,
        第三方SKU编码: skuCode,
        第三方SKU_ID: item.thirdPartySkuId || '无',
        第三方SPU_ID: item.thirdPartySpuId || '无',
        说明: '此记录用于建立第三方商品ID与系统商品ID的映射关系'
      });

      await tx.goodsSkuChannel.create({
        data: {
          // 关联信息
          goods_sku_id: skuId,                         // 系统SKU ID
          channel_id: BigInt(channelId),               // 渠道ID

          // 第三方标识信息（重要：用于后续查询现有商品）
          third_party_sku_id: item.thirdPartySkuId || null,      // 第三方SKU ID
          third_party_spu_id: item.thirdPartySpuId || null,      // 第三方SPU ID

          // 状态信息
          is_enabled: 1,                               // 是否启用（1=启用）

          // 审计字段
          created_at: now,                             // 创建时间
          updated_at: now,                             // 更新时间
          created_by: userId,                          // 创建人ID
          updated_by: userId                           // 更新人ID
        }
      });

      console.log('[ThirdPartyOrderGoodsService] ✅ 渠道关联创建成功');

      const endTime = Date.now();
      console.log('[ThirdPartyOrderGoodsService] ========== 新商品创建完成 ==========');
      console.log('[ThirdPartyOrderGoodsService] 创建结果:', {
        系统SPU_ID: spuId.toString(),
        系统SKU_ID: skuId.toString(),
        商品名称: spu.name,
        销售价格: sku.sales_price,
        第三方SKU_ID: item.thirdPartySkuId || '无',
        第三方SPU_ID: item.thirdPartySpuId || '无',
        总耗时: `${endTime - startTime}ms`
      });

      return {
        goodsSkuId: skuId,    // 返回系统SKU ID
        goodsSpuId: spuId,    // 返回系统SPU ID
        spu: spu,             // 返回SPU记录对象
        sku: sku              // 返回SKU记录对象
      };

    } catch (error) {
      console.error('[ThirdPartyOrderGoodsService] ❌ 创建新商品失败:', {
        error: error.message,
        商品名称: item.productName,
        第三方SKU_ID: item.thirdPartySkuId,
        第三方SPU_ID: item.thirdPartySpuId,
        渠道ID: channelId,
        stack: error.stack
      });
      throw error;
    }
  }
}

module.exports = ThirdPartyOrderGoodsService;
