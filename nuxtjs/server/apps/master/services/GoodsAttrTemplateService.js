const snowflake = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 判断是否是特殊处理的字段（保持整数值）
 * @param {string} key 字段名
 * @returns {boolean} 是否需要特殊处理
 */
function isIntegerField(key) {
  // 需要保持整数值的字段列表
  const integerFields = ['is_required', 'is_filterable', 'is_enabled'];
  return integerFields.includes(key);
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * 并将指定字段的整数值(0/1)转换为布尔值
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        
        // 对于特殊字段，保持整数值
        if (isIntegerField(key) && typeof obj[key] === 'number') {
          result[camelKey] = obj[key]; // 保持整数值不变
        } else {
          result[camelKey] = convertKeysToCamelCase(obj[key]);
        }
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id相关字段且值为bigint，返回字符串
        if ((key === 'id' || 
            key.endsWith('_id') || 
            key === 'attribute_set_id' || 
            key === 'attribute_item_id' || 
            key === 'category_id' || 
            key === 'goods_attribute_set_id' || 
            key === 'goods_attribute_item_id') && 
            typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品属性模板服务
 */
class GoodsAttrTemplateService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }
  
  /**
   * 获取分类的完整路径
   * @param {string} categoryId 分类ID
   * @returns {Promise<{id: string, name: string, fullPath: string}>} 返回分类信息，包含完整路径
   */
  async getCategoryFullPath(categoryId) {
    try {
      if (!categoryId) return null;
      
      // 获取分类信息
      const category = await this.prisma.goodsCategory.findFirst({
        where: { 
          id: BigInt(categoryId),
          deleted_at: null 
        }
      });
      
      if (!category) return null;
      
      // 处理BigInt序列化
      const processedCategory = handleBigInt(category);
      const camelCaseCategory = convertKeysToCamelCase(processedCategory);
      
      // 如果没有父分类，直接返回当前分类名称
      if (!camelCaseCategory.goodsParentCategoryId) {
        return {
          id: camelCaseCategory.id,
          name: camelCaseCategory.name,
          fullPath: camelCaseCategory.name
        };
      }
      
      // 如果有父分类，递归获取父分类路径
      const pathParts = [];
      let currentCategory = camelCaseCategory;
      
      // 最多处理4级分类，避免无限循环
      for (let i = 0; i < 4; i++) {
        pathParts.unshift(currentCategory.name);
        
        if (!currentCategory.goodsParentCategoryId) break;
        
        const parentCategory = await this.prisma.goodsCategory.findFirst({
          where: { 
            id: BigInt(currentCategory.goodsParentCategoryId),
            deleted_at: null 
          }
        });
        
        if (!parentCategory) break;
        
        currentCategory = convertKeysToCamelCase(handleBigInt(parentCategory));
      }
      
      return {
        id: camelCaseCategory.id,
        name: camelCaseCategory.name,
        fullPath: pathParts.join('/')
      };
    } catch (error) {
      console.error(`获取分类路径失败:`, error);
      return null;
    }
  }
  /**
   * 获取所有商品属性模板
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 返回包含items和total的对象
   */
  async getAllGoodsAttrTemplates(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的模板
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 用于存储模板与分类的关联信息
      let templateCategoryMap = {};
      
      // 如果提供了分类ID，添加分类筛选条件
      if (options.categoryId) {
        // 查询与分类关联的模板
        const categoryAssociations = await this.prisma.goodsAttributeSetCategoryAssociation.findMany({
          where: {
            goods_category_id: BigInt(options.categoryId)
          },
          select: {
            goods_attribute_set_id: true,
            goods_category_id: true
          }
        });
        
        // 提取模板ID列表
        const templateIds = categoryAssociations.map(assoc => assoc.goods_attribute_set_id);
        
        // 构建模板与分类的映射关系
        categoryAssociations.forEach(assoc => {
          templateCategoryMap[assoc.goods_attribute_set_id.toString()] = assoc.goods_category_id.toString();
        });
        
        // 如果有关联的模板，添加到查询条件
        if (templateIds.length > 0) {
          whereCondition.id = {
            in: templateIds
          };
        } else {
          // 如果没有关联的模板，返回空数组
          return {
            items: [],
            total: 0
          };
        }
      } else {
        // 如果没有提供分类ID，获取所有模板的分类关联
        const allAssociations = await this.prisma.goodsAttributeSetCategoryAssociation.findMany({
          select: {
            goods_attribute_set_id: true,
            goods_category_id: true
          }
        });
        
        // 构建模板与分类的映射关系
        allAssociations.forEach(assoc => {
          templateCategoryMap[assoc.goods_attribute_set_id.toString()] = assoc.goods_category_id.toString();
        });
      }
      
      // 如果提供了创建时间范围，添加时间筛选条件
      if (options.startTime || options.endTime) {
        whereCondition.created_at = {};
        
        if (options.startTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const startTimeMs = BigInt(options.startTime);
          whereCondition.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 前端传入的是毫秒时间戳，直接使用
          const endTimeMs = BigInt(options.endTime);
          whereCondition.created_at.lte = endTimeMs;
        }
      }
      
      // 从数据库获取所有模板
      const templates = await this.prisma.goodsAttributeSet.findMany({
        where: whereCondition,
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        },
        skip: options.skip || 0,
        take: options.take || 10
      });
      
      // 获取总数
      const total = await this.prisma.goodsAttributeSet.count({
        where: whereCondition
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplates = handleBigInt(templates);
      const camelCaseTemplates = convertKeysToCamelCase(processedTemplates);
      
      // 为每个模板添加分类信息和参数项数量
      const templatesWithCategory = await Promise.all(camelCaseTemplates.map(async template => {
        template.sort = template.sortOrder;
        
        // 获取关联的分类ID
        const categoryId = templateCategoryMap[template.id];
        
        if (categoryId) {
          // 获取分类的完整路径
          const categoryInfo = await this.getCategoryFullPath(categoryId);
          
          if (categoryInfo) {
            template.categoryId = categoryInfo.id;
            template.categoryName = categoryInfo.fullPath;
          }
        }
        
        // 获取模板的参数项数量
        try {
          const params = await this.prisma.goodsAttributeItem.count({
            where: { 
              goods_attribute_set_id: BigInt(template.id),
              deleted_at: null 
            }
          });
          
          template.paramCount = params;
        } catch (error) {
          console.error(`获取模板参数数量失败:`, error);
          template.paramCount = 0;
        }
        
        return template;
      }));
      
      return {
        items: templatesWithCategory,
        total
      };
    } catch (error) {
      console.error('获取商品属性模板列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品属性模板
   * @param {number} id 模板ID
   * @returns {Promise<Object>} 返回模板对象
   */
  async getGoodsAttrTemplateById(id) {
    try {
      // 查询模板
      const template = await this.prisma.goodsAttributeSet.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });
      
      if (!template) {
        throw new Error(`ID为${id}的商品属性模板不存在`);
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(template);
      const camelCaseTemplate = convertKeysToCamelCase(processedTemplate);
      camelCaseTemplate.sort = camelCaseTemplate.sortOrder;
      
      // 查询模板关联的分类
      const categoryAssociation = await this.prisma.goodsAttributeSetCategoryAssociation.findFirst({
        where: {
          goods_attribute_set_id: BigInt(id)
        },
        select: {
          goods_category_id: true
        }
      });
      
      if (categoryAssociation) {
        // 获取分类的完整路径
        const categoryInfo = await this.getCategoryFullPath(categoryAssociation.goods_category_id.toString());
        
        if (categoryInfo) {
          camelCaseTemplate.categoryId = categoryInfo.id;
          camelCaseTemplate.categoryName = categoryInfo.fullPath;
        }
      }
      
      // 获取模板的参数项
      const params = await this.prisma.goodsAttributeItem.findMany({
        where: {
          goods_attribute_set_id: BigInt(id),
          deleted_at: null
        },
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        }
      });
      
      // 处理参数项的BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParams = handleBigInt(params);
      const camelCaseParams = convertKeysToCamelCase(processedParams);
      
      // 为每个参数项添加排序字段
      camelCaseParams.forEach(param => {
        param.sort = param.sortOrder;
      });
      
      camelCaseTemplate.params = camelCaseParams;
      camelCaseTemplate.paramCount = camelCaseParams.length;
      
      return camelCaseTemplate;
    } catch (error) {
      console.error(`获取ID为${id}的商品属性模板失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品属性模板
   * @param {Object} templateData 模板数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回新创建的模板
   */
  async addGoodsAttrTemplate(templateData, userId = null) {
    try {
      // 业务校验：检查模板名称是否已存在
      await this.validateTemplateNameUnique(templateData.name);
      
      // 准备模板数据
      const now = BigInt(Date.now()); // 当前时间戳（毫秒）
      const id = snowflake.generateSnowflakeId(); // 生成雪花ID
      
      const templateToCreate = {
        id: id, // 添加必需的id字段
        name: templateData.name,
        sort_order: templateData.sort !== undefined ? parseInt(templateData.sort, 10) : 0,
        created_at: now,
        updated_at: now,
        created_by: userId ? BigInt(userId) : null,
        updated_by: userId ? BigInt(userId) : null
      };
      
      // 创建模板
      const newTemplate = await this.prisma.goodsAttributeSet.create({
        data: templateToCreate
      });
      
      // 如果提供了分类ID，创建模板与分类的关联
      if (templateData.categoryId) {
        await this.prisma.goodsAttributeSetCategoryAssociation.create({
          data: {
            goods_attribute_set_id: newTemplate.id,
            goods_category_id: BigInt(templateData.categoryId)
          }
        });
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(newTemplate);
      const camelCaseTemplate = convertKeysToCamelCase(processedTemplate);
      camelCaseTemplate.sort = camelCaseTemplate.sortOrder;
      
      // 如果提供了分类ID，添加分类信息
      if (templateData.categoryId) {
        const categoryInfo = await this.getCategoryFullPath(templateData.categoryId);
        
        if (categoryInfo) {
          camelCaseTemplate.categoryId = categoryInfo.id;
          camelCaseTemplate.categoryName = categoryInfo.fullPath;
        }
      }
      
      return camelCaseTemplate;
    } catch (error) {
      console.error('添加商品属性模板失败:', error);
      throw error;
    }
  }
  
  /**
   * 校验模板名称是否唯一
   * @param {string} name 模板名称
   * @throws {Error} 如果名称已存在，抛出错误
   */
  async validateTemplateNameUnique(name) {
    const existingTemplate = await this.prisma.goodsAttributeSet.findFirst({
      where: {
        name: name,
        deleted_at: null
      }
    });
    
    if (existingTemplate) {
      throw new Error(`商品属性模板名称"${name}"已存在`);
    }
  }

  /**
   * 更新商品属性模板
   * @param {number} id 模板ID
   * @param {Object} templateData 模板数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回更新后的模板
   */
  async updateGoodsAttrTemplate(id, templateData, userId = null) {
    try {
      // 业务校验：检查模板是否存在
      const existingTemplate = await this.getGoodsAttrTemplateById(id);
      
      // 如果要更新名称，检查名称是否已被其他模板使用
      if (templateData.name && templateData.name !== existingTemplate.name) {
        await this.validateTemplateNameForUpdate(id, templateData.name);
      }
      
      // 准备更新数据
      const now = BigInt(Date.now()); // 当前时间戳（毫秒）
      
      const templateToUpdate = {
        updated_at: now,
        updated_by: userId ? BigInt(userId) : null
      };
      
      // 如果提供了名称，更新名称
      if (templateData.name) {
        templateToUpdate.name = templateData.name;
      }
      
      // 如果提供了排序值，更新排序值
      if (templateData.sort !== undefined) {
        templateToUpdate.sort_order = parseInt(templateData.sort, 10);
      }
      
      // 更新模板
      const updatedTemplate = await this.prisma.goodsAttributeSet.update({
        where: {
          id: BigInt(id)
        },
        data: templateToUpdate
      });
      
      // 如果提供了分类ID，更新模板与分类的关联
      if (templateData.categoryId) {
        // 先删除现有的关联
        await this.prisma.goodsAttributeSetCategoryAssociation.deleteMany({
          where: {
            goods_attribute_set_id: BigInt(id)
          }
        });
        
        // 创建新的关联
        await this.prisma.goodsAttributeSetCategoryAssociation.create({
          data: {
            goods_attribute_set_id: BigInt(id),
            goods_category_id: BigInt(templateData.categoryId)
          }
        });
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedTemplate = handleBigInt(updatedTemplate);
      const camelCaseTemplate = convertKeysToCamelCase(processedTemplate);
      camelCaseTemplate.sort = camelCaseTemplate.sortOrder;
      
      // 如果提供了分类ID，添加分类信息
      if (templateData.categoryId) {
        const categoryInfo = await this.getCategoryFullPath(templateData.categoryId);
        
        if (categoryInfo) {
          camelCaseTemplate.categoryId = categoryInfo.id;
          camelCaseTemplate.categoryName = categoryInfo.fullPath;
        }
      }
      
      return camelCaseTemplate;
    } catch (error) {
      console.error(`更新ID为${id}的商品属性模板失败:`, error);
      throw error;
    }
  }
  
  /**
   * 校验更新时的模板名称是否唯一
   * @param {number} id 模板ID
   * @param {string} name 模板名称
   * @throws {Error} 如果名称已被其他模板使用，抛出错误
   */
  async validateTemplateNameForUpdate(id, name) {
    const existingTemplate = await this.prisma.goodsAttributeSet.findFirst({
      where: {
        name: name,
        id: {
          not: BigInt(id)
        },
        deleted_at: null
      }
    });
    
    if (existingTemplate) {
      throw new Error(`商品属性模板名称"${name}"已存在`);
    }
  }
  
  /**
   * 删除商品属性模板
   * @param {number} id 模板ID
   * @returns {Promise<void>}
   */
  async deleteGoodsAttrTemplate(id) {
    try {
      // 检查模板是否存在
      const template = await this.prisma.goodsAttributeSet.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });
      
      if (!template) {
        throw new Error(`ID为${id}的商品属性模板不存在`);
      }
      
      // 检查是否有商品使用了该模板
      // 通过 goods_attribute_values -> goods_attribute_items -> goods_attribute_set_id 的关系链查询
      const relatedProducts = await this.prisma.goodsSpu.count({
        where: {
          goods_attribute_values: {
            some: {
              goods_attribute_items: {
                goods_attribute_set_id: BigInt(id)
              }
            }
          },
          deleted_at: null
        }
      });
      
      if (relatedProducts > 0) {
        throw new Error(`无法删除，该模板已被${relatedProducts}个商品关联使用`);
      }
      
      // 设置删除时间（软删除）
      const now = BigInt(Date.now());
      
      // 删除模板
      await this.prisma.goodsAttributeSet.update({
        where: {
          id: BigInt(id)
        },
        data: {
          deleted_at: now
        }
      });
      
      // 删除模板的所有参数项
      await this.prisma.goodsAttributeItem.updateMany({
        where: {
          goods_attribute_set_id: BigInt(id),
          deleted_at: null
        },
        data: {
          deleted_at: now
        }
      });
      
      // 删除模板与分类的关联
      await this.prisma.goodsAttributeSetCategoryAssociation.deleteMany({
        where: {
          goods_attribute_set_id: BigInt(id)
        }
      });
    } catch (error) {
      console.error(`删除ID为${id}的商品属性模板失败:`, error);
      throw error;
    }
  }

  /**
   * 添加属性模板参数
   * @param {number} templateId 模板ID
   * @param {Object} paramData 参数数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回新创建的参数
   */
  async addGoodsAttrTemplateParam(templateId, paramData, userId = null) {
    try {
      // 业务校验：检查模板是否存在
      await this.validateTemplateExists(templateId);
      
      // 业务校验：检查同一模板下是否已存在同名参数
      await this.validateParamNameUnique(templateId, paramData.name);
      
      // 业务校验：检查参数类型与值的一致性
      this.validateParamTypeAndValue(paramData.type, paramData.value);
      
      // 准备参数数据
      const now = BigInt(Date.now()); // 当前时间戳（毫秒）
      const id = snowflake.generateSnowflakeId(); // 生成雪花ID
      
      const paramToCreate = {
        id: id, // 添加必需的id字段
        goods_attribute_set_id: BigInt(templateId),
        name: paramData.name,
        type: paramData.type,
        value: paramData.value || '',
        is_required: paramData.isRequired ? 1 : 0,
        is_filterable: paramData.isFilterable ? 1 : 0,
        is_enabled: paramData.isEnabled !== undefined ? (paramData.isEnabled ? 1 : 0) : 1,
        sort_order: paramData.sort || 0,
        created_at: now,
        updated_at: now,
        created_by: userId ? BigInt(userId) : null,
        updated_by: userId ? BigInt(userId) : null
      };
      
      // 创建参数
      const newParam = await this.prisma.goodsAttributeItem.create({
        data: paramToCreate
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(newParam);
      const camelCaseParam = convertKeysToCamelCase(processedParam);
      camelCaseParam.sort = camelCaseParam.sortOrder;
      
      return camelCaseParam;
    } catch (error) {
      console.error('添加属性模板参数失败:', error);
      throw error;
    }
  }
  
  /**
   * 校验模板是否存在
   * @param {number} templateId 模板ID
   * @throws {Error} 如果模板不存在，抛出错误
   */
  async validateTemplateExists(templateId) {
    const template = await this.prisma.goodsAttributeSet.findFirst({
      where: {
        id: BigInt(templateId),
        deleted_at: null
      }
    });
    
    if (!template) {
      throw new Error(`ID为${templateId}的模板不存在`);
    }
  }
  
  /**
   * 校验参数名称在同模板下是否唯一
   * @param {number} templateId 模板ID
   * @param {string} paramName 参数名称
   * @throws {Error} 如果名称已存在，抛出错误
   */
  async validateParamNameUnique(templateId, paramName) {
    const existingParam = await this.prisma.goodsAttributeItem.findFirst({
      where: {
        goods_attribute_set_id: BigInt(templateId),
        name: paramName,
        deleted_at: null
      }
    });
    
    if (existingParam) {
      throw new Error(`模板中已存在同名参数"${paramName}"`);
    }
  }
  
  /**
   * 校验参数类型与值的一致性
   * @param {string} type 参数类型
   * @param {string} value 参数值
   * @throws {Error} 如果类型与值不一致，抛出错误
   */
  validateParamTypeAndValue(type, value) {
    if (['radio', 'checkbox', 'select'].includes(type) && (!value || value.trim() === '')) {
      throw new Error(`类型为${type}的参数必须提供可选值`);
    }
  }
  
  /**
   * 获取属性模板的所有参数
   * @param {number} templateId 模板ID
   * @returns {Promise<Array>} 返回参数列表
   */
  async getGoodsAttrTemplateParams(templateId) {
    try {
      // 业务校验：检查模板是否存在
      await this.validateTemplateExists(templateId);
      
      // 获取参数列表
      const params = await this.prisma.goodsAttributeItem.findMany({
        where: {
          goods_attribute_set_id: BigInt(templateId),
          deleted_at: null
        },
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParams = handleBigInt(params);
      const camelCaseParams = convertKeysToCamelCase(processedParams);
      
      // 为每个参数项添加排序字段
      camelCaseParams.forEach(param => {
        param.sort = param.sortOrder;
      });
      
      return camelCaseParams;
    } catch (error) {
      console.error(`获取模板ID为${templateId}的参数列表失败:`, error);
      throw error;
    }
  }

  /**
   * 更新属性模板参数
   * @param {number} paramId 参数ID
   * @param {Object} paramData 参数数据
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 返回更新后的参数
   */
  async updateGoodsAttrTemplateParam(paramId, paramData, userId = null) {
    try {
      // 业务校验：检查参数是否存在
      const existingParam = await this.prisma.goodsAttributeItem.findFirst({
        where: {
          id: BigInt(paramId),
          deleted_at: null
        }
      });
      
      if (!existingParam) {
        throw new Error(`ID为${paramId}的参数不存在`);
      }
      
      // 业务校验：如果要更新名称，检查同一模板下是否已存在同名参数
      if (paramData.name && paramData.name !== existingParam.name) {
        await this.validateParamNameForUpdate(existingParam.goods_attribute_set_id.toString(), paramId, paramData.name);
      }
      
      // 业务校验：检查参数类型与值的一致性
      if (paramData.type) {
        this.validateParamTypeAndValue(paramData.type, paramData.value || existingParam.value);
      }
      
      // 准备更新数据
      const now = BigInt(Date.now()); // 当前时间戳（毫秒）
      
      const paramToUpdate = {
        updated_at: now,
        updated_by: userId ? BigInt(userId) : null
      };
      
      // 如果提供了名称，更新名称
      if (paramData.name) {
        paramToUpdate.name = paramData.name;
      }
      
      // 如果提供了类型，更新类型
      if (paramData.type) {
        paramToUpdate.type = paramData.type;
      }
      
      // 如果提供了值，更新值
      if (paramData.value !== undefined) {
        paramToUpdate.value = paramData.value;
      }
      
      // 如果提供了是否必填，更新是否必填
      if (paramData.isRequired !== undefined) {
        paramToUpdate.is_required = paramData.isRequired ? 1 : 0;
      }
      
      // 如果提供了是否可筛选，更新是否可筛选
      if (paramData.isFilterable !== undefined) {
        paramToUpdate.is_filterable = paramData.isFilterable ? 1 : 0;
      }
      
      // 如果提供了是否启用，更新是否启用
      if (paramData.isEnabled !== undefined) {
        paramToUpdate.is_enabled = paramData.isEnabled ? 1 : 0;
      }
      
      // 如果提供了排序值，更新排序值
      if (paramData.sort !== undefined) {
        paramToUpdate.sort_order = paramData.sort;
      }
      
      // 更新参数
      const updatedParam = await this.prisma.goodsAttributeItem.update({
        where: {
          id: BigInt(paramId)
        },
        data: paramToUpdate
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParam = handleBigInt(updatedParam);
      const camelCaseParam = convertKeysToCamelCase(processedParam);
      camelCaseParam.sort = camelCaseParam.sortOrder;
      
      return camelCaseParam;
    } catch (error) {
      console.error(`更新ID为${paramId}的属性模板参数失败:`, error);
      throw error;
    }
  }
  
  /**
   * 校验更新时的参数名称在同模板下是否唯一
   * @param {number} templateId 模板ID
   * @param {number} paramId 参数ID
   * @param {string} paramName 参数名称
   * @throws {Error} 如果名称已被其他参数使用，抛出错误
   */
  async validateParamNameForUpdate(templateId, paramId, paramName) {
    const existingParam = await this.prisma.goodsAttributeItem.findFirst({
      where: {
        goods_attribute_set_id: BigInt(templateId),
        name: paramName,
        id: {
          not: BigInt(paramId)
        },
        deleted_at: null
      }
    });
    
    if (existingParam) {
      throw new Error(`模板中已存在同名参数"${paramName}"`);
    }
  }
  /**
   * 删除属性模板参数
   * @param {number} paramId 参数ID
   * @returns {Promise<void>}
   */
  async deleteGoodsAttrTemplateParam(paramId) {
    try {
      // 业务校验：检查参数是否存在
      const existingParam = await this.prisma.goodsAttributeItem.findFirst({
        where: {
          id: BigInt(paramId),
          deleted_at: null
        }
      });
      
      if (!existingParam) {
        throw new Error(`ID为${paramId}的参数不存在`);
      }
      
      // 设置删除时间（软删除）
      const now = BigInt(Date.now());
      
      // 删除参数
      await this.prisma.goodsAttributeItem.update({
        where: {
          id: BigInt(paramId)
        },
        data: {
          deleted_at: now
        }
      });
    } catch (error) {
      console.error(`删除ID为${paramId}的属性模板参数失败:`, error);
      throw error;
    }
  }

  /**
   * 获取属性模板的所有参数
   * @param {number} templateId 模板ID
   * @returns {Promise<Array>} 返回参数列表
   */
  async getGoodsAttrTemplateParams(templateId) {
    try {
      // 业务校验：检查模板是否存在
      await this.validateTemplateExists(templateId);
      
      // 获取参数列表
      const params = await this.prisma.goodsAttributeItem.findMany({
        where: {
          goods_attribute_set_id: BigInt(templateId),
          deleted_at: null
        },
        orderBy: {
          sort_order: 'asc' // 按排序字段升序排列
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedParams = handleBigInt(params);
      const camelCaseParams = convertKeysToCamelCase(processedParams);
      
      // 为每个参数项添加排序字段
      camelCaseParams.forEach(param => {
        param.sort = param.sortOrder;
      });
      
      return camelCaseParams;
    } catch (error) {
      console.error(`获取模板ID为${templateId}的参数列表失败:`, error);
      throw error;
    }
  }
}

module.exports = GoodsAttrTemplateService;
