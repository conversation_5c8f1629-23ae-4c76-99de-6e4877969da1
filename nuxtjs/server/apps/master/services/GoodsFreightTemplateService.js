/**
 * 运费模板服务
 */
const GoodsFreightTemplateDto = require('../dto/GoodsFreightTemplateDto');
const GoodsFreightConfigDto = require('../dto/GoodsFreightConfigDto');
const GoodsFreightRegionRelationDto = require('../dto/GoodsFreightRegionRelationDto');
const GoodsFreightTemplateModel = require('../models/GoodsFreightTemplateModel');
const ChargeTypeEnum = require('../constants/ChargeTypeEnum');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 安全地序列化包含BigInt的对象
 * @param {Object} obj 要序列化的对象
 * @returns {string} 序列化后的JSON字符串
 */
function safeStringify(obj) {
  return JSON.stringify(obj, (key, value) => 
    typeof value === 'bigint' ? value.toString() : value
  );
}

class GoodsFreightTemplateService {
  constructor(prisma) {
    this.prisma = prisma;
    this.goodsFreightTemplateModel = new GoodsFreightTemplateModel(this.prisma);
  }

  /**
   * 获取运费模板列表
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} 运费模板列表和总数
   */
  /**
   * 将对象的键从下划线命名法转换为驼峰命名法
   * @param {Object|Array} data - 要转换的数据
   * @returns {Object|Array} 转换后的数据
   */
  convertToCamelCase(data) {
    if (data === null || data === undefined) {
      return data;
    }
    
    // 处理数组
    if (Array.isArray(data)) {
      return data.map(item => this.convertToCamelCase(item));
    }
    
    // 处理对象
    if (typeof data === 'object' && !Array.isArray(data)) {
      const result = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          // 将键从下划线转换为驼峰
          const camelKey = key.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
          
          // 递归转换值
          let value = data[key];
          if (typeof value === 'object' && value !== null) {
            value = this.convertToCamelCase(value);
          }
          
          result[camelKey] = value;
        }
      }
      return result;
    }
    
    return data;
  }
  
  async getList({ page, pageSize, filters = {} }) {
    try {
      // 调整过滤条件格式，适配模型层期望的格式
      const modelFilters = {};
      
      if (filters.name) {
        modelFilters.name = filters.name;
      }
      
      if (filters.chargeType) {
        modelFilters.charge_type = filters.chargeType;
      }
      
      // 确保页码和页大小是有效整数，避免NaN
      const validPage = parseInt(page) || 1;
      const validPageSize = parseInt(pageSize) || 10;
      
      // 获取数据
      const result = await this.goodsFreightTemplateModel.getGoodsFreightTemplatesWithRegions(
        modelFilters, 
        validPage, 
        validPageSize
      );
      
      // 将返回的数据从下划线转换为驼峰格式
      const camelCaseItems = this.convertToCamelCase(result.list || []);
      
      // 确保返回结果中的items不为undefined
      return { 
        items: camelCaseItems, 
        total: result.total || 0,
        page: result.page || validPage,
        pageSize: result.pageSize || validPageSize
      };
    } catch (error) {
      console.error('获取运费模板列表服务失败:', error);
      throw error;
    }
  }

  buildWhereClause(filters) {
    const where = {};
    
    if (filters.name) {
      // 支持模糊搜索，将 { contains: "关键词" } 转换为 Prisma 查询格式
      if (typeof filters.name === 'object' && filters.name.contains) {
        where.name = {
          contains: filters.name.contains,
          mode: 'insensitive' // 不区分大小写
        };
      } else {
        // 如果是字符串，直接模糊匹配
        where.name = {
          contains: filters.name,
          mode: 'insensitive'
        };
      }
    }
    if (filters.chargeType) {
      where.charge_type = filters.chargeType;
    }
    if (filters.isDefault !== undefined) {
      where.isDefault = filters.isDefault;
    }

    return where;
  }

  /**
   * 获取指定ID的运费模板
   * @param {number} id - 运费模板ID
   * @returns {Promise<GoodsFreightTemplateDto|null>} 运费模板DTO
   */
  async getGoodsFreightTemplateById(id) {
    try {
      return await this.goodsFreightTemplateModel.getGoodsFreightTemplateById(id);
    } catch (error) {
      console.error(`获取运费模板(ID: ${id})服务失败:`, error);
      throw error;
    }
  }

  /**
   * 添加运费模板
   * @param {Object} templateData - 运费模板数据
   * @param {number} userId - 当前用户ID
   * @returns {Promise<GoodsFreightTemplateDto>} 添加后的运费模板DTO
   */
  async addGoodsFreightTemplate(templateData, userId) {
    try {
      // 调试日志：输出接收到的模板数据
      console.log('接收到的运费模板数据:', safeStringify(templateData));
      
      // 开启事务以确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 1. 创建运费模板DTO并验证
        const templateDto = new GoodsFreightTemplateDto({
          name: templateData.name,
          charge_type: templateData.charge_type,
          created_by: userId,
          updated_by: userId
        });
        
        // 验证基本信息
        const baseValidation = {
          isValid: true,
          errors: []
        };
        
        if (!templateDto.name) {
          baseValidation.isValid = false;
          baseValidation.errors.push('运费模板名称不能为空');
        } else if (templateDto.name.length > 100) {
          baseValidation.isValid = false;
          baseValidation.errors.push('运费模板名称不能超过100个字符');
        }
        
        if (!ChargeTypeEnum.isValidChargeType(templateDto.charge_type)) {
          baseValidation.isValid = false;
          baseValidation.errors.push(`计价方式必须是以下之一：${ChargeTypeEnum.getAllOptions().map(opt => `${opt.value}-${opt.label}`).join('，')}`);
        }
        
        if (!baseValidation.isValid) {
          throw new Error(baseValidation.errors.join('; '));
        }

        // 检查模板名称是否已存在
        const nameExists = await this.goodsFreightTemplateModel.checkNameExists(templateDto.name);
        if (nameExists) {
          throw new Error(`运费模板名称"${templateDto.name}"已存在，请使用其他名称`);
        }

        // 2. 直接在事务中创建运费模板基本信息（避免嵌套事务）
        const templateData_db = templateDto.toDbObject();
        const templateId = generateSnowflakeId();
        
        const createdTemplate = await tx.goodsFreightTemplates.create({
          data: {
            id: BigInt(templateId),
            name: templateData_db.name,
            charge_type: templateData_db.charge_type,
            created_at: BigInt(templateData_db.created_at),
            updated_at: BigInt(templateData_db.updated_at),
            created_by: templateData_db.created_by ? BigInt(templateData_db.created_by) : null,
            updated_by: templateData_db.updated_by ? BigInt(templateData_db.updated_by) : null
          }
        });
        
        if (!createdTemplate || !createdTemplate.id) {
          throw new Error('创建运费模板失败');
        }
        
        // 3. 处理运费配置数据
        console.log('运费配置数据类型:', typeof templateData.freight_configs);
        console.log('运费配置数据是否为数组:', Array.isArray(templateData.freight_configs));
        console.log('运费配置数据长度:', templateData.freight_configs ? templateData.freight_configs.length : '无');
        
        if (!templateData.freight_configs || templateData.freight_configs.length === 0) {
          console.log('错误：未找到有效的运费配置');
          throw new Error('至少需要一个运费配置');
        }
        
        // 4. 遍历处理运费配置（在同一事务中）
        const freightConfigPromises = templateData.freight_configs.map(async (configData) => {
          // 判断是否为全国默认配置
          const isDefault = configData.is_default === 1 || configData.is_default === true || configData.is_default === '1' || (configData.name === '全国');
          
          // 创建运费配置DTO
          const configDto = new GoodsFreightConfigDto({
            freight_template_id: createdTemplate.id.toString(),
            first_item: configData.first_item || 1,
            first_fee: configData.first_fee || 0,
            additional_item: configData.additional_item || 1,
            additional_fee: configData.additional_fee || 0,
            is_default: isDefault ? 1 : 0,
            created_by: userId,
            updated_by: userId
          });
          
          // 在事务中保存运费配置
          const configData_db = configDto.toDbObject();
          const configId = generateSnowflakeId();
          
          const createdConfig = await tx.goodsFreightConfig.create({
            data: {
              id: BigInt(configId),
              freight_template_id: BigInt(configData_db.freight_template_id),
              first_item: configData_db.first_item,
              first_fee: configData_db.first_fee,
              additional_item: configData_db.additional_item,
              additional_fee: configData_db.additional_fee,
              is_default: configData_db.is_default,
              created_at: BigInt(configData_db.created_at),
              updated_at: BigInt(configData_db.updated_at),
              created_by: configData_db.created_by ? BigInt(configData_db.created_by) : null,
              updated_by: configData_db.updated_by ? BigInt(configData_db.updated_by) : null
            }
          });
          
          // 5. 如果不是默认配置，处理区域关联（在同一事务中）
          let regionRelations = [];
          if (!isDefault && configData.areas && configData.areas.length > 0) {
            // 为每个区域创建关联记录
            const regionRelationPromises = configData.areas.map(async (area) => {
              // 根据区域代码获取区域信息
              const regionInfo = await this.getRegionInfoByCode(area.code);
              
              const regionDto = new GoodsFreightRegionRelationDto({
                freight_config_id: createdConfig.id.toString(),
                region_code: area.code,
                region_name: regionInfo.region_name || '',
                parent_name: regionInfo.parent_name || '',
                created_by: userId,
                updated_by: userId
              });
              
              // 在事务中保存区域关联
              const regionData_db = regionDto.toDbObject();
              const regionId = generateSnowflakeId();
              
              return await tx.goodsFreightRegionRelation.create({
                data: {
                  id: BigInt(regionId),
                  freight_config_id: BigInt(regionData_db.freight_config_id),
                  region_code: regionData_db.region_code,
                  region_name: regionData_db.region_name,
                  parent_name: regionData_db.parent_name,
                  created_at: BigInt(regionData_db.created_at),
                  updated_at: BigInt(regionData_db.updated_at)
                }
              });
            });
            
            regionRelations = await Promise.all(regionRelationPromises);
          }
          
          // 返回完整的配置数据，包含区域关联
          return {
            ...createdConfig,
            regionRelations: regionRelations.map(relation => ({
              regionCode: relation.region_code,
              regionName: relation.region_name,
              parentName: relation.parent_name
            }))
          };
        });
        
        const createdConfigs = await Promise.all(freightConfigPromises);
        
        // 6. 直接构建返回数据，避免在事务中重新查询可能导致的null问题
        const result = {
          id: createdTemplate.id.toString(),
          name: createdTemplate.name,
          chargeType: createdTemplate.charge_type,
          charge_type: createdTemplate.charge_type,
          createdAt: createdTemplate.created_at,
          updatedAt: createdTemplate.updated_at,
          createdBy: createdTemplate.created_by ? createdTemplate.created_by.toString() : null,
          updatedBy: createdTemplate.updated_by ? createdTemplate.updated_by.toString() : null,
          freightConfigs: createdConfigs.map(config => ({
            id: config.id.toString(),
            firstItem: config.first_item,
            firstFee: parseFloat(config.first_fee),
            additionalItem: config.additional_item,
            additionalFee: parseFloat(config.additional_fee),
            isDefault: config.is_default === 1,
            regionRelations: config.regionRelations || []
          }))
        };
        
        return result;
      });
    } catch (error) {
      console.error('添加运费模板服务失败:', error);
      throw error;
    }
  }

  /**
   * 更新运费模板
   * @param {number} id - 运费模板ID
   * @param {Object} templateData - 运费模板数据
   * @param {number} userId - 当前用户ID
   * @returns {Promise<GoodsFreightTemplateDto>} 更新后的运费模板DTO
   */
  async updateGoodsFreightTemplate(id, templateData, userId) {
    try {
      // 开启事务以确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 1. 获取现有模板确认存在
        const existingTemplate = await this.goodsFreightTemplateModel.getGoodsFreightTemplateById(id);
        if (!existingTemplate) {
          throw new Error(`运费模板(ID: ${id})不存在`);
        }

        // 2. 更新运费模板基本信息
        const templateDto = new GoodsFreightTemplateDto({
          ...existingTemplate,
          name: templateData.name !== undefined ? templateData.name : existingTemplate.name,
          charge_type: templateData.charge_type !== undefined ? templateData.charge_type : existingTemplate.charge_type,
          updated_at: Date.now(),
          updated_by: userId
        });
        
        // 验证基本信息
        const baseValidation = {
          isValid: true,
          errors: []
        };
        
        if (!templateDto.name) {
          baseValidation.isValid = false;
          baseValidation.errors.push('运费模板名称不能为空');
        } else if (templateDto.name.length > 100) {
          baseValidation.isValid = false;
          baseValidation.errors.push('运费模板名称不能超过100个字符');
        }
        
        if (!ChargeTypeEnum.isValidChargeType(templateDto.charge_type)) {
          baseValidation.isValid = false;
          baseValidation.errors.push(`计价方式必须是以下之一：${ChargeTypeEnum.getAllOptions().map(opt => `${opt.value}-${opt.label}`).join('，')}`);
        }
        
        if (!baseValidation.isValid) {
          throw new Error(baseValidation.errors.join('; '));
        }

        // 检查模板名称是否已存在（排除当前模板）
        const nameExists = await this.goodsFreightTemplateModel.checkNameExists(templateDto.name, id);
        if (nameExists) {
          throw new Error(`运费模板名称"${templateDto.name}"已存在，请使用其他名称`);
        }

        // 3. 直接在事务中更新模板基本信息（避免嵌套事务）
        const templateData_db = templateDto.toDbObject();
        await tx.goodsFreightTemplates.update({
          where: {
            id: BigInt(id)
          },
          data: {
            name: templateData_db.name,
            charge_type: templateData_db.charge_type,
            updated_at: BigInt(templateData_db.updated_at),
            updated_by: templateData_db.updated_by ? BigInt(templateData_db.updated_by) : null
          }
        });
        
        // 4. 处理运费配置数据 - 如果有配置更新
        if (templateData.freight_configs && templateData.freight_configs.length > 0) {
          // 先在事务中删除原有的所有运费配置和区域关联
          
          // 查询所有相关的运费配置
          const configs = await tx.goodsFreightConfig.findMany({
            where: {
              freight_template_id: BigInt(id),
              deleted_at: null
            }
          });
          
          const configIds = configs.map(config => config.id);
          
          // 软删除所有相关的区域关联
          if (configIds.length > 0) {
            await tx.goodsFreightRegionRelation.updateMany({
              where: {
                freight_config_id: {
                  in: configIds
                },
                deleted_at: null
              },
              data: {
                deleted_at: BigInt(Date.now()),
                updated_at: BigInt(Date.now())
              }
            });
          }
          
          // 软删除所有运费配置
          await tx.goodsFreightConfig.updateMany({
            where: {
              freight_template_id: BigInt(id),
              deleted_at: null
            },
            data: {
              deleted_at: BigInt(Date.now()),
              updated_at: BigInt(Date.now()),
              updated_by: userId ? BigInt(userId) : null
            }
          });
          
          // 5. 遍历处理运费配置（在同一事务中）
          const freightConfigPromises = templateData.freight_configs.map(async (configData) => {
            // 判断是否为全国默认配置
            const isDefault = configData.is_default === 1 || configData.is_default === true || (configData.name === '全国');
            
            // 创建运费配置DTO
            const configDto = new GoodsFreightConfigDto({
              freight_template_id: id,
              first_item: configData.firstPiece || configData.first_item || 1,
              first_fee: configData.firstFee || configData.first_fee || 0,
              additional_item: configData.continuation || configData.additional_item || 1,
              additional_fee: configData.renew || configData.additional_fee || 0,
              is_default: isDefault ? 1 : 0,
              created_by: userId,
              updated_by: userId
            });
            
            // 在事务中保存运费配置
            const configData_db = configDto.toDbObject();
            const configId = generateSnowflakeId();
            
            const createdConfig = await tx.goodsFreightConfig.create({
              data: {
                id: BigInt(configId),
                freight_template_id: BigInt(configData_db.freight_template_id),
                first_item: configData_db.first_item,
                first_fee: configData_db.first_fee,
                additional_item: configData_db.additional_item,
                additional_fee: configData_db.additional_fee,
                is_default: configData_db.is_default,
                created_at: BigInt(configData_db.created_at),
                updated_at: BigInt(configData_db.updated_at),
                created_by: configData_db.created_by ? BigInt(configData_db.created_by) : null,
                updated_by: configData_db.updated_by ? BigInt(configData_db.updated_by) : null
              }
            });
            
            // 6. 如果不是默认配置，处理区域关联（在同一事务中）
            if (!isDefault && configData.areas && configData.areas.length > 0) {
              // 为每个区域创建关联记录
              const regionRelationPromises = configData.areas.map(async (area) => {
                // 根据区域代码获取区域信息
                const regionInfo = await this.getRegionInfoByCode(area.code);
                
                const regionDto = new GoodsFreightRegionRelationDto({
                  freight_config_id: createdConfig.id.toString(),
                  region_code: area.code,
                  region_name: area.name || regionInfo.region_name || '',
                  parent_name: area.parentName || regionInfo.parent_name || '',
                  created_by: userId,
                  updated_by: userId
                });
                
                // 在事务中保存区域关联
                const regionData_db = regionDto.toDbObject();
                const regionId = generateSnowflakeId();
                
                return await tx.goodsFreightRegionRelation.create({
                  data: {
                    id: BigInt(regionId),
                    freight_config_id: BigInt(regionData_db.freight_config_id),
                    region_code: regionData_db.region_code,
                    region_name: regionData_db.region_name,
                    parent_name: regionData_db.parent_name,
                    created_at: BigInt(regionData_db.created_at),
                    updated_at: BigInt(regionData_db.updated_at)
                  }
                });
              });
              
              await Promise.all(regionRelationPromises);
            }
            
            return createdConfig;
          });
          
          await Promise.all(freightConfigPromises);
        }
        
        // 7. 重新获取完整的模板数据（包含关联配置和区域）
        return await this.goodsFreightTemplateModel.getGoodsFreightTemplateById(id);
      });
    } catch (error) {
      console.error(`更新运费模板(ID: ${id})服务失败:`, error);
      throw error;
    }
  }

  /**
   * 删除运费模板
   * @param {number} id - 运费模板ID
   * @param {number} userId - 当前用户ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteGoodsFreightTemplate(id, userId) {
    try {
      // 开启事务以确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 1. 检查模板是否存在
        const existingTemplate = await tx.goodsFreightTemplates.findFirst({
          where: {
            id: BigInt(id),
            deleted_at: null
          }
        });

        if (!existingTemplate) {
          throw new Error(`运费模板(ID: ${id})不存在`);
        }

        // 2. 检查是否有商品正在使用该运费模板
        // TODO: 添加商品使用检查逻辑
        // const goodsCount = await tx.goods.count({
        //   where: {
        //     freight_template_id: BigInt(id),
        //     deleted_at: null
        //   }
        // });
        // if (goodsCount > 0) {
        //   throw new Error(`该运费模板正在被 ${goodsCount} 个商品使用，无法删除`);
        // }

        // 3. 查询所有相关的运费配置
        const configs = await tx.goodsFreightConfig.findMany({
          where: {
            freight_template_id: BigInt(id),
            deleted_at: null
          }
        });
        
        const configIds = configs.map(config => config.id);
        
        // 4. 软删除所有相关的区域关联
        if (configIds.length > 0) {
          await tx.goodsFreightRegionRelation.updateMany({
            where: {
              freight_config_id: {
                in: configIds
              },
              deleted_at: null
            },
            data: {
              deleted_at: BigInt(Date.now()),
              updated_at: BigInt(Date.now())
            }
          });
        }
        
        // 5. 软删除所有运费配置
        await tx.goodsFreightConfig.updateMany({
          where: {
            freight_template_id: BigInt(id),
            deleted_at: null
          },
          data: {
            deleted_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
            updated_by: userId ? BigInt(userId) : null
          }
        });

        // 6. 软删除运费模板
        await tx.goodsFreightTemplates.update({
          where: {
            id: BigInt(id)
          },
          data: {
            deleted_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
            updated_by: userId ? BigInt(userId) : null
          }
        });

        return true;
      });
    } catch (error) {
      console.error(`删除运费模板(ID: ${id})服务失败:`, error);
      throw error;
    }
  }

  /**
   * 根据区域代码获取区域信息
   * @param {string} regionCode - 区域代码，格式如："310000,310200" 或 "310000"
   * @returns {Promise<Object>} 区域信息 {region_name, parent_name}
   */
  async getRegionInfoByCode(regionCode) {
    try {
      if (!regionCode) {
        return { region_name: '', parent_name: '' };
      }
      
      // 解析区域代码，支持多级格式如 "310000,310200"
      const codes = regionCode.split(',').map(code => code.trim()).filter(code => code);
      
      if (codes.length === 0) {
        return { region_name: '', parent_name: '' };
      }
      
      let region_name = '';
      let parent_name = '';
      
      // 查询最后一级区域（最具体的区域）
      const lastCode = codes[codes.length - 1];
      const region = await this.prisma.region.findFirst({
        where: {
          citycode: parseInt(lastCode)
        }
      });
      
      if (region) {
        region_name = region.cityname || '';
        
        // 如果有父级区域代码，查询父级区域名称
        if (region.parent_id && region.parent_id > 0) {
          const parentRegion = await this.prisma.region.findFirst({
            where: {
              id: region.parent_id
            }
          });
          
          if (parentRegion) {
            parent_name = parentRegion.cityname || '';
          }
        } else if (codes.length > 1) {
          // 如果没有 parent_id，但有多级代码，使用上一级代码查询
          const parentCode = codes[codes.length - 2];
          const parentRegion = await this.prisma.region.findFirst({
            where: {
              citycode: parseInt(parentCode)
            }
          });
          
          if (parentRegion) {
            parent_name = parentRegion.cityname || '';
          }
        }
      }
      
      return { region_name, parent_name };
    } catch (error) {
      console.error(`获取区域信息失败，区域代码: ${regionCode}`, error);
      return { region_name: '', parent_name: '' };
    }
  }

  /**
   * 获取计价方式文本
   * @param {number} chargeType - 计价方式代码
   * @returns {string} 计价方式文本
   */
  getChargeTypeText(chargeType) {
    return ChargeTypeEnum.getChargeTypeText(chargeType);
  }
}

module.exports = GoodsFreightTemplateService;
