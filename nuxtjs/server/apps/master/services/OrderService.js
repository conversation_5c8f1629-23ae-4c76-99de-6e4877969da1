/**
 * 订单服务
 * 负责处理订单业务逻辑
 */
const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const { snakeToCamel } = require('../../../shared/utils/format');

/**
 * 递归处理数据，将所有层级的下划线命名转换为驼峰命名
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const recursiveSnakeToCamel = (data) => {
  if (data === null || data === undefined) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => recursiveSnakeToCamel(item));
  }
  
  // 处理对象，排除Date类型
  if (typeof data === 'object' && !(data instanceof Date)) {
    // 先使用现有的snakeToCamel函数处理当前层级
    const camelData = snakeToCamel(data);
    
    // 然后递归处理每个属性
    const result = {};
    for (const key in camelData) {
      result[key] = recursiveSnakeToCamel(camelData[key]);
    }
    
    return result;
  }
  
  // 其他类型直接返回
  return data;
};
const OrderModel = require('../models/OrderModel/OrderModel');
const OrderItemModel = require('../models/OrderModel/OrderItemModel');
const OrderLogModel = require('../models/OrderModel/OrderLogModel');
const OrderShippingInfoModel = require('../models/OrderModel/OrderShippingInfoModel');
const OrderSalespeopleModel = require('../models/OrderModel/OrderSalespeopleModel');
const OrderFollowerModel = require('../models/OrderModel/OrderFollowerModel');
const OrderRemarkModel = require('../models/OrderModel/OrderRemarkModel');
const OrderPackageModel = require('../models/OrderPackageModel');
const GoodsSkuModel = require('../models/GoodsSkuModel');
const InvoiceModel = require('../models/InvoiceModel');
const InvoiceApplicationModel = require('../models/InvoiceApplicationModel');
const RegionModel = require('../models/RegionModel');
const ThirdPartyOrderGoodsService = require('./ThirdPartyOrderGoodsService');
const CustomerOrderAssociationService = require('../../crm/services/CustomerOrderAssociationService');
const CrmOrderManagementService = require('../../crm/services/CrmOrderManagementService');
const OrderSourceEnum = require('../constants/OrderSourceEnum');
const OrderTypeEnum = require('../constants/OrderTypeEnum');
const OrderStatusEnum = require('../constants/OrderStatusEnum');
const PaymentStatusEnum = require('../constants/PaymentStatusEnum');
const PaymentMethodEnum = require('../constants/PaymentMethodEnum');
const ShippingStatusEnum = require('../constants/ShippingStatusEnum');
const InvoiceTypeEnum = require('../constants/InvoiceTypeEnum');
const { ORDER_INVOICE_STATUS } = require('../constants/InvoiceConstants');

class OrderService {
  /**
   * 构造函数
   */
  constructor() {
    const prisma = prismaManager.getClient('base');
    this.prisma = prisma;
    this.orderModel = new OrderModel();
    this.orderItemModel = new OrderItemModel();
    this.orderLogModel = new OrderLogModel();
    this.orderShippingInfoModel = new OrderShippingInfoModel();
    this.orderSalespeopleModel = new OrderSalespeopleModel();
    this.orderFollowerModel = new OrderFollowerModel();
    this.orderRemarkModel = OrderRemarkModel; // 直接使用导入的实例
    this.goodsSkuModel = GoodsSkuModel; // 直接使用导入的实例
    this.invoiceModel = new InvoiceModel();
    this.invoiceApplicationModel = new InvoiceApplicationModel();
    this.customerOrderAssociationService = new CustomerOrderAssociationService(prismaManager.getClient('crm'));
    this.crmOrderManagementService = new CrmOrderManagementService();
    this.thirdPartyOrderGoodsService = new ThirdPartyOrderGoodsService(this.prisma);
  }

  /**
   * 将订单类型数字转换为字符串
   * @param {number} typeCode - 订单类型数字代码
   * @returns {string} - 订单类型字符串
   */
  mapOrderTypeToString(typeCode) {
    switch (parseInt(typeCode)) {
      case OrderTypeEnum.MALL:
        return 'normal';    // 商城订单对应为normal
      case OrderTypeEnum.SYSTEM:
        return 'wholesale'; // 系统订单对应为wholesale
      default:
        return 'normal';    // 默认为normal
    }
  }

  /**
   * 将订单来源数字转换为字符串
   * @param {number} sourceCode - 订单来源数字代码
   * @returns {string} - 订单来源字符串
   */
  mapOrderSourceToString(sourceCode) {
    switch (parseInt(sourceCode)) {
      case OrderSourceEnum.ADMIN:
        return 'offline'; // 后台创建对应为offline
      case OrderSourceEnum.MALL:
        return 'web';     // 商城下单对应为web
      case OrderSourceEnum.SYSTEM:
      default:
        return 'system';  // 系统创建对应为system
    }
  }

  /**
   * 生成订单编号
   * @returns {string} - 订单编号
   */
  generateOrderNumber() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    return `JLY${year}${month}${day}${hours}${minutes}${seconds}${random}`;
  }

  /**
   * 获取订单列表
   * @param {Object} query - 查询参数
   * @returns {Promise<Object>} - 订单列表数据
   */
  async getOrders(query) {
    try {
      // 提取分页和排序参数，不应该包含在过滤条件中
      const {
        page,
        pageSize,
        sortField = 'created_at',
        sortOrder = 'desc',
        orderNumber,
        thirdPartyPlatformId, // 添加第三方平台ID参数
        orderStatus,
        paymentStatus,
        shippingStatus,
        customerName,
        customerPhone,
        orderType,
        orderSource,
        startTime,
        endTime,
        timeRange // 添加时间范围参数（recent6m: 近6个月，before6m: 6个月前）
      } = query;

      // 构建查询条件（只包含过滤条件，不包含分页和排序参数）
      const filters = {};

      // 添加过滤条件
      if (orderNumber) {
        try {
          // 将订单号转换为BigInt类型
          filters.id = BigInt(orderNumber);
        } catch (error) {
          console.error('订单号格式无效:', orderNumber, error.message);
          // 如果转换失败，设置一个不可能存在的ID，确保查询返回空结果
          filters.id = BigInt(0);
        }
      }

      if (orderStatus) {
        filters.order_status = orderStatus;
      }
      if (paymentStatus) {
        filters.payment_status = paymentStatus;
      }
      if (shippingStatus) {
        filters.shipping_status = shippingStatus;
      }
      if (customerName) {
        filters.customer_name = { contains: customerName };
      }
      if (customerPhone) {
        filters.customer_phone = { contains: customerPhone };
      }
      if (orderType) {
        filters.order_type = orderType;
      }
      if (orderSource) {
        filters.order_source = orderSource;
      }
      // 添加第三方平台ID过滤条件（已弃用）
      // if (thirdPartyPlatformId) {
      //   filters.third_party_platform_id = BigInt(thirdPartyPlatformId);
      // }

      // 处理startTime和endTime参数
      if (startTime && endTime) {
        filters.created_at = {
          gte: BigInt(startTime),
          lte: BigInt(endTime)
        };
      } else if (startTime) {
        filters.created_at = {
          gte: BigInt(startTime)
        };
      } else if (endTime) {
        filters.created_at = {
          lte: BigInt(endTime)
        };
      }

      // 额外处理timeRange参数（与上面的完全独立）
      if (timeRange) {
        // 计算时间范围
        const now = Date.now(); // 当前毫秒时间戳
        const sixMonthsAgo = now - (6 * 30 * 24 * 60 * 60 * 1000); // 大约6个月前的毫秒时间戳

        // 对毫秒时间戳进行转换
        const sixMonthsAgoBigInt = BigInt(sixMonthsAgo);

        // 用AND条件作为额外的筛选条件
        // 使用OR全面查询的情况下，想要使用AND，需要使用AND运算筛选
        if (timeRange === 'recent6m') {
          // 近6个月的订单
          if (!filters.AND) filters.AND = [];
          filters.AND.push({
            created_at: {
              gte: sixMonthsAgoBigInt
            }
          });
        } else if (timeRange === 'before6m') {
          // 6个月前的订单
          if (!filters.AND) filters.AND = [];
          filters.AND.push({
            created_at: {
              lt: sixMonthsAgoBigInt
            }
          });
        }
      }

      // 构建排序条件
      const orderBy = {};
      orderBy[sortField] = sortOrder;

      // 查询订单列表（包含总数）
      const orderResult = await this.orderModel.getOrders(filters, page, pageSize, orderBy);

      // 查询汇总数据
      const summary = await this.getOrdersSummary();

      // 构建返回结果
      const result = {
        total: orderResult.total || 0,
        page: parseInt(page) || 1,
        pageSize: parseInt(pageSize) || 10,
        totalPages: Math.ceil((orderResult.total || 0) / (pageSize || 10)),
        items: orderResult.items || [],
        summary: summary || {}
      };

      // 返回结果
      return result;
    } catch (error) {
      console.error('获取订单列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单角标统计数据
   * @param {Object} params - 查询参数
   * @param {string} [params.timeRange] - 时间范围过滤（recent6m: 近6个月，before6m: 6个月前）
   * @returns {Promise<Object>} - 订单角标统计数据
   */
  async getOrderBadgeStats(params = {}) {
    try {
      // 获取Prisma客户端
      const prisma = this.prisma;

      // 基础查询条件
      const baseWhere = {
        deleted_at: null
      };

      // 如果有时间范围参数，添加时间过滤条件（作为独立条件）
      if (params.timeRange) {
        const now = Date.now(); // 当前毫秒时间戳
        const sixMonthsAgo = now - (6 * 30 * 24 * 60 * 60 * 1000); // 大约6个月前的毫秒时间戳
        const sixMonthsAgoBigInt = BigInt(sixMonthsAgo); // 转换为BigInt

        if (params.timeRange === 'recent6m') {
          // 近6个月的订单
          if (!baseWhere.AND) baseWhere.AND = [];
          baseWhere.AND.push({
            created_at: {
              gte: sixMonthsAgoBigInt // 转换为BigInt类型，与数据库存储的毫秒时间戳匹配
            }
          });
        } else if (params.timeRange === 'before6m') {
          // 6个月前的订单
          if (!baseWhere.AND) baseWhere.AND = [];
          baseWhere.AND.push({
            created_at: {
              lt: sixMonthsAgoBigInt // 转换为BigInt类型，与数据库存储的毫秒时间戳匹配
            }
          });
        }
      }

      // 统计全部未删除订单
      const totalOrders = await prisma.orders.count({
        where: baseWhere
      });

      // 待付款订单（订单状态为待付款）
      const unpaidOrders = await prisma.orders.count({
        where: {
          ...baseWhere,
          order_status: OrderStatusEnum.PENDING_PAYMENT // 0: 待付款
        }
      });

      // 待发货订单（订单状态为待发货）
      const toBeShippedOrders = await prisma.orders.count({
        where: {
          ...baseWhere,
          order_status: OrderStatusEnum.PENDING_SHIPMENT // 1: 待发货
        }
      });

      // 已发货订单（订单状态为已发货）
      const shippedOrders = await prisma.orders.count({
        where: {
          ...baseWhere,
          order_status: OrderStatusEnum.SHIPPED // 2: 已发货
        }
      });

      // 已关闭订单（订单状态为已关闭）
      const closedOrders = await prisma.orders.count({
        where: {
          ...baseWhere,
          order_status: OrderStatusEnum.CLOSED // 5: 已关闭
        }
      });

      // 交易成功订单（订单状态为已完成）
      const successOrders = await prisma.orders.count({
        where: {
          ...baseWhere,
          order_status: OrderStatusEnum.COMPLETED // 3: 已完成
        }
      });

      // 返回统计结果
      return {
        all: totalOrders,          // 全部订单
        unpaid: unpaidOrders,       // 待付款
        toBeShipped: toBeShippedOrders, // 待发货
        shipped: shippedOrders,     // 已发货
        closed: closedOrders,       // 已关闭
        success: successOrders      // 交易成功
      };
    } catch (error) {
      console.error('获取订单角标统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单汇总数据
   * @returns {Promise<Object>} - 汇总数据
   */
  async getOrdersSummary() {
    try {
      // 查询总订单数
      const totalOrders = await this.orderModel.countOrders({});

      // 查询各状态订单数
      const pendingOrders = await this.orderModel.countOrders({ orderStatus: 'pending' });
      const processingOrders = await this.orderModel.countOrders({ orderStatus: 'processing' });
      const shippedOrders = await this.orderModel.countOrders({ orderStatus: 'shipped' });
      const completedOrders = await this.orderModel.countOrders({ orderStatus: 'completed' });

      return {
        totalOrders,
        pendingOrders,
        processingOrders,
        shippedOrders,
        completedOrders
      };
    } catch (error) {
      console.error('获取订单汇总数据失败:', error);
      return {
        totalOrders: 0,
        pendingOrders: 0,
        processingOrders: 0,
        shippedOrders: 0,
        completedOrders: 0
      };
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 创建结果
   */
  async createOrder(orderData, currentUser) {
    // 使用事务确保数据一致性
    const result = await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 验证订单项
        if (!orderData.items || orderData.items.length === 0) {
          throw new Error('订单项不能为空');
        }

        // 2. 检查渠道内置状态，如果有渠道ID且渠道内置状态为1，则校验库存并执行扣减库存和增加销量
        let shouldManageStock = false;
        if (orderData.channelId) {
          console.log(`[订单创建] 检查渠道ID: ${orderData.channelId}`);
          const channel = await tx.channel.findUnique({
            where: { id: BigInt(orderData.channelId) },
            select: { id: true, name: true, is_built_in: true }
          });
          
          if (channel) {
            console.log(`[订单创建] 找到渠道信息: ID=${channel.id}, 名称=${channel.name}, 内置状态=${channel.is_built_in}`);
            if (channel.is_built_in === 1) {
              shouldManageStock = true;
              console.log(`[订单创建] 渠道为内置渠道，将执行库存管理`);
              // 校验商品库存
              await this.validateAndLockStock(orderData.items, tx);
            } else {
              console.log(`[订单创建] 渠道非内置渠道，跳过库存管理`);
            }
          } else {
            console.log(`[订单创建] 警告: 渠道ID ${orderData.channelId} 不存在`);
          }
        } else {
          console.log(`[订单创建] 未提供渠道ID，跳过渠道检查`);
        }

        // 3. 生成订单ID和订单号
        const orderId = generateSnowflakeId();
        const orderNumber = this.generateOrderNumber();

        // 4. 计算订单金额
        const { totalAmount, totalWeight, totalVolume } = this.calculateOrderTotals(orderData.items);

        // 5. 计算应付金额
        const payableAmount = totalAmount - (orderData.discountAmount || 0) - (orderData.couponAmount || 0) + (orderData.shippingFee || 0);
        
        // 获取支付方式ID
        const paymentMethodId = orderData.paymentMethodId || null;
        
        // 根据支付方式确定订单状态和支付状态
        let orderStatus = OrderStatusEnum.PENDING_PAYMENT; // 默认待付款状态
        let paymentStatus = PaymentStatusEnum.UNPAID; // 默认未支付状态
        let paidAmount = orderData.paidAmount || 0;
        let paymentTime = orderData.paymentTime;
        
        // 货到付款特殊处理
        if (paymentMethodId === PaymentMethodEnum.COD) {
          // 货到付款订单直接设置为已支付状态，可以发货
          orderStatus = OrderStatusEnum.PENDING_SHIPMENT; // 待发货状态
          paymentStatus = PaymentStatusEnum.PAID; // 已支付状态
          paidAmount = payableAmount; // 设置已支付金额等于应付金额
          paymentTime = BigInt(Date.now()); // 设置支付时间为当前时间
          
          console.log(`货到付款订单特殊处理: orderStatus=${orderStatus} (${OrderStatusEnum.getStatusName(orderStatus)}), paymentStatus=${paymentStatus} (${PaymentStatusEnum.getStatusName(paymentStatus)})`);
        }

        // 5. 准备订单数据
        const newOrderData = {
          id: orderId,
          orderNumber: orderNumber,
          customerId: orderData.customerId,
          customerName: orderData.customerName,
          customerPhone: orderData.customerPhone,
          orderStatus: orderStatus,
          // 直接使用整数类型的orderType
          orderType: orderData.orderType !== undefined ? parseInt(orderData.orderType) : OrderTypeEnum.MALL,
          // 直接使用整数类型的orderSource
          orderSource: orderData.orderSource !== undefined ? parseInt(orderData.orderSource) : OrderSourceEnum.SYSTEM,
          totalAmount: totalAmount,
          discountAmount: orderData.discountAmount || 0,
          couponAmount: orderData.couponAmount || 0,
          shippingFee: orderData.shippingFee || 0,
          payableAmount: payableAmount,
          paidAmount: paidAmount,
          paymentStatus: paymentStatus,
          paymentMethod: orderData.paymentMethod,
          payment_method_id: paymentMethodId,
          transactionId: orderData.transactionId,
          paymentTime: paymentTime,
          shippingStatus: ShippingStatusEnum.UNSHIPPED,
          totalWeight: totalWeight,
          totalVolume: totalVolume,
          remark: orderData.remark,
          thirdPartyPlatformId: orderData.thirdPartyPlatformId,
          channelId: orderData.channelId ? BigInt(orderData.channelId) : null, // 添加渠道ID
          createdBy: currentUser?.id?.toString() || '0'
        };

        console.log(`[订单创建] 准备订单数据完成, 订单号: ${orderNumber}, 渠道ID: ${newOrderData.channelId}, 库存管理: ${shouldManageStock ? '是' : '否'}`);

        // 6. 创建订单
        const order = await this.orderModel.createOrder(newOrderData, tx);

        // 7. 创建订单项
        const orderItems = orderData.items.map(item => ({
          orderId: orderId,
          goodsSpuId: item.goodsSpuId,
          goodsSkuId: item.goodsSkuId,
          spuCodeSnapshot: item.spuCodeSnapshot,
          spuNameSnapshot: item.spuNameSnapshot,
          productName: item.productName,
          skuCode: item.skuCode,
          skuSpecifications: item.skuSpecifications,
          productImage: item.productImage,
          unitPrice: item.unitPrice,
          marketPriceSnapshot: item.marketPriceSnapshot,
          costPriceSnapshot: item.costPriceSnapshot,
          weightSnapshot: item.weightSnapshot,
          volumeSnapshot: item.volumeSnapshot,
          thirdPartySpuId: item.thirdPartySpuId,
          thirdPartySkuId: item.thirdPartySkuId,
          thirdPartyProductCode: item.thirdPartyProductCode,
          thirdPartyItemSnapshot: item.thirdPartyItemSnapshot,
          quantity: item.quantity,
          totalPrice: item.unitPrice * item.quantity,
          itemPaidAmount: item.itemPaidAmount || 0
        }));
        await this.orderItemModel.createOrderItems(orderItems, tx);

        // 8. 创建订单配送信息（如果有）
        if (orderData.shipping) {
          try {
            // 根据省市区ID获取区域名称
            let regionPathName = orderData.shipping.regionPathName || '';

            // 如果没有提供regionPathName但有区域ID，则尝试查询区域名称
            if (!regionPathName && (orderData.shipping.regionProvinceId || orderData.shipping.regionCityId || orderData.shipping.regionDistrictId)) {
              const regionCodes = [];

              if (orderData.shipping.regionProvinceId) {
                regionCodes.push(orderData.shipping.regionProvinceId);
              }

              if (orderData.shipping.regionCityId) {
                regionCodes.push(orderData.shipping.regionCityId);
              }

              if (orderData.shipping.regionDistrictId) {
                regionCodes.push(orderData.shipping.regionDistrictId);
              }

              if (regionCodes.length > 0) {
                // 获取区域名称
                const regionNames = await RegionModel.getRegionNamesByCodes(regionCodes);
                if (regionNames) {
                  regionPathName = regionNames;
                }
              }
            }

            // 格式化完整地址：省市区名称 + 用户输入的详细地址
            let fullStreetAddress = orderData.shipping.streetAddress || '';

            if (regionPathName && fullStreetAddress) {
              // 将省市区名称与详细地址拼接，后端查询出的regionPathName格式化为短横线分隔
              const regionNameFormatted = regionPathName.replace(/[\/,]/g, '-'); // 将数据库查询出的分隔符替换为短横线
              fullStreetAddress = `${regionNameFormatted} ${fullStreetAddress}`;
            } else if (regionPathName) {
              // 如果只有省市区名称，没有详细地址
              fullStreetAddress = regionPathName.replace(/[\/,]/g, '-');
            }

            await this.orderShippingInfoModel.createShippingInfo({
              orderId: orderId,
              recipientName: orderData.shipping.recipientName,
              recipientPhone: orderData.shipping.recipientPhone,
              regionProvinceId: orderData.shipping.regionProvinceId || null,
              regionCityId: orderData.shipping.regionCityId || null,
              regionDistrictId: orderData.shipping.regionDistrictId || null,
              regionPathName: regionPathName ? regionPathName.replace(/[\/,]/g, '-') : '', // 格式化为短横线分隔
              streetAddress: fullStreetAddress, // 使用格式化后的完整地址
              postalCode: orderData.shipping.postalCode || '',
              shippingCompanyCode: orderData.shipping.shippingCompanyCode || '',
              shippingCompanyName: orderData.shipping.shippingCompanyName || '',
              trackingNumber: orderData.shipping.trackingNumber || ''
            }, tx);
          } catch (error) {
            console.error('创建订单配送信息失败：', error);
            throw error;
          }
        }

        // 9. 创建订单销售人员信息（如果有）
        if (orderData.salespeople) {
          await this.orderSalespeopleModel.createSalespeople({
            orderId: orderId,
            salespersonId: orderData.salespeople.salespersonId
            // 移除不需要的字段
            // salespersonName: orderData.salespeople.salespersonName,
            // salespersonPhone: orderData.salespeople.salespersonPhone,
            // commissionRate: orderData.salespeople.commissionRate,
            // commissionAmount: orderData.salespeople.commissionAmount
          }, tx);
        }

        // 10. 创建订单日志
        // 准备日志数据，处理可能缺失的属性
        try {

          const logData = {
            orderId: orderId,
            logType: 'create',
            logContent: '订单创建成功',
            operatorId: currentUser?.id || '0',
            operatorName: currentUser?.username || currentUser?.name || '系统'
          };

          await this.orderLogModel.createLog(logData, tx);
        } catch (error) {
          console.error('创建订单日志失败：', error);
          // 日志创建失败不应影响订单创建，所以不抛出异常
        }

        // 11. 创建发票申请记录（如果有发票信息）
        console.log(`[订单创建] 检查发票信息:`, {
          hasInvoice: !!orderData.invoice,
          invoiceData: orderData.invoice,
          invoiceType: orderData.invoice?.invoiceType,
          invoiceTypeEnum: InvoiceTypeEnum.NONE,
          condition: orderData.invoice && orderData.invoice.invoiceType !== InvoiceTypeEnum.NONE
        });

        if (orderData.invoice && orderData.invoice.invoiceType !== InvoiceTypeEnum.NONE) {
          try {
            console.log(`[订单创建] 开始创建发票申请记录`);
            await this.createInvoiceRecord(orderId, orderData.invoice, currentUser, tx);
            console.log(`[订单创建] 发票申请记录创建成功`);
          } catch (error) {
            console.error('创建发票申请记录失败：', error);
            throw error; // 发票创建失败应该影响订单创建
          }
        } else {
          console.log(`[订单创建] 跳过发票申请记录创建`);
        }

        // 12. 如果是货到付款，记录支付日志
        if (paymentMethodId === PaymentMethodEnum.COD && paymentStatus === PaymentStatusEnum.PAID) {
          try {
            const paymentLogData = {
              orderId: orderId,
              logType: 'payment',
              logContent: `货到付款订单自动标记为已支付，支付方式：${PaymentMethodEnum.getName(PaymentMethodEnum.COD)}，金额：￥${payableAmount}`,
              operatorId: currentUser?.id || '0',
              operatorName: currentUser?.username || currentUser?.name || '系统管理员',
              remark: '货到付款订单自动处理'
            };

            await this.orderLogModel.createLog(paymentLogData, tx);
          } catch (error) {
            console.error('创建货到付款支付日志失败：', error);
            // 日志创建失败不应影响订单创建，所以不抛出异常
          }
        }

        // 13. 如果渠道内置状态为1，扣减库存并增加销量
        if (shouldManageStock) {
          console.log(`[订单创建] 开始执行库存扣减和销量增加，商品数量: ${orderData.items.length}`);
          await this.deductStockAndIncreaseSales(orderData.items, tx);
          console.log(`[订单创建] 库存扣减和销量增加完成`);
        } else {
          console.log(`[订单创建] 跳过库存扣减和销量增加`);
        }

        // 14. 处理客户关联逻辑（在事务外执行，避免跨schema事务问题）
        // 注意：这里先返回订单创建结果，客户关联在事务外异步处理
        const orderResult = {
          success: true,
          data: {
            id: order.id,
            orderNumber: order.orderNumber
          }
        };

        // 设置客户关联处理标记，在事务外执行
        // 优先使用shipping中的收货人信息，如果没有则使用订单的客户信息
        const recipientName = orderData.shipping?.recipientName || orderData.customerName;
        const recipientPhone = orderData.shipping?.recipientPhone || orderData.customerPhone;

        orderResult._needCustomerAssociation = {
          orderId: orderId,
          orderNumber: orderNumber,
          recipientName: recipientName,
          recipientPhone: recipientPhone,
          shippingAddress: orderData.shipping ?
            `${orderData.shipping.regionPathName || ''}${orderData.shipping.streetAddress || ''}` : '',
          userId: currentUser?.id || 'system'
        };

        return orderResult;
      } catch (error) {
        console.error('创建订单失败', error);
        throw error;
      }
    }, {
      maxWait: 5000, // 最长等待时间
      timeout: 10000 // 事务超时时间
    });

    // 处理客户关联逻辑（在事务外执行）
    if (result._needCustomerAssociation) {
      try {
        const associationInfo = result._needCustomerAssociation;
        if (associationInfo.recipientName && associationInfo.recipientPhone) {
          console.log(`[订单创建] 开始处理客户关联，订单号: ${associationInfo.orderNumber}`);

          const associationResult = await this.customerOrderAssociationService.processOrderCustomerAssociation({
            orderId: associationInfo.orderId,
            orderNumber: associationInfo.orderNumber,
            recipientName: associationInfo.recipientName,
            recipientPhone: associationInfo.recipientPhone,
            shippingAddress: associationInfo.shippingAddress
          }, associationInfo.userId);

          console.log(`[订单创建] 客户关联处理完成:`, associationResult);

          // 将客户关联信息添加到返回结果中
          result.data.customerAssociation = {
            customerId: associationResult.customerId,
            customerName: associationResult.customerName,
            isTemporary: associationResult.isTemporary
          };

          // 同步订单到CRM
          try {
            console.log(`[订单创建] 开始同步订单到CRM，订单ID: ${associationInfo.orderId}`);
            const crmSyncResult = await this.crmOrderManagementService.syncOrderFromMaster(associationInfo.orderId);
            console.log(`[订单创建] CRM同步成功:`, crmSyncResult);

            // 将CRM同步信息添加到返回结果中
            result.data.crmSync = {
              success: true,
              crmOrderId: crmSyncResult.crmOrderId
            };
          } catch (crmError) {
            console.error(`[订单创建] CRM同步失败:`, crmError);
            result.data.crmSync = {
              success: false,
              error: crmError.message
            };
          }
        } else {
          console.log(`[订单创建] 跳过客户关联，缺少收货人信息`);
        }
      } catch (error) {
        console.error(`[订单创建] 客户关联处理失败:`, error);
        // 客户关联失败不影响订单创建成功的返回
      }

      // 清理临时标记
      delete result._needCustomerAssociation;
    }

    return result;
  }

  /**
   * 创建第三方订单
   * @param {Object} orderData - 订单数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyOrder(orderData, currentUser) {
    // 验证渠道ID是否存在
    if (!orderData.channelId) {
      throw new Error('渠道ID是必填项');
    }
    
    // 使用事务确保数据一致性
    const result = await this.prisma.$transaction(async (tx) => {
      try {
        // 验证渠道ID是否存在于数据库中
        // 将渠道ID转换为BigInt类型
        const channelIdBigInt = BigInt(orderData.channelId);

        const channelExists = await tx.channel.findFirst({
          where: {
            id: channelIdBigInt,
            deleted_at: null
          }
        });

        if (!channelExists) {
          throw new Error(`渠道ID ${orderData.channelId} 不存在，请检查渠道ID是否正确`);
        }

        // 验证平台ID和店铺ID（如果提供了）
        let platformIdBigInt = null;
        let storeIdBigInt = null;



        if (orderData.platformId) {
          platformIdBigInt = BigInt(orderData.platformId);

          // 使用原生SQL验证平台是否存在
          try {
            const platformResult = await tx.$queryRaw`
              SELECT id FROM base.platform
              WHERE id = ${platformIdBigInt} AND deleted_at IS NULL
              LIMIT 1
            `;

            if (!platformResult || platformResult.length === 0) {
              throw new Error(`平台ID ${orderData.platformId} 不存在，请检查平台ID是否正确`);
            }
          } catch (error) {
            if (error.message.includes('平台ID')) {
              throw error;
            }
            // 如果是SQL查询错误，可能是表不存在，跳过验证
          }
        }

        if (orderData.storeId) {
          storeIdBigInt = BigInt(orderData.storeId);

          // 使用原生SQL验证店铺是否存在
          try {
            const storeResult = await tx.$queryRaw`
              SELECT id, platform_id FROM base.store
              WHERE id = ${storeIdBigInt} AND deleted_at IS NULL
              LIMIT 1
            `;

            if (!storeResult || storeResult.length === 0) {
              throw new Error(`店铺ID ${orderData.storeId} 不存在，请检查店铺ID是否正确`);
            }

            // 如果同时提供了平台ID和店铺ID，验证店铺是否属于该平台
            if (platformIdBigInt && storeResult[0].platform_id !== platformIdBigInt) {
              throw new Error(`店铺ID ${orderData.storeId} 不属于平台ID ${orderData.platformId}`);
            }
          } catch (error) {
            if (error.message.includes('店铺ID')) {
              throw error;
            }
            // 如果是SQL查询错误，可能是表不存在，跳过验证
          }
        }
        
        // 1. 验证订单项
        if (!orderData.items || orderData.items.length === 0) {
          throw new Error('订单项不能为空');
        }

        // 第三方SKU ID和SPU ID允许为空或不传
        // 注意：已移除必填验证

        // 2. 生成订单ID和订单号
        const orderId = generateSnowflakeId();
        const orderNumber = this.generateOrderNumber();

        // 3. 计算订单金额
        const { totalAmount, totalWeight, totalVolume } = this.calculateOrderTotals(orderData.items);

        // 4. 准备收货信息（如果提供了地址字段）
        let shippingInfo = null;
        if (orderData.address) {
          shippingInfo = {
            address: orderData.address,
            postalCode: orderData.postalCode
          };
        }

        // 5. 准备订单数据
        const newOrderData = {
          id: orderId,
          orderNumber: orderNumber,
          customerId: orderData.customerId,
          customerName: orderData.customerName,
          customerPhone: orderData.customerPhone,
          orderStatus: OrderStatusEnum.PENDING_SHIPMENT,
          // 直接使用整数类型的orderType
          orderType: orderData.orderType !== undefined ? parseInt(orderData.orderType) : OrderTypeEnum.MALL,
          // 第三方订单来源固定为OrderSourceEnum.SYSTEM
          orderSource: OrderSourceEnum.SYSTEM,
          totalAmount: totalAmount,
          discountAmount: orderData.discountAmount || 0,
          couponAmount: orderData.couponAmount || 0,
          shippingFee: orderData.shippingFee || 0,
          payableAmount: totalAmount - (orderData.discountAmount || 0) - (orderData.couponAmount || 0) + (orderData.shippingFee || 0),
          paidAmount: orderData.paidAmount || 0,
          paymentStatus: PaymentStatusEnum.UNPAID,
          // 第三方订单固定使用账期支付7
          paymentMethod: PaymentMethodEnum.getName(PaymentMethodEnum.CREDIT_PAYMENT),
          paymentMethodId: PaymentMethodEnum.CREDIT_PAYMENT,
          transactionId: orderData.transactionId,
          paymentTime: orderData.paymentTime,
          shippingStatus: ShippingStatusEnum.UNSHIPPED,
          totalWeight: totalWeight,
          totalVolume: totalVolume,
          remark: orderData.remark,
          // 添加第三方订单号（字符串类型）
          thirdPartyOrderSn: orderData.thirdPartyOrderSn,
          // 添加渠道ID（转换为BigInt类型）
          channelId: BigInt(orderData.channelId),
          // 添加平台ID（如果提供了）
          platformId: platformIdBigInt,
          // 添加店铺ID（如果提供了）
          storeId: storeIdBigInt,
          createdBy: currentUser?.id?.toString() || '0',
          // 添加收货信息（如果提供了）
          shippingInfo: shippingInfo
        };



        // 5. 检查第三方订单号是否已存在
        const existingOrder = await tx.orders.findFirst({
          where: {
            third_party_order_sn: orderData.thirdPartyOrderSn,
            deleted_at: null
          },
          select: {
            id: true,
            third_party_order_sn: true
          }
        });

        if (existingOrder) {
          throw new Error(`第三方订单号 ${orderData.thirdPartyOrderSn} 已存在，订单ID: ${existingOrder.id}`);
        }

        // 6. 创建订单
        const order = await this.orderModel.createOrder(newOrderData, tx);

        // 6.5. 处理第三方录单商品（查询现有商品或创建新商品）
        console.log('[OrderService] ========== 开始处理第三方录单商品 ==========');
        console.log('[OrderService] 商品处理参数:', {
          订单号: orderNumber,
          第三方订单号: orderData.thirdPartyOrderSn,
          商品数量: orderData.items.length,
          渠道ID: orderData.channelId,
          平台ID: orderData.platformId || '未提供',
          店铺ID: orderData.storeId || '未提供'
        });

        const goodsProcessStartTime = Date.now();
        const processedItems = await this.thirdPartyOrderGoodsService.processOrderItems(
          orderData.items,
          orderData.channelId,
          orderData.platformId,
          orderData.storeId,
          tx
        );
        const goodsProcessEndTime = Date.now();

        console.log('[OrderService] ========== 第三方录单商品处理完成 ==========');
        console.log('[OrderService] 商品处理结果:', {
          原始商品数量: orderData.items.length,
          处理后商品数量: processedItems.length,
          处理耗时: `${goodsProcessEndTime - goodsProcessStartTime}ms`,
          所有商品都有系统ID: processedItems.every(item => item.goodsSkuId && item.goodsSpuId)
        });

        // 7. 创建订单项（使用处理后的商品数据）
        console.log('[OrderService] ========== 开始创建订单项 ==========');

        const orderItems = processedItems.map((item, index) => {
          // 准备订单项数据，包含处理后的商品ID
          const orderItem = {
            orderId: orderId,                            // 订单ID
            goodsSpuId: item.goodsSpuId,                // 使用处理后的SPU ID（重要）
            goodsSkuId: item.goodsSkuId,                // 使用处理后的SKU ID（重要）
            productName: item.productName || '第三方商品', // 商品名称
            unitPrice: item.unitPrice || 0,              // 单价
            quantity: item.quantity || 1,                // 数量
            totalPrice: (item.unitPrice || 0) * (item.quantity || 1), // 总价
            itemPaidAmount: item.itemPaidAmount || 0     // 已付金额
          };

          // 添加可选字段，只有在存在时才添加
          if (item.spuCodeSnapshot) orderItem.spuCodeSnapshot = item.spuCodeSnapshot;
          if (item.spuNameSnapshot) orderItem.spuNameSnapshot = item.spuNameSnapshot;
          if (item.skuCode) orderItem.skuCode = item.skuCode;
          if (item.skuSpecifications) orderItem.skuSpecifications = item.skuSpecifications;
          if (item.productImage) orderItem.productImage = item.productImage;
          if (item.marketPriceSnapshot) orderItem.marketPriceSnapshot = item.marketPriceSnapshot;
          if (item.costPriceSnapshot) orderItem.costPriceSnapshot = item.costPriceSnapshot;
          if (item.weightSnapshot) orderItem.weightSnapshot = item.weightSnapshot;
          if (item.volumeSnapshot) orderItem.volumeSnapshot = item.volumeSnapshot;
          if (item.thirdPartySpuId) orderItem.thirdPartySpuId = item.thirdPartySpuId;
          if (item.thirdPartySkuId) orderItem.thirdPartySkuId = item.thirdPartySkuId;
          if (item.thirdPartyProductCode) orderItem.thirdPartyProductCode = item.thirdPartyProductCode;
          if (item.thirdPartyItemSnapshot) orderItem.thirdPartyItemSnapshot = item.thirdPartyItemSnapshot;

          console.log(`[OrderService] 订单项${index + 1}数据:`, {
            商品名称: orderItem.productName,
            系统SPU_ID: orderItem.goodsSpuId?.toString(),
            系统SKU_ID: orderItem.goodsSkuId?.toString(),
            单价: orderItem.unitPrice,
            数量: orderItem.quantity,
            总价: orderItem.totalPrice,
            第三方SKU_ID: orderItem.thirdPartySkuId || '无',
            第三方SPU_ID: orderItem.thirdPartySpuId || '无'
          });

          return orderItem;
        });

        console.log('[OrderService] 开始保存订单项到数据库...');
        await this.orderItemModel.createOrderItems(orderItems, tx);
        console.log('[OrderService] ✅ 订单项创建完成，共创建:', orderItems.length, '个订单项');

        // 8. 创建订单配送信息（如果有）
        if (orderData.shipping) {
          try {
            // 根据省市区ID获取区域名称
            let regionPathName = orderData.shipping.regionPathName || '';

            // 如果没有提供regionPathName但有区域ID，则尝试查询区域名称
            if (!regionPathName && (orderData.shipping.regionProvinceId || orderData.shipping.regionCityId || orderData.shipping.regionDistrictId)) {
              const regionCodes = [];

              if (orderData.shipping.regionProvinceId) {
                regionCodes.push(orderData.shipping.regionProvinceId);
              }

              if (orderData.shipping.regionCityId) {
                regionCodes.push(orderData.shipping.regionCityId);
              }

              if (orderData.shipping.regionDistrictId) {
                regionCodes.push(orderData.shipping.regionDistrictId);
              }

              if (regionCodes.length > 0) {
                // 获取区域名称
                const regionNames = await RegionModel.getRegionNamesByCodes(regionCodes);
                if (regionNames) {
                  regionPathName = regionNames;
                }
              }
            }

            // 格式化完整地址：省市区名称 + 用户输入的详细地址
            let fullStreetAddress = orderData.shipping.streetAddress || '';

            if (regionPathName && fullStreetAddress) {
              // 将省市区名称与详细地址拼接，保持原有格式
              fullStreetAddress = `${regionPathName} ${fullStreetAddress}`;
            } else if (regionPathName) {
              // 如果只有省市区名称，没有详细地址
              fullStreetAddress = regionPathName;
            }

            await this.orderShippingInfoModel.createShippingInfo({
              orderId: orderId,
              recipientName: orderData.shipping.recipientName,
              recipientPhone: orderData.shipping.recipientPhone,
              regionProvinceId: orderData.shipping.regionProvinceId || 0,
              regionCityId: orderData.shipping.regionCityId || 0,
              regionDistrictId: orderData.shipping.regionDistrictId || 0,
              regionPathName: regionPathName, // 保持原有格式
              streetAddress: fullStreetAddress, // 使用格式化后的完整地址
              postalCode: orderData.shipping.postalCode || '',
              shippingCompanyCode: orderData.shipping.shippingCompanyCode || '',
              shippingCompanyName: orderData.shipping.shippingCompanyName || '',
              trackingNumber: orderData.shipping.trackingNumber || ''
            }, tx);
          } catch (error) {
            console.error('创建订单配送信息失败：', error);
            throw error;
          }
        }

        // 9. 创建订单销售人员信息（如果有）
        if (orderData.salespeople) {
          await this.orderSalespeopleModel.createSalespeople({
            orderId: orderId,
            salespersonId: orderData.salespeople.salespersonId
          }, tx);
        }

        // 10. 创建订单日志
        try {
          const logData = {
            orderId: orderId,
            logType: 'create',
            logContent: '第三方订单创建成功',
            operatorId: currentUser?.id || '0',
            operatorName: currentUser?.username || currentUser?.name || '第三方系统'
          };

          await this.orderLogModel.createLog(logData, tx);
        } catch (error) {
          console.error('创建订单日志失败：', error);
          // 日志创建失败不应影响订单创建，所以不抛出异常
        }



        // 准备返回结果，包含客户关联处理标记
        const orderResult = {
          success: true,
          data: {
            id: order.id,
            orderNumber: order.orderNumber,
            thirdPartyOrderSn: order.thirdPartyOrderSn,
            channelId: order.channelId,
            platformId: order.platformId,
            storeId: order.storeId
          }
        };

        // 设置客户关联处理标记，在事务外执行
        // 优先使用shipping中的收货人信息，如果没有则使用订单的客户信息
        const recipientName = orderData.shipping?.recipientName || orderData.customerName;
        const recipientPhone = orderData.shipping?.recipientPhone || orderData.customerPhone;

        orderResult._needCustomerAssociation = {
          orderId: orderId,
          orderNumber: orderNumber,
          recipientName: recipientName,
          recipientPhone: recipientPhone,
          shippingAddress: orderData.shipping ?
            `${orderData.shipping.regionPathName || ''}${orderData.shipping.streetAddress || ''}` : '',
          userId: currentUser?.id || 'system'
        };

        return orderResult;
      } catch (error) {
        console.error('创建第三方订单失败', error);
        throw error;
      }
    }, {
      maxWait: 5000, // 最长等待时间
      timeout: 10000 // 事务超时时间
    });

    // 处理客户关联逻辑（在事务外执行）
    if (result._needCustomerAssociation) {
      try {
        const associationInfo = result._needCustomerAssociation;
        if (associationInfo.recipientName && associationInfo.recipientPhone) {
          console.log(`[第三方订单创建] 开始处理客户关联，订单号: ${associationInfo.orderNumber}`);

          const associationResult = await this.customerOrderAssociationService.processOrderCustomerAssociation({
            orderId: associationInfo.orderId,
            orderNumber: associationInfo.orderNumber,
            recipientName: associationInfo.recipientName,
            recipientPhone: associationInfo.recipientPhone,
            shippingAddress: associationInfo.shippingAddress
          }, associationInfo.userId);

          console.log(`[第三方订单创建] 客户关联处理完成:`, associationResult);

          // 将客户关联信息添加到返回结果中
          result.data.customerAssociation = {
            customerId: associationResult.customerId,
            customerName: associationResult.customerName,
            isTemporary: associationResult.isTemporary
          };

          // 同步订单到CRM
          try {
            console.log(`[第三方订单创建] 开始同步订单到CRM，订单ID: ${associationInfo.orderId}`);
            const crmSyncResult = await this.crmOrderManagementService.syncOrderFromMaster(associationInfo.orderId);
            console.log(`[第三方订单创建] CRM同步成功:`, crmSyncResult);

            result.data.crmSync = {
              success: true,
              crmOrderId: crmSyncResult.crmOrderId
            };
          } catch (crmError) {
            console.error(`[第三方订单创建] CRM同步失败:`, crmError);
            result.data.crmSync = {
              success: false,
              error: crmError.message
            };
          }
        } else {
          console.log(`[第三方订单创建] 跳过客户关联，缺少收货人信息`);
        }
      } catch (error) {
        console.error(`[第三方订单创建] 客户关联处理失败:`, error);
        // 客户关联失败不影响订单创建成功的返回
      }

      // 清理临时标记
      delete result._needCustomerAssociation;
    }

    return result;
  }

  /**
   * 验证并锁定库存
   * @param {Array} items - 订单项数组
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async validateAndLockStock(items, tx) {
    console.log(`[库存校验] 开始校验 ${items.length} 个商品的库存`);
    
    for (const item of items) {
      console.log(`[库存校验] 检查商品SKU: ${item.goodsSkuId}, 需要数量: ${item.quantity}`);
      
      // 查询商品SKU信息（只能使用唯一字段进行查询）
      const sku = await tx.goodsSku.findUnique({
        where: {
          id: BigInt(item.goodsSkuId)
        },
        include: {
          goods_spu: true
        }
      });

      if (!sku || sku.deleted_at !== null) {
        console.log(`[库存校验] 错误: 商品SKU(ID: ${item.goodsSkuId})不存在或已删除`);
        throw new Error(`商品SKU(ID: ${item.goodsSkuId})不存在或已删除`);
      }

      console.log(`[库存校验] 商品信息: SKU编码=${sku.sku_code}, 商品名称=${sku.goods_spu.name}, 当前库存=${sku.stock}`);

      // 检查库存是否充足
      if (sku.stock < item.quantity) {
        console.log(`[库存校验] 错误: 库存不足 - 当前库存: ${sku.stock}, 需要数量: ${item.quantity}`);
        throw new Error(`商品"${sku.goods_spu.name}"(SKU编码: ${sku.sku_code})库存不足，当前库存: ${sku.stock}, 需要: ${item.quantity}`);
      }

      console.log(`[库存校验] 商品 ${sku.sku_code} 库存充足`);

      // 设置商品快照信息
      if (!item.spuNameSnapshot) {
        item.spuNameSnapshot = sku.goods_spu.name;
      }
      if (!item.spuCodeSnapshot) {
        item.spuCodeSnapshot = sku.goods_spu.spu_code;
      }
      if (!item.skuCode) {
        item.skuCode = sku.sku_code;
      }
    }
    
    console.log(`[库存校验] 所有商品库存校验通过`);
  }

  /**
   * 扣减库存
   * @param {Array} items - 订单项数组
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async deductStock(items, tx) {
    for (const item of items) {
      await tx.goodsSku.update({
        where: {
          id: BigInt(item.goodsSkuId)
        },
        data: {
          stock: {
            decrement: item.quantity
          },
          updated_at: BigInt(Date.now())
        }
      });
    }
  }

  /**
   * 扣减库存并增加销量（仅限渠道内置状态为1的情况）
   * @param {Array} items - 订单项数组
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async deductStockAndIncreaseSales(items, tx) {
    for (const item of items) {
      console.log(`[库存管理] 处理商品SKU: ${item.goodsSkuId}, 数量: ${item.quantity}`);
      
              // 先查询更新前的数据
        const beforeUpdate = await tx.goodsSku.findUnique({
          where: { id: BigInt(item.goodsSkuId) },
          select: {
            id: true,
            sku_code: true,
            stock: true,
            sales_volume: true,
            goods_spu_id: true
          }
        });
        
        console.log(`[库存管理] 更新前 SKU ${beforeUpdate.sku_code}: 库存=${beforeUpdate.stock}, 销量=${beforeUpdate.sales_volume || 0}`);
      
              const result = await tx.goodsSku.update({
          where: {
            id: BigInt(item.goodsSkuId)
          },
          data: {
            stock: {
              decrement: item.quantity
            },
            sales_volume: {
              increment: item.quantity
            },
            updated_at: BigInt(Date.now())
          },
          select: {
            id: true,
            sku_code: true,
            stock: true,
            sales_volume: true,
            goods_spu_id: true
          }
        });
        
        // 验证更新结果
        if (result.sales_volume === beforeUpdate.sales_volume) {
          console.log(`[库存管理] 警告: SKU ${result.sku_code} 销量未发生变化，可能存在问题`);
          
          // 尝试直接设置销量
          const fixedResult = await tx.goodsSku.update({
            where: { id: BigInt(item.goodsSkuId) },
            data: { 
              sales_volume: (beforeUpdate.sales_volume || 0) + item.quantity,
              updated_at: BigInt(Date.now())
            },
            select: { sku_code: true, sales_volume: true }
          });
          
          console.log(`[库存管理] 修复后 SKU ${fixedResult.sku_code} 销量: ${fixedResult.sales_volume}`);
        }
      
      console.log(`[库存管理] 更新后 SKU ${result.sku_code}: 库存=${result.stock}, 销量=${result.sales_volume}`);
      console.log(`[库存管理] 变化情况: 库存减少${beforeUpdate.stock - result.stock}, 销量增加${result.sales_volume - beforeUpdate.sales_volume}`);
    }
    
    // 同步更新SPU的总库存和总销量
    await this.updateSpuTotalStatsAfterStockChange(items, tx);
  }

  /**
   * 恢复库存并减少销量（仅限渠道内置状态为1的情况，订单取消时使用）
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async restoreStockAndDecreaseSales(orderId, tx) {
    console.log(`[库存恢复] 开始处理订单 ${orderId} 的库存恢复和销量减少`);
    
    // 获取订单项信息
    const orderItems = await tx.order_items.findMany({
      where: { order_id: BigInt(orderId) },
      select: {
        goods_sku_id: true,
        quantity: true,
        product_name: true
      }
    });

    if (!orderItems || orderItems.length === 0) {
      console.log(`[库存恢复] 订单 ${orderId} 没有订单项，跳过库存恢复`);
      return;
    }

    console.log(`[库存恢复] 找到 ${orderItems.length} 个订单项需要处理`);

    for (const item of orderItems) {
      console.log(`[库存恢复] 处理商品SKU: ${item.goods_sku_id}, 恢复数量: ${item.quantity}`);
      
      const result = await tx.goodsSku.update({
        where: {
          id: BigInt(item.goods_sku_id)
        },
        data: {
          stock: {
            increment: item.quantity  // 恢复库存（增加）
          },
          sales_volume: {
            decrement: item.quantity  // 减少销量
          },
          updated_at: BigInt(Date.now())
        },
        select: {
          id: true,
          sku_code: true,
          stock: true,
          sales_volume: true
        }
      });
      
      console.log(`[库存恢复] SKU ${result.sku_code} 恢复完成: 当前库存=${result.stock}, 当前销量=${result.sales_volume}`);
    }
    
    console.log(`[库存恢复] 订单 ${orderId} 的库存恢复和销量减少全部完成`);
    
    // 将订单项转换为更新SPU所需的格式
    const itemsForSpuUpdate = orderItems.map(item => ({
      goodsSkuId: item.goods_sku_id,
      quantity: item.quantity
    }));
    
    // 同步更新SPU的总库存和总销量
    await this.updateSpuTotalStatsAfterStockChange(itemsForSpuUpdate, tx);
  }

  /**
   * 计算订单总金额、总重量和总体积
   * @param {Array} items - 订单项数组
   * @returns {Object} - 计算结果
   */
  calculateOrderTotals(items) {
    let totalAmount = 0;
    let totalWeight = 0;
    let totalVolume = 0;

    for (const item of items) {
      // 计算总金额
      const itemTotal = item.unitPrice * item.quantity;
      totalAmount += itemTotal;

      // 计算总重量
      if (item.weightSnapshot) {
        totalWeight += item.weightSnapshot * item.quantity;
      }

      // 计算总体积
      if (item.volumeSnapshot) {
        totalVolume += item.volumeSnapshot * item.quantity;
      }
    }

    return {
      totalAmount,
      totalWeight,
      totalVolume
    };
  }

  /**
   * 获取订单列表
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} - 订单列表和总数
   */
  async getOrders(filters, page, pageSize) {
    return await this.orderModel.getOrders(filters, page, pageSize);
  }

  /**
   * 获取订单详情
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} - 订单详情
   */
  async getOrderDetail(orderId) {
    // 获取订单基本信息
    const order = await this.orderModel.getOrderById(orderId);
    if (!order) {
      return null;
    }



    // 获取订单项
    const orderItems = await this.orderItemModel.getOrderItemsByOrderId(orderId);

    // 获取订单配送信息
    const shippingInfo = await this.orderShippingInfoModel.getShippingInfoByOrderId(orderId);

    // 获取订单销售人员信息
    const salespeople = await this.orderSalespeopleModel.getSalespeopleByOrderId(orderId);

    // 获取订单日志
    const logs = await this.orderLogModel.getLogsByOrderId(orderId);

    // 获取发票申请信息
    let invoiceApplication = null;
    let invoices = [];
    try {
      // 获取发票申请记录
      invoiceApplication = await this.invoiceApplicationModel.getApplicationByOrderId(orderId);
      if (invoiceApplication) {
        console.log(`获取到订单 ${orderId} 的发票申请信息:`, {
          id: invoiceApplication.id,
          applicationType: invoiceApplication.applicationType,
          applicationAmount: invoiceApplication.applicationAmount,
          applicationStatus: invoiceApplication.applicationStatus
        });

        // 获取该申请对应的发票记录
        invoices = await this.invoiceModel.getInvoicesByApplicationId(invoiceApplication.id);
        console.log(`获取到 ${invoices.length} 张发票记录`);
      }
    } catch (error) {
      console.error('获取订单发票信息失败:', error);
      // 发票信息获取失败不应影响订单详情查询
      invoiceApplication = null;
      invoices = [];
    }

    // 获取订单包裹信息
    let packages = [];
    try {
              packages = await OrderPackageModel.getPackagesByOrderId(orderId);

      // 将包裹数据转换为驼峰命名格式
      if (packages && packages.length > 0) {
        packages = packages.map(pkg => ({
          id: String(pkg.id),
          orderId: String(pkg.order_id),
          packageSn: pkg.package_sn,
          shippingCompanyCode: pkg.shipping_company_code,
          shippingCompanyName: pkg.shipping_company_name,
          trackingNumber: pkg.tracking_number,
          shippingMethod: pkg.shipping_method,
          shippingStatus: pkg.shipping_status,
          recipientName: pkg.recipient_name,
          recipientPhone: pkg.recipient_phone,
          regionPathName: pkg.region_path_name,
          streetAddress: pkg.street_address,
          postalCode: pkg.postal_code,
          imagesUrl: pkg.images_url,
          shippingOrigin: pkg.shipping_origin,
          remark: pkg.remark,
          createdAt: String(pkg.created_at),
          updatedAt: String(pkg.updated_at),
          shippedAt: pkg.shipped_at ? String(pkg.shipped_at) : null,

          // 包裹项信息
          items: pkg.order_package_items ? pkg.order_package_items.map(item => ({
            id: String(item.id),
            packageId: String(item.package_id),
            orderItemId: String(item.order_item_id),
            quantity: item.quantity,
            createdAt: String(item.created_at),
            updatedAt: String(item.updated_at),
            // 包含订单项简要信息
            orderItem: item.order_item ? {
              id: String(item.order_item.id),
              productName: item.order_item.product_name,
              productImage: item.order_item.product_image,
              quantity: item.order_item.quantity
            } : null
          })) : []
        }));
      }
    } catch (error) {
      console.error('获取订单包裹信息失败:', error);
      // 当获取包裹信息失败时，仍然继续处理订单详情
      packages = [];
    }

    // 获取物流轨迹信息
    let logisticsTracks = [];
    try {
      // 导入物流轨迹服务
      const ExpressTrackService = require('../system/integration/services/express/ExpressTrackService');
      const prismaManager = require('../../../core/prisma');
      const prisma = prismaManager.getClient('base');
      const expressTrackService = new ExpressTrackService(prisma);
      
      // 获取订单的所有包裹轨迹
      const tracksResult = await expressTrackService.getTracksByOrderId(BigInt(orderId));
      
      if (tracksResult.success && tracksResult.data) {
        // 处理轨迹数据，转换为前端需要的格式
        logisticsTracks = tracksResult.data.map(track => {
          // 解析JSON格式的轨迹数据
          let trackData = [];
          try {
            if (track.track_data && typeof track.track_data === 'string') {
              trackData = JSON.parse(track.track_data);
            } else if (track.track_data && typeof track.track_data === 'object') {
              trackData = track.track_data;
            }
          } catch (e) {
            console.error('解析轨迹数据失败:', e);
            trackData = [];
          }
          
          return {
            id: String(track.id),
            packageId: String(track.package_id),
            orderId: String(track.order_id),
            expressNo: track.express_no,
            expressCode: track.express_code,
            expressName: track.express_name,
            status: track.status,
            conditionCode: track.condition_code,
            isCheck: track.is_check,
            trackData: trackData, // 已解析的轨迹数据
            lastUpdateTime: track.last_update_time ? String(track.last_update_time) : null,
            createdAt: String(track.created_at),
            updatedAt: String(track.updated_at)
          };
        });
        
        console.log(`获取到订单 ${orderId} 的物流轨迹数据 ${logisticsTracks.length} 条`);
      } else {
        console.log(`订单 ${orderId} 没有物流轨迹数据或获取失败:`, tracksResult.message);
      }
    } catch (error) {
      console.error('获取物流轨迹信息失败:', error);
      // 当获取物流轨迹信息失败时，仍然继续处理订单详情
      logisticsTracks = [];
    }

    // 计算订单项的待发货数量
    const processedItems = orderItems.map(item => ({
      ...item,
      // 待发货数量 = 商品总数量 - 已发货数量
      pendingShipQuantity: Math.max(0, item.quantity - (item.shipped_quantity || 0))
    }));

    // 合并物流信息到shipping对象中
    let enhancedShippingInfo = shippingInfo;
    if (shippingInfo && logisticsTracks && logisticsTracks.length > 0) {
      // 获取第一个物流轨迹信息（通常一个订单只有一个物流单号）
      const firstTrack = logisticsTracks[0];

      // 获取快递公司名称，优先使用express_name，如果为空则从数据库查询
      let companyName = firstTrack.expressName || '';
      if (!companyName && firstTrack.expressCode) {
        try {
          // 从数据库中查询快递公司名称
          const expressCompany = await this.prisma.expressCompanyCode.findFirst({
            where: {
              company_code: firstTrack.expressCode,
              deleted_at: null
            }
          });

          if (expressCompany) {
            companyName = expressCompany.company_name;
          } else {
            // 如果数据库中没有找到，使用编码作为名称
            companyName = firstTrack.expressCode;
          }
        } catch (error) {
          console.error('查询快递公司名称失败:', error);
          // 查询失败时使用编码作为名称
          companyName = firstTrack.expressCode;
        }
      }

      enhancedShippingInfo = {
        ...shippingInfo,
        // 添加物流公司和快递单号信息
        shippingCompanyName: companyName,
        trackingNumber: firstTrack.expressNo || '',
        // 保持原有字段的兼容性
        logisticsCompany: companyName,
        expressNo: firstTrack.expressNo || ''
      };
    }

    // 组合完整的订单详情
    // 处理渠道信息
    let channelName = '';
    let channelIconUrl = '';
    if (order.channel) {
      channelName = order.channel.name;
      channelIconUrl = order.channel.icon_url;
    }

    return {
      ...order,
      // 添加渠道名称和图标到返回数据中
      channelName,
      channelIconUrl,
      // 确保平台和店铺信息被正确返回
      platformId: order.platformId,
      storeId: order.storeId,
      platformName: order.platformName,
      storeName: order.storeName,
      items: processedItems,
      shipping: enhancedShippingInfo,
      salespeople: salespeople,
      logs: logs,
      packages: packages, // 添加包裹信息
      logisticsTracks: logisticsTracks, // 添加物流轨迹信息
      invoiceApplication: invoiceApplication, // 添加发票申请信息
      invoices: invoices // 添加发票记录信息
    };
  };

  /**
   * 更新订单状态
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {string} status - 新状态
   * @param {string} remark - 备注
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateOrderStatus(orderId, status, remark, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取订单信息
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 验证状态转换是否合法
        this.validateStatusTransition(order.orderStatus, status);

        // 3. 更新订单状态
        const updatedOrder = await orderModel.updateOrderStatus(orderId, status, remark);

        // 4. 记录状态变更日志
        await orderLogModel.recordStatusChange(
            orderId,
            order.orderStatus,
            status,
            operator
        );

        // 5. 特殊状态处理
        if (status === 'cancelled') {
          // 如果取消订单，需要恢复库存（暂时移除）
          // await this.restoreStock(orderId, tx);
        }

        return {
          success: true,
          data: updatedOrder
        };
      } catch (error) {
        console.error('更新订单状态失败', error);
        throw error;
      }
    });
  }

  /**
   * 验证订单状态转换是否合法
   * @param {string} currentStatus - 当前状态
   * @param {string} newStatus - 新状态
   * @throws {Error} - 如果状态转换不合法
   */
  validateStatusTransition(currentStatus, newStatus) {
    // 定义状态转换规则
    const allowedTransitions = {
      'pending': ['processing', 'cancelled'],
      'processing': ['shipped', 'completed', 'cancelled'],
      'shipped': ['delivered', 'returned', 'cancelled'],
      'delivered': ['completed', 'returned'],
      'completed': ['returned'],
      'returned': [],
      'cancelled': []
    };

    // 检查状态转换是否合法
    if (!allowedTransitions[currentStatus] || !allowedTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`不允许从 ${currentStatus} 状态转换到 ${newStatus} 状态`);
    }
  }

  /**
   * 恢复库存
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async restoreStock(orderId, tx) {
    // 获取订单项
    const orderItems = await tx.order_items.findMany({
      where: {
        order_id: BigInt(orderId)
      },
      select: {
        goods_sku_id: true,
        quantity: true
      }
    });

    // 恢复每个SKU的库存
    for (const item of orderItems) {
      await tx.goodsSku.update({
        where: {
          id: item.goods_sku_id
        },
        data: {
          stock: {
            increment: item.quantity
          },
          updated_at: BigInt(Date.now())
        }
      });
    }
  }

  /**
   * 更新订单支付信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} paymentData - 支付信息
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateOrderPayment(orderId, paymentData, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取订单信息
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 验证支付状态
        if (order.paymentStatus === 'paid') {
          throw new Error('订单已支付，不能重复支付');
        }

        // 3. 更新订单支付信息
        const updatedOrder = await orderModel.updateOrderPayment(orderId, {
          paymentMethod: paymentData.paymentMethod,
          transactionId: paymentData.transactionId,
          paidAmount: paymentData.paidAmount,
          paymentStatus: paymentData.paidAmount >= order.payableAmount ? 'paid' : 'partial_paid',
          paymentTime: BigInt(Date.now())
        });

        // 4. 记录支付日志
        await orderLogModel.recordPayment(
            orderId,
            {
              amount: paymentData.paidAmount,
              method: paymentData.paymentMethod,
              transactionId: paymentData.transactionId
            },
            operator
        );

        // 5. 如果是首次支付且金额足够，自动将订单状态更新为处理中
        if (order.paymentStatus === 'unpaid' && paymentData.paidAmount >= order.payableAmount && order.orderStatus === 'pending') {
          await orderModel.updateOrderStatus(orderId, 'processing', '支付完成，订单进入处理阶段');

          await orderLogModel.recordStatusChange(
              orderId,
              'pending',
              'processing',
              operator
          );
        }

        return {
          success: true,
          data: updatedOrder
        };
      } catch (error) {
        console.error('更新订单支付信息失败', error);
        throw error;
      }
    });
  }

  /**
   * 订单发货
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} shippingData - 物流信息
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 发货结果
   */
  async shipOrder(orderId, shippingData, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取订单信息
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);
        const orderShippingInfoModel = new OrderShippingInfoModel(tx);

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 验证订单状态
        // 使用订单状态枚举: PENDING_PAYMENT(0), PENDING_SHIPMENT(1), SHIPPED(2), COMPLETED(3), CANCELLED(4), CLOSED(5)
        if (order.orderStatus !== OrderStatusEnum.PENDING_SHIPMENT) { // 只允许待发货状态的订单发货
          throw new Error(`当前订单状态(${OrderStatusEnum.getStatusName(order.orderStatus)})不允许发货`);
        }

        // 3. 检查是否已有配送信息
        const existingShipping = await orderShippingInfoModel.getShippingInfoByOrderId(orderId);

        // 4. 更新或创建配送信息
        let shippingInfo;
        if (existingShipping) {
          // 准备更新数据
          const updateData = {
            // 更新配送方式
            shippingMethod: shippingData.shippingMethod
          };

          // 根据配送方式设置物流信息
          if (shippingData.shippingMethod === 1 || shippingData.shippingMethod === 2) {
            // 快递物流或自定义物流，需要物流公司和单号
            updateData.shippingCompanyCode = shippingData.shippingCompanyCode;
            updateData.shippingCompanyName = shippingData.shippingCompanyName;
            updateData.trackingNumber = shippingData.trackingNumber;
          } else {
            // 其他配送方式，清空物流信息
            updateData.shippingCompanyCode = '';
            updateData.shippingCompanyName = '';
            updateData.trackingNumber = '';
          }

          // 如果有附件图片URL，添加到更新数据中
          if (shippingData.imagesUrl) {
            updateData.images_url = shippingData.imagesUrl;
          }

          shippingInfo = await orderShippingInfoModel.updateLogisticsInfo(orderId, updateData);
        } else {
          // 获取订单的收件人信息
          // 如果请求中没有提供收件人信息，使用订单中的收件人信息
          const recipientName = shippingData.recipientName || order.recipientName;
          const recipientPhone = shippingData.recipientPhone || order.recipientPhone;
          const regionProvinceId = shippingData.regionProvinceId || order.regionProvinceId;
          const regionCityId = shippingData.regionCityId || order.regionCityId;
          const regionDistrictId = shippingData.regionDistrictId || order.regionDistrictId;
          const regionPathName = shippingData.regionPathName || order.regionPathName;
          const streetAddress = shippingData.streetAddress || order.streetAddress;
          const postalCode = shippingData.postalCode || order.postalCode;

          // 如果仍然缺少必填信息，抛出错误
          if (!recipientName || !recipientPhone || !streetAddress) {
            throw new Error('缺少必要的收件人信息，请提供收件人姓名、电话和详细地址');
          }

          // 准备创建数据
          const createData = {
            orderId: orderId,
            recipientName: recipientName,
            recipientPhone: recipientPhone,
            regionProvinceId: regionProvinceId,
            regionCityId: regionCityId,
            regionDistrictId: regionDistrictId,
            regionPathName: regionPathName,
            streetAddress: streetAddress,
            postalCode: postalCode,
            shippingMethod: shippingData.shippingMethod
          };

          // 根据配送方式设置物流信息
          if (shippingData.shippingMethod === 1 || shippingData.shippingMethod === 2) {
            // 快递物流或自定义物流，需要物流公司和单号
            createData.shippingCompanyCode = shippingData.shippingCompanyCode;
            createData.shippingCompanyName = shippingData.shippingCompanyName;
            createData.trackingNumber = shippingData.trackingNumber;
          }

          // 如果有附件图片URL，添加到创建数据中
          if (shippingData.imagesUrl) {
            createData.images_url = shippingData.imagesUrl;
          }

          shippingInfo = await orderShippingInfoModel.createShippingInfo(createData, tx);
        }

        // 5. 更新订单状态为已发货
        await orderModel.updateShippingStatus(orderId, ShippingStatusEnum.SHIPPED); // 发货状态更新：已发货
        const updatedOrder = await orderModel.updateOrderStatus(orderId, OrderStatusEnum.SHIPPED, '订单已发货，待收货');

        // 6. 记录发货日志
        await orderLogModel.recordShipping(
            orderId,
            {
              companyName: shippingData.shippingCompanyName,
              trackingNumber: shippingData.trackingNumber
            },
            operator
        );

        // 7. 记录状态变更日志
        await orderLogModel.recordStatusChange(
            orderId,
            order.orderStatus, // 原状态
            OrderStatusEnum.SHIPPED, // 新状态：已发货
            operator
        );

        return {
          success: true,
          data: {
            order: updatedOrder,
            shipping: shippingInfo
          }
        };
      } catch (error) {
        console.error('订单发货失败', error);
        throw error;
      }
    });
  }

  /**
   * 取消订单
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {string} cancelReason - 取消原因
   * @param {Object} operator - 操作者信息
   * @returns {Promise<Object>} - 取消结果
   */
  async cancelOrder(orderId, cancelReason, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        console.log(`[订单取消] 开始取消订单, 订单ID: ${orderId}, 取消原因: ${cancelReason}`);
        
        // 1. 获取订单信息（包含渠道信息）
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        console.log(`[订单取消] 订单信息: 订单ID=${order.id}, 渠道ID=${order.channelId}, 状态=${order.orderStatus}`);

        // 2. 验证订单状态
        // 使用订单状态枚举: PENDING_PAYMENT(0), PENDING_SHIPMENT(1), SHIPPED(2), COMPLETED(3), CANCELLED(4), REFUNDING(5)
        // 允许取消/关闭的状态：待付款、待发货、已发货
        const allowedStatuses = [
          OrderStatusEnum.PENDING_PAYMENT,   // 0 - 待付款
          OrderStatusEnum.PENDING_SHIPMENT,  // 1 - 待发货
          OrderStatusEnum.SHIPPED            // 2 - 已发货
        ];

        if (!allowedStatuses.includes(order.orderStatus)) {
          throw new Error(`当前订单状态(${OrderStatusEnum.getStatusName(order.orderStatus)})不允许关闭，只有待付款、待发货、已发货状态的订单可以关闭`);
        }

        // 3. 检查渠道内置状态和订单状态，决定是否需要恢复库存和减少销量
        let shouldRestoreStock = false;

        // 只有待付款和待发货状态的订单才考虑库存恢复
        // 已发货的订单关闭时不恢复库存，因为商品已经发出
        if (order.orderStatus === OrderStatusEnum.PENDING_PAYMENT || order.orderStatus === OrderStatusEnum.PENDING_SHIPMENT) {
          if (order.channelId) {
            console.log(`[订单关闭] 检查渠道ID: ${order.channelId}`);
            const channel = await tx.channel.findUnique({
              where: { id: BigInt(order.channelId) },
              select: { id: true, name: true, is_built_in: true }
            });

            if (channel) {
              console.log(`[订单关闭] 找到渠道信息: ID=${channel.id}, 名称=${channel.name}, 内置状态=${channel.is_built_in}`);
              if (channel.is_built_in === 1) {
                shouldRestoreStock = true;
                console.log(`[订单关闭] 渠道为内置渠道，将执行库存恢复和销量减少`);
              } else {
                console.log(`[订单关闭] 渠道非内置渠道，跳过库存恢复`);
              }
            } else {
              console.log(`[订单关闭] 警告: 渠道ID ${order.channelId} 不存在`);
            }
          } else {
            console.log(`[订单关闭] 订单未关联渠道，跳过渠道检查`);
          }
        } else {
          console.log(`[订单关闭] 订单状态为${OrderStatusEnum.getStatusName(order.orderStatus)}，跳过库存恢复`);
        }

        // 4. 更新订单状态为已关闭
        const updatedOrder = await orderModel.updateOrderStatus(orderId, OrderStatusEnum.CANCELLED, cancelReason, operator);

        // 5. 如果需要恢复库存，执行库存恢复和销量减少
        if (shouldRestoreStock) {
          console.log(`[订单关闭] 开始执行库存恢复和销量减少`);
          await this.restoreStockAndDecreaseSales(orderId, tx);
          console.log(`[订单关闭] 库存恢复和销量减少完成`);
        } else {
          console.log(`[订单关闭] 跳过库存恢复和销量减少`);
        }

        // 6. 记录状态变更日志
        await orderLogModel.recordStatusChange(
            orderId,
            order.orderStatus,
            OrderStatusEnum.CANCELLED, // 已关闭状态
            operator
        );

        // 7. 记录关闭原因日志
        const actionType = order.orderStatus === OrderStatusEnum.PENDING_PAYMENT ? 'cancel' : 'close';
        const actionText = order.orderStatus === OrderStatusEnum.PENDING_PAYMENT ? '取消' : '关闭';

        await orderLogModel.createLog({
          orderId: orderId,
          logType: actionType,
          logContent: `订单${actionText}原因：${cancelReason}`,
          operatorId: operator.id,
          operatorName: operator.name,
          operatorRole: operator.role,
          operationTime: BigInt(Date.now()),
          operationIp: operator.ip,
          operationPlatform: operator.platform
        }, tx);

        console.log(`[订单${actionText}] 订单${actionText}成功, 订单ID: ${order.id}, 库存恢复: ${shouldRestoreStock ? '是' : '否'}`);

        return {
          success: true,
          data: updatedOrder
        };
      } catch (error) {
        console.error('取消订单失败', error);
        throw error;
      }
    });
  }

  /**
   * 确认收货
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 确认结果
   */
  async confirmReceipt(orderId, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取订单信息
        const orderModel = new OrderModel(tx);
        const orderLogModel = new OrderLogModel(tx);

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 验证订单状态
        // 数字状态值: 0-待付款, 1-待发货, 2-待收货, 3-已完成, 4-已取消, 5-已关闭
        if (order.orderStatus !== 2) {
          throw new Error(`当前订单状态(${order.orderStatus})不允许确认收货，只有待收货状态的订单可以确认收货`);
        }

        // 3. 更新订单状态为已完成（3）
        const updatedOrder = await orderModel.updateOrderStatus(orderId, 3, '已确认收货', operator);

        // 4. 记录状态变更日志
        await orderLogModel.recordStatusChange(
            orderId,
            2, // 待收货状态码
            3, // 已完成状态码
            operator
        );

        // 5. 记录收货日志
        await orderLogModel.createLog({
          orderId: orderId,
          logType: 'receipt',
          logContent: '客户已确认收货',
          operatorId: operator.id,
          operatorName: operator.name,
          operatorRole: operator.role,
          operationTime: BigInt(Date.now()),
          operationIp: operator.ip,
          operationPlatform: operator.platform
        }, tx);

        return {
          success: true,
          data: updatedOrder
        };
      } catch (error) {
        console.error('确认收货失败', error);
        throw error;
      }
    });
  }
  /**
   * 获取订单跟单员列表
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} - 跟单员列表
   */
  async getOrderFollowers(orderId) {
    try {
      const orderFollowerModel = new OrderFollowerModel();
      const orderModel = this.orderModel;

      // 1. 获取订单信息
      const order = await orderModel.getOrderById(orderId);
      if (!order) {
        return {
          success: false,
          code: 404,
          message: `订单(ID: ${orderId})不存在`
        };
      }

      // 2. 获取跟单员列表
      const followers = await orderFollowerModel.getOrderFollowers(orderId);

      return {
        success: true,
        code: 200,
        message: '获取订单跟单员列表成功',
        data: followers
      };
    } catch (error) {
      console.error('获取订单跟单员列表失败', error);
      return {
        success: false,
        code: 500,
        message: `获取订单跟单员列表失败: ${error.message}`,
        error: error
      };
    }
  }


  /**
   * 批量添加/编辑订单跟单员
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Array<string|number|BigInt>} followerIds - 跟单员ID数组
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 添加/编辑结果
   */
  async addOrderFollowers(orderId, followerIds, operator) {
    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 1. 获取订单信息
        const orderModel = this.orderModel;
        const orderLogModel = this.orderLogModel;
        const orderFollowerModel = new OrderFollowerModel();

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 获取订单现有的未删除跟单员
        const existingFollowers = await orderFollowerModel.getOrderFollowers(orderId);
        
        // 将要保留的followerIds转换为Set，便于快速查找
        const followerIdSet = new Set(followerIds.map(id => id.toString()));
        
        // 需要软删除的跟单员ID列表
        const followersToRemove = existingFollowers
          .filter(f => !followerIdSet.has(f.followerId.toString()))
          .map(f => f.followerId);
        
        // 3. 软删除不在新列表中的现有跟单员
        for (const followerId of followersToRemove) {
          await orderFollowerModel.removeOrderFollower(orderId, followerId, operator.id);
        }
        
        // 4. 添加/恢复新列表中的跟单员
        const followers = [];
        for (const followerId of followerIds) {
          const follower = await orderFollowerModel.addOrderFollower({
            orderId,
            followerId,
            createdBy: operator.id
          });
          followers.push(follower);
        }

        // 5. 记录操作日志
        const logContent = followersToRemove.length > 0 
          ? `编辑跟单员: 添加/恢复 ${followerIds.join(', ')}, 删除 ${followersToRemove.join(', ')}` 
          : `批量添加跟单员: ${followerIds.join(', ')}`;
        
        await orderLogModel.createLog({
          orderId: orderId,
          logType: 'follower',
          logContent: logContent,
          operatorId: operator.id,
          operatorName: operator.name,
          operatorRole: operator.role,
          operationTime: BigInt(Date.now()),
          operationIp: operator.ip,
          operationPlatform: operator.platform
        }, tx);

        // 转换为驼峰命名格式
        const camelCaseFollowers = recursiveSnakeToCamel(followers);
        
        const message = followersToRemove.length > 0 ? '编辑跟单员成功' : '批量添加跟单员成功';
        
        return {
          success: true,
          code: 200,
          message: message,
          data: camelCaseFollowers
        };
      } catch (error) {
        throw error;
      }
    });
  }



  /**
   * 添加订单管理员备注
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {string} content - 备注内容
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 添加结果
   */
  async addOrderRemark(orderId, content, operator) {
    try {
      // 使用事务确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 1. 获取订单信息
        const orderModel = this.orderModel;
        const orderLogModel = this.orderLogModel;
        const orderRemarkModel = this.orderRemarkModel;

        const order = await orderModel.getOrderById(orderId);
        if (!order) {
          throw new Error(`订单(ID: ${orderId})不存在`);
        }

        // 2. 创建订单备注
        const remarkData = {
          orderId: orderId,
          content: content,
          createdBy: operator.id
        };

        const newRemark = await orderRemarkModel.createOrderRemark(remarkData);

        // 3. 记录操作日志
        await orderLogModel.createLog({
          orderId: orderId,
          logType: 'remark',
          logContent: `添加订单备注: ${content.length > 50 ? content.substring(0, 50) + '...' : content}`,
          operatorId: operator.id,
          operatorName: operator.name,
          operatorRole: operator.role,
          operationTime: BigInt(Date.now()),
          operationIp: operator.ip,
          operationPlatform: operator.platform
        }, tx);

        // 转换为驼峰命名格式
        const camelCaseRemark = recursiveSnakeToCamel(newRemark);

        return {
          success: true,
          code: 200,
          message: '添加订单备注成功',
          data: camelCaseRemark
        };
      });
    } catch (error) {
      console.error('添加订单备注失败', error);
      return {
        success: false,
        code: 500,
        message: `添加订单备注失败: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * 同步更新SPU的总库存和总销量
   * @param {Array} items - 订单项数组，包含goodsSkuId和quantity
   * @param {Object} tx - 事务对象
   * @returns {Promise<void>}
   */
  async updateSpuTotalStatsAfterStockChange(items, tx) {
    console.log(`[SPU同步] 开始同步更新SPU总库存和总销量`);
    
    // 获取涉及的所有SPU ID
    const skuIds = items.map(item => BigInt(item.goodsSkuId));
    
    // 查询所有相关的SKU，获取其所属的SPU ID
    const skus = await tx.goodsSku.findMany({
      where: { id: { in: skuIds } },
      select: { id: true, goods_spu_id: true, sku_code: true }
    });
    
    // 按SPU ID分组
    const spuIds = [...new Set(skus.map(sku => sku.goods_spu_id.toString()))];
    
    console.log(`[SPU同步] 需要更新 ${spuIds.length} 个SPU的统计数据`);
    
    // 为每个SPU重新计算总库存和总销量
    for (const spuId of spuIds) {
      console.log(`[SPU同步] 更新SPU ${spuId} 的统计数据`);
      
      // 查询该SPU下所有SKU的库存和销量总和
      const spuStats = await tx.goodsSku.aggregate({
        where: { 
          goods_spu_id: BigInt(spuId),
          deleted_at: null  // 只计算未删除的SKU
        },
        _sum: {
          stock: true,
          sales_volume: true
        }
      });
      
      const totalStock = spuStats._sum.stock || 0;
      const totalSales = spuStats._sum.sales_volume || 0;
      
      console.log(`[SPU同步] SPU ${spuId} 计算结果: 总库存=${totalStock}, 总销量=${totalSales}`);
      
      // 更新SPU的总库存和总销量
      await tx.goodsSpu.update({
        where: { id: BigInt(spuId) },
        data: {
          total_stock: totalStock,
          total_sales: totalSales,
          updated_at: BigInt(Date.now())
        }
      });
      
      console.log(`[SPU同步] SPU ${spuId} 统计数据更新完成`);
    }
    
    console.log(`[SPU同步] 所有SPU统计数据同步完成`);
  }

  /**
   * 根据支付状态自动更新订单状态
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {number} paymentStatus - 支付状态
   * @param {Object} operator - 操作人信息
   * @returns {Promise<Object>} - 更新结果
   */
  async updateOrderStatusByPaymentStatus(orderId, paymentStatus, operator) {
    try {
      // 根据支付状态确定应该设置的订单状态
      let newOrderStatus = null;
      let statusChangeReason = '';
      
      switch (parseInt(paymentStatus)) {
        case PaymentStatusEnum.PAID:
          // 已支付 -> 待发货（如果当前是待付款状态）
          const currentOrder = await this.orderModel.getOrderById(orderId);
          if (currentOrder && currentOrder.orderStatus === OrderStatusEnum.PENDING_PAYMENT) {
            newOrderStatus = OrderStatusEnum.PENDING_SHIPMENT;
            statusChangeReason = '支付完成，订单状态自动更新为待发货';
          }
          break;
          
        case PaymentStatusEnum.REFUNDING:
          // 退款中 -> 退款中
          newOrderStatus = OrderStatusEnum.REFUNDING;
          statusChangeReason = '订单进入退款流程，状态更新为退款中';
          break;
          
        case PaymentStatusEnum.REFUNDED:
          // 已退款 -> 已关闭
          newOrderStatus = OrderStatusEnum.CANCELLED;
          statusChangeReason = '退款完成，订单状态更新为已关闭';
          break;
      }
      
      // 如果需要更新订单状态
      if (newOrderStatus !== null) {
        const result = await this.updateOrderStatus(orderId, newOrderStatus, statusChangeReason, operator);
        
        console.log('根据支付状态自动更新订单状态:', {
          orderId: orderId.toString(),
          paymentStatus,
          newOrderStatus,
          statusChangeReason,
          result: result.success
        });
        
        return result;
      }
      
      return {
        success: true,
        message: '无需更新订单状态'
      };
      
    } catch (error) {
      console.error('根据支付状态自动更新订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建发票申请记录
   * @param {BigInt} orderId - 订单ID
   * @param {Object} invoiceData - 发票数据
   * @param {Object} currentUser - 当前用户信息
   * @param {Object} tx - 事务对象
   * @returns {Promise<Object>} - 创建结果
   */
  async createInvoiceRecord(orderId, invoiceData, currentUser, tx) {
    try {
      // 如果有发票抬头ID，获取抬头的完整信息作为快照
      let invoiceSnapshot = null;
      if (invoiceData.headerId) {
        console.log(`[发票申请] 查询发票抬头信息: ${invoiceData.headerId}`);

        // 查询发票抬头信息
        const invoiceHeader = await tx.mall_invoice_headers.findFirst({
          where: {
            id: BigInt(invoiceData.headerId),
            deleted_at: null
          }
        });

        if (!invoiceHeader) {
          throw new Error(`发票抬头ID ${invoiceData.headerId} 不存在或已删除`);
        }

        // 创建发票信息快照 - 使用独立字段存储
        invoiceSnapshot = {
          snapshotHeaderType: invoiceHeader.header_type,
          snapshotHeaderName: invoiceHeader.header_name,
          snapshotTaxNumber: invoiceHeader.tax_number,
          snapshotCompanyAddress: invoiceHeader.company_address,
          snapshotCompanyPhone: invoiceHeader.company_phone,
          snapshotBankName: invoiceHeader.bank_name,
          snapshotBankAccount: invoiceHeader.bank_account
        };

        console.log(`[发票申请] 发票抬头快照创建完成:`, invoiceSnapshot);
      }

      // 生成发票申请ID
      const applicationId = generateSnowflakeId();
      const currentTimestamp = BigInt(Date.now());

      // 准备发票申请数据
      const applicationData = {
        id: applicationId,
        order_id: BigInt(orderId),
        user_id: currentUser?.id ? BigInt(currentUser.id) : null,
        header_id: invoiceData.headerId ? BigInt(invoiceData.headerId) : null,
        application_type: invoiceData.invoiceType,
        application_amount: invoiceData.invoiceAmount || 0,
        application_status: 0, // 0-待审核
        // 发票抬头快照字段
        snapshot_header_type: invoiceSnapshot?.snapshotHeaderType || null,
        snapshot_header_name: invoiceSnapshot?.snapshotHeaderName || null,
        snapshot_tax_number: invoiceSnapshot?.snapshotTaxNumber || null,
        snapshot_company_address: invoiceSnapshot?.snapshotCompanyAddress || null,
        snapshot_company_phone: invoiceSnapshot?.snapshotCompanyPhone || null,
        snapshot_bank_name: invoiceSnapshot?.snapshotBankName || null,
        snapshot_bank_account: invoiceSnapshot?.snapshotBankAccount || null,
        applicant_remark: invoiceData.remark || null,
        created_by: currentUser?.id ? BigInt(currentUser.id) : null,
        created_at: currentTimestamp,
        updated_at: currentTimestamp
      };

      console.log(`[发票申请] 准备创建发票申请记录:`, {
        id: applicationData.id.toString(),
        orderId: orderId.toString(),
        applicationType: applicationData.application_type,
        applicationAmount: applicationData.application_amount
      });

      // 创建发票申请记录
      const application = await this.invoiceApplicationModel.createApplication(applicationData);

      console.log(`[发票申请] 发票申请记录创建成功: ${application.id}`);

      // 更新订单状态为"已申请开票"
      try {
        await tx.orders.update({
          where: { id: BigInt(orderId) },
          data: {
            invoice_status: ORDER_INVOICE_STATUS.APPLIED, // 已申请开票
            updated_at: BigInt(Date.now())
          }
        });
        console.log(`[发票申请] 订单开票状态更新成功: ${orderId}`);
      } catch (updateError) {
        console.error('更新订单开票状态失败:', updateError);
        // 不影响主流程，只记录错误
      }

      return {
        success: true,
        data: application
      };
    } catch (error) {
      console.error('创建发票申请记录失败:', error);
      throw error;
    }
  }
}

module.exports = OrderService;
