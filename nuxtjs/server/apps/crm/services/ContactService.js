const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const { getCurrentTimestamp } = require('../../../shared/utils/timestamp');
const regionService = require('../../master/services/RegionService');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 联系人服务类
 */
class ContactService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取省市区名称
   * @param {string} provinceId 省份ID
   * @param {string} cityId 城市ID
   * @param {string} districtId 区县ID
   * @returns {Promise<Object>} 包含省市区名称的对象
   */
  async getRegionNames(provinceId, cityId, districtId) {
    try {
      const regionCodes = [];
      if (provinceId) regionCodes.push(provinceId);
      if (cityId) regionCodes.push(cityId);
      if (districtId) regionCodes.push(districtId);

      if (regionCodes.length === 0) {
        return {
          provinceName: '',
          cityName: '',
          districtName: '',
          fullRegionName: ''
        };
      }

      // 分别获取各级区域名称
      const provinceName = provinceId ? await regionService.getRegionNamesByCodes([provinceId]) : '';
      const cityName = cityId ? await regionService.getRegionNamesByCodes([cityId]) : '';
      const districtName = districtId ? await regionService.getRegionNamesByCodes([districtId]) : '';

      // 将省市区名称直接合并，不用逗号隔开
      const fullRegionName = `${provinceName}${cityName}${districtName}`;

      return {
        provinceName,
        cityName,
        districtName,
        fullRegionName
      };
    } catch (error) {
      console.error('获取省市区名称失败:', error);
      return {
        provinceName: '',
        cityName: '',
        districtName: '',
        fullRegionName: ''
      };
    }
  }

  /**
   * 新增联系人
   * @param {CreateContactDto} createDto 联系人数据
   * @param {string} userId 操作用户ID
   * @returns {Promise<Object>} 新增的联系人信息
   */
  async createContact(createDto, userId = 'system') {
    try {
      console.log('🔍 服务：开始新增联系人');
      console.log('联系人数据:', createDto);

      // 生成雪花ID
      const contactId = generateSnowflakeId();
      const currentTime = getCurrentTimestamp();

      // 检查电话号码是否已存在
      if (createDto.contactPhone) {
        const existingContact = await this.prisma.crmCustomerContact.findFirst({
          where: {
            contactPhone: createDto.contactPhone,
            deletedAt: null
          }
        });

        if (existingContact) {
          throw new Error(`电话号码 ${createDto.contactPhone} 已存在`);
        }
      }

      // 如果设置为关键联系人且关联了客户，检查该客户是否已有关键联系人
      if (createDto.isDefault && createDto.customerId) {
        const existingKeyContact = await this.prisma.crmCustomerContact.findFirst({
          where: {
            customerId: BigInt(createDto.customerId),
            isDefault: true,
            deletedAt: null
          },
          select: {
            id: true,
            contactName: true
          }
        });

        if (existingKeyContact) {
          throw new Error(`该客户已存在关键联系人"${existingKeyContact.contactName}"，一个客户只能设置一个关键联系人。请先取消其他联系人的关键联系人设置。`);
        }
      }

      // 如果指定了客户ID，验证客户是否存在
      if (createDto.customerId) {
        const customer = await this.prisma.crmCustomer.findUnique({
          where: {
            id: BigInt(createDto.customerId)
          }
        });

        if (!customer || customer.deletedAt !== null) {
          throw new Error('指定的客户不存在');
        }
      }

      // 创建联系人数据
      const contactData = {
        id: contactId,
        customerId: createDto.customerId ? BigInt(createDto.customerId) : null,
        contactName: createDto.contactName,
        contactPhone: createDto.contactPhone,
        wechatId: createDto.wechatId,
        email: createDto.email,
        department: createDto.department,
        position: createDto.position,
        birthday: createDto.birthday ? BigInt(createDto.birthday) : null,
        provinceId: createDto.provinceId,
        cityId: createDto.cityId,
        districtId: createDto.districtId,
        detailAddress: createDto.detailAddress,
        isDefault: createDto.isDefault || false,
        remark: createDto.remark,
        createdAt: currentTime,
        updatedAt: currentTime,
        createdBy: userId && userId !== 'system' ? BigInt(userId) : null,
        updatedBy: userId && userId !== 'system' ? BigInt(userId) : null,
        deletedAt: null
      };

      // 创建联系人
      const newContact = await this.prisma.crmCustomerContact.create({
        data: contactData,
        include: {
          customer: {
            select: {
              id: true,
              customerName: true,
              customerAlias: true,
              status: true
            }
          }
        }
      });

      console.log('✅ 服务：新增联系人成功');

      const result = handleBigInt(newContact);

      // 将省市区字段组合为location数组
      const location = [];
      if (result.provinceId) location.push(result.provinceId);
      if (result.cityId) location.push(result.cityId);
      if (result.districtId) location.push(result.districtId);

      // 获取省市区名称
      const regionNames = await this.getRegionNames(
        result.provinceId,
        result.cityId,
        result.districtId
      );

      return {
        ...result,
        location: location.length > 0 ? location : null,
        provinceName: regionNames.provinceName,
        cityName: regionNames.cityName,
        districtName: regionNames.districtName,
        fullRegionName: regionNames.fullRegionName
      };
    } catch (error) {
      console.error('新增联系人失败:', error);
      throw new Error(`新增联系人失败: ${error.message}`);
    }
  }

  /**
   * 获取联系人列表
   * @param {ContactQueryDto} queryDto 查询条件
   * @returns {Promise<Object>} 联系人列表和分页信息
   */
  async getContactList(queryDto) {
    try {
      const {
        page, pageSize, contactName, contactPhone, customerName,
        customerId, department, position, email, wechatId, detailAddress,
        isDefault, startDate, endDate
      } = queryDto;

      // 确保 page 和 pageSize 是数字类型
      const pageNum = parseInt(page) || 1;
      const pageSizeNum = parseInt(pageSize) || 10;
      const skip = (pageNum - 1) * pageSizeNum;

      // 构建查询条件
      const where = {
        deletedAt: null // 只查询未删除的联系人
      };

      if (contactName) {
        where.contactName = {
          contains: contactName,
          mode: 'insensitive'
        };
      }

      if (contactPhone) {
        where.contactPhone = {
          contains: contactPhone,
          mode: 'insensitive'
        };
      }

      if (customerId) {
        where.customerId = BigInt(customerId);
      }

      if (department) {
        where.department = {
          contains: department,
          mode: 'insensitive'
        };
      }

      if (position) {
        where.position = {
          contains: position,
          mode: 'insensitive'
        };
      }

      if (email) {
        where.email = {
          contains: email,
          mode: 'insensitive'
        };
      }

      if (wechatId) {
        where.wechatId = {
          contains: wechatId,
          mode: 'insensitive'
        };
      }

      if (detailAddress) {
        where.detailAddress = {
          contains: detailAddress,
          mode: 'insensitive'
        };
      }

      if (isDefault !== null && isDefault !== undefined) {
        where.isDefault = isDefault === 'true' || isDefault === true;
      }

      // 时间范围查询
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = BigInt(new Date(startDate).getTime());
        }
        if (endDate) {
          where.createdAt.lte = BigInt(new Date(endDate).getTime());
        }
      }

      // 如果有客户名称查询，需要关联查询
      let include = {
        customer: {
          select: {
            id: true,
            customerName: true,
            customerAlias: true,
            status: true
          }
        }
      };

      // 如果有客户名称筛选，需要添加到where条件中
      if (customerName) {
        where.customer = {
          customerName: {
            contains: customerName,
            mode: 'insensitive'
          },
          deletedAt: null
        };
      }

      // 查询总数
      const total = await this.prisma.crmCustomerContact.count({
        where
      });

      // 查询数据
      const contacts = await this.prisma.crmCustomerContact.findMany({
        where,
        include,
        skip,
        take: pageSizeNum,
        orderBy: {
          createdAt: 'desc'
        }
      });

      // 处理数据格式，添加省市区名称
      const items = await Promise.all(contacts.map(async (contact) => {
        const result = handleBigInt(contact);

        // 将省市区字段组合为location数组
        const location = [];
        if (result.provinceId) location.push(result.provinceId);
        if (result.cityId) location.push(result.cityId);
        if (result.districtId) location.push(result.districtId);

        // 获取省市区名称
        const regionNames = await this.getRegionNames(
          result.provinceId,
          result.cityId,
          result.districtId
        );

        return {
          ...result,
          location: location.length > 0 ? location : null,
          provinceName: regionNames.provinceName,
          cityName: regionNames.cityName,
          districtName: regionNames.districtName,
          fullRegionName: regionNames.fullRegionName,
          customerName: result.customer?.customerName || null,
          customerAlias: result.customer?.customerAlias || null,
          customerStatus: result.customer?.status || null,
          // 返回毫秒时间戳
          createdAt: result.createdAt ? Number(result.createdAt) : null,
          updatedAt: result.updatedAt ? Number(result.updatedAt) : null,
          birthday: result.birthday ? Number(result.birthday) : null
        };
      }));

      return {
        items,
        pageInfo: {
          total,
          currentPage: pageNum,
          totalPage: Math.ceil(total / pageSizeNum)
        }
      };
    } catch (error) {
      console.error('获取联系人列表失败:', error);
      throw new Error(`获取联系人列表失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取联系人详情
   * @param {string} contactId 联系人ID
   * @returns {Promise<Object>} 联系人详情
   */
  async getContactById(contactId) {
    try {
      const contact = await this.prisma.crmCustomerContact.findUnique({
        where: {
          id: BigInt(contactId)
        },
        include: {
          customer: {
            select: {
              id: true,
              customerName: true,
              customerAlias: true,
              status: true
            }
          }
        }
      });

      if (!contact || contact.deletedAt) {
        throw new Error('联系人不存在');
      }

      const result = handleBigInt(contact);

      // 将省市区字段组合为location数组
      const location = [];
      if (result.provinceId) location.push(result.provinceId);
      if (result.cityId) location.push(result.cityId);
      if (result.districtId) location.push(result.districtId);

      // 获取省市区名称
      const regionNames = await this.getRegionNames(
        result.provinceId,
        result.cityId,
        result.districtId
      );

      return {
        ...result,
        location: location.length > 0 ? location : null,
        provinceName: regionNames.provinceName,
        cityName: regionNames.cityName,
        districtName: regionNames.districtName,
        fullRegionName: regionNames.fullRegionName,
        customerName: result.customer?.customerName || null,
        customerAlias: result.customer?.customerAlias || null,
        customerStatus: result.customer?.status || null,
        // 格式化时间戳
        createdAt: result.createdAt ? new Date(Number(result.createdAt)).toISOString() : null,
        updatedAt: result.updatedAt ? new Date(Number(result.updatedAt)).toISOString() : null,
        birthday: result.birthday ? new Date(Number(result.birthday)).toISOString() : null
      };
    } catch (error) {
      console.error('获取联系人详情失败:', error);
      throw new Error(`获取联系人详情失败: ${error.message}`);
    }
  }

  /**
   * 更新联系人信息
   * @param {string} contactId 联系人ID
   * @param {UpdateContactDto} updateDto 更新数据
   * @param {string} userId 操作用户ID
   * @returns {Promise<Object>} 更新后的联系人信息
   */
  async updateContact(contactId, updateDto, userId = 'system') {
    try {
      console.log('🔄 服务层：开始更新联系人');
      console.log('联系人ID:', contactId);
      console.log('更新数据:', updateDto);

      // 检查联系人是否存在
      const existingContact = await this.prisma.crmCustomerContact.findUnique({
        where: {
          id: BigInt(contactId)
        }
      });

      if (!existingContact || existingContact.deletedAt) {
        throw new Error('联系人不存在');
      }

      // 检查电话号码是否被其他联系人使用
      if (updateDto.contactPhone && updateDto.contactPhone !== existingContact.contactPhone) {
        const phoneExists = await this.prisma.crmCustomerContact.findFirst({
          where: {
            contactPhone: updateDto.contactPhone,
            deletedAt: null,
            id: {
              not: BigInt(contactId) // 排除当前联系人
            }
          }
        });

        if (phoneExists) {
          throw new Error(`电话号码 ${updateDto.contactPhone} 已被其他联系人使用`);
        }
      }

      // 如果指定了客户ID，验证客户是否存在
      if (updateDto.customerId) {
        const customer = await this.prisma.crmCustomer.findUnique({
          where: {
            id: BigInt(updateDto.customerId)
          }
        });

        if (!customer || customer.deletedAt !== null) {
          throw new Error('指定的客户不存在');
        }
      }

      // 如果设置为关键联系人且关联了客户，检查该客户是否已有其他关键联系人
      if (updateDto.isDefault && updateDto.customerId) {
        const existingKeyContact = await this.prisma.crmCustomerContact.findFirst({
          where: {
            customerId: BigInt(updateDto.customerId),
            isDefault: true,
            id: { not: BigInt(contactId) }, // 排除当前联系人
            deletedAt: null
          },
          select: {
            id: true,
            contactName: true
          }
        });

        if (existingKeyContact) {
          throw new Error(`该客户已存在关键联系人"${existingKeyContact.contactName}"，一个客户只能设置一个关键联系人。请先取消其他联系人的关键联系人设置。`);
        }
      }

      // 准备更新数据
      const updateData = {
        customerId: updateDto.customerId ? BigInt(updateDto.customerId) : null,
        contactName: updateDto.contactName,
        contactPhone: updateDto.contactPhone,
        wechatId: updateDto.wechatId,
        email: updateDto.email,
        department: updateDto.department,
        position: updateDto.position,
        birthday: updateDto.birthday ? BigInt(updateDto.birthday) : null,
        provinceId: updateDto.provinceId,
        cityId: updateDto.cityId,
        districtId: updateDto.districtId,
        detailAddress: updateDto.detailAddress,
        isDefault: updateDto.isDefault || false,
        remark: updateDto.remark,
        updatedAt: getCurrentTimestamp(),
        updatedBy: userId && userId !== 'system' ? BigInt(userId) : null
      };

      // 更新联系人
      const updatedContact = await this.prisma.crmCustomerContact.update({
        where: { id: BigInt(contactId) },
        data: updateData,
        include: {
          customer: {
            select: {
              id: true,
              customerName: true,
              customerAlias: true,
              status: true
            }
          }
        }
      });

      console.log('✅ 服务层：联系人更新成功');

      const result = handleBigInt(updatedContact);

      // 将省市区字段组合为location数组
      const location = [];
      if (result.provinceId) location.push(result.provinceId);
      if (result.cityId) location.push(result.cityId);
      if (result.districtId) location.push(result.districtId);

      // 获取省市区名称
      const regionNames = await this.getRegionNames(
        result.provinceId,
        result.cityId,
        result.districtId
      );

      return {
        ...result,
        location: location.length > 0 ? location : null,
        provinceName: regionNames.provinceName,
        cityName: regionNames.cityName,
        districtName: regionNames.districtName,
        fullRegionName: regionNames.fullRegionName,
        customerName: result.customer?.customerName || null,
        customerAlias: result.customer?.customerAlias || null,
        customerStatus: result.customer?.status || null,
        // 格式化时间戳
        createdAt: result.createdAt ? new Date(Number(result.createdAt)).toISOString() : null,
        updatedAt: result.updatedAt ? new Date(Number(result.updatedAt)).toISOString() : null,
        birthday: result.birthday ? new Date(Number(result.birthday)).toISOString() : null
      };
    } catch (error) {
      console.error('❌ 服务层：更新联系人失败');
      console.error('错误信息:', error.message);
      console.error('错误堆栈:', error.stack);
      throw new Error(`更新联系人失败: ${error.message}`);
    }
  }

  /**
   * 绑定联系人到客户
   * @param {string} contactId 联系人ID
   * @param {string} customerId 客户ID
   * @param {string} userId 操作用户ID
   * @returns {Promise<Object>} 绑定结果
   */
  async bindCustomer(contactId, customerId, userId = 'system') {
    try {
      // 检查联系人是否存在
      const existingContact = await this.prisma.crmCustomerContact.findUnique({
        where: {
          id: BigInt(contactId)
        }
      });

      if (!existingContact || existingContact.deletedAt) {
        throw new Error('联系人不存在');
      }

      // 检查客户是否存在
      const existingCustomer = await this.prisma.crmCustomer.findUnique({
        where: {
          id: BigInt(customerId)
        }
      });

      if (!existingCustomer || existingCustomer.deletedAt) {
        throw new Error('客户不存在');
      }

      // 如果该联系人是关键联系人，检查目标客户是否已有关键联系人
      if (existingContact.isDefault) {
        const existingKeyContact = await this.prisma.crmCustomerContact.findFirst({
          where: {
            customerId: BigInt(customerId),
            isDefault: true,
            id: { not: BigInt(contactId) }, // 排除当前联系人
            deletedAt: null
          },
          select: {
            id: true,
            contactName: true
          }
        });

        if (existingKeyContact) {
          throw new Error(`该客户已存在关键联系人"${existingKeyContact.contactName}"，一个客户只能设置一个关键联系人。请先取消其他联系人的关键联系人设置。`);
        }
      }

      // 更新联系人的客户关联
      const updatedContact = await this.prisma.crmCustomerContact.update({
        where: { id: BigInt(contactId) },
        data: {
          customerId: BigInt(customerId),
          updatedAt: getCurrentTimestamp(),
          updatedBy: userId && userId !== 'system' ? BigInt(userId) : null
        },
        include: {
          customer: {
            select: {
              id: true,
              customerName: true,
              customerAlias: true,
              status: true
            }
          }
        }
      });

      const result = handleBigInt(updatedContact);
      return {
        ...result,
        customerName: result.customer?.customerName || null,
        customerAlias: result.customer?.customerAlias || null,
        customerStatus: result.customer?.status || null
      };
    } catch (error) {
      console.error('绑定客户失败:', error);
      throw new Error(`绑定客户失败: ${error.message}`);
    }
  }

  /**
   * 解绑联系人与客户的关联
   * @param {string} contactId 联系人ID
   * @param {string} userId 操作用户ID
   * @returns {Promise<Object>} 解绑结果
   */
  async unbindCustomer(contactId, userId = 'system') {
    try {
      // 检查联系人是否存在
      const existingContact = await this.prisma.crmCustomerContact.findUnique({
        where: {
          id: BigInt(contactId)
        }
      });

      if (!existingContact || existingContact.deletedAt) {
        throw new Error('联系人不存在');
      }

      // 更新联系人，移除客户关联
      const updatedContact = await this.prisma.crmCustomerContact.update({
        where: { id: BigInt(contactId) },
        data: {
          customerId: null,
          updatedAt: getCurrentTimestamp(),
          updatedBy: userId && userId !== 'system' ? BigInt(userId) : null
        }
      });

      return handleBigInt(updatedContact);
    } catch (error) {
      console.error('解绑客户失败:', error);
      throw new Error(`解绑客户失败: ${error.message}`);
    }
  }
}

module.exports = ContactService;
