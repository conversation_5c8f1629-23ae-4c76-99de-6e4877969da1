const { generateCustomerId } = require('../../../shared/utils/customerIdGenerator');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const regionService = require('../../master/services/RegionService');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 客户管理服务
 */
class CustomerService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取省市区名称
   * @param {string} provinceId 省份ID
   * @param {string} cityId 城市ID
   * @param {string} districtId 区县ID
   * @returns {Promise<Object>} 包含省市区名称的对象
   */
  async getRegionNames(provinceId, cityId, districtId) {
    try {
      const regionCodes = [];
      if (provinceId) regionCodes.push(provinceId);
      if (cityId) regionCodes.push(cityId);
      if (districtId) regionCodes.push(districtId);

      if (regionCodes.length === 0) {
        return {
          provinceName: '',
          cityName: '',
          districtName: '',
          fullRegionName: ''
        };
      }

      // 分别获取各级区域名称
      const provinceName = provinceId ? await regionService.getRegionNamesByCodes([provinceId]) : '';
      const cityName = cityId ? await regionService.getRegionNamesByCodes([cityId]) : '';
      const districtName = districtId ? await regionService.getRegionNamesByCodes([districtId]) : '';

      // 将省市区名称直接合并，不用逗号隔开
      const fullRegionName = `${provinceName}${cityName}${districtName}`;

      return {
        provinceName,
        cityName,
        districtName,
        fullRegionName
      };
    } catch (error) {
      console.error('获取省市区名称失败:', error);
      return {
        provinceName: '',
        cityName: '',
        districtName: '',
        fullRegionName: ''
      };
    }
  }

  /**
   * 创建客户
   * @param {CreateCustomerDto} customerDto 客户数据
   * @param {string} userId 创建用户ID
   * @returns {Promise<Object>} 创建的客户信息
   */
  async createCustomer(customerDto, userId) {
    try {
      console.log('=== 开始创建客户 ===');
      console.log('客户DTO数据:', JSON.stringify(customerDto, null, 2));
      console.log('用户ID:', userId);

      // 检查客户名称是否已存在
      console.log('检查客户名称是否存在:', customerDto.customerName);
      const existingCustomer = await this.prisma.crmCustomer.findFirst({
        where: {
          customerName: customerDto.customerName
        }
      });

      if (existingCustomer) {
        console.log('❌ 客户名称已存在');
        throw new Error('客户名称已存在');
      }
      console.log('✅ 客户名称检查通过');

      // 验证业务员是否存在
      console.log('验证业务员信息...');
      if (customerDto.salesperson1) {
        console.log('验证第一级业务员:', customerDto.salesperson1);
        const salesperson1 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson1) }
        });
        if (!salesperson1) {
          console.log('❌ 第一级业务员不存在');
          throw new Error('第一级业务员不存在');
        }
        console.log('✅ 第一级业务员验证通过');
      }

      if (customerDto.salesperson2) {
        const salesperson2 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson2) }
        });
        if (!salesperson2) {
          throw new Error('第二级业务员不存在');
        }
      }

      if (customerDto.salesperson3) {
        const salesperson3 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson3) }
        });
        if (!salesperson3) {
          throw new Error('第三级业务员不存在');
        }
      }

      // 检查联系人中是否有多个默认联系人
      const defaultContacts = customerDto.contacts.filter(contact => contact.isDefault);
      if (defaultContacts.length > 1) {
        throw new Error('只能设置一个默认联系人');
      }

      // 生成客户ID（年月日时分秒+两位递增数字）
      const customerId = generateCustomerId();
      const now = BigInt(Date.now());
      const userIdBigInt = userId && userId !== 'system' ? BigInt(userId) : null;

      console.log('生成的客户ID:', customerId);
      console.log('当前时间戳:', now);
      console.log('用户ID (BigInt):', userIdBigInt);

      console.log('准备创建客户数据...');
      console.log('客户基本信息:', {
        customerName: customerDto.customerName,
        orderAccount: customerDto.orderAccount,
        orderPlatformId: customerDto.orderPlatformId,
        customerLevel: customerDto.customerLevel,
        enterpriseNature: customerDto.enterpriseNature
      });

      // 创建客户及关联数据
      console.log('开始执行数据库创建操作...');
      const customer = await this.prisma.crmCustomer.create({
        data: {
          id: customerId,
          customerName: customerDto.customerName,
          customerAlias: customerDto.customerAlias,
          parentGroup: customerDto.parentGroup,
          source: customerDto.source,
          industry: customerDto.industry,
          status: customerDto.status,
          companyScale: customerDto.companyScale,
          provinceId: customerDto.provinceId,
          cityId: customerDto.cityId,
          districtId: customerDto.districtId,
          detailAddress: customerDto.detailAddress,
          remark: customerDto.remark,

          // 新增字段
          orderAccount: customerDto.orderAccount,
          orderPlatformId: customerDto.orderPlatformId ? BigInt(customerDto.orderPlatformId) : null,
          customerLevel: customerDto.customerLevel,
          enterpriseNature: customerDto.enterpriseNature,
          businessScope: customerDto.businessScope,
          topThreePeers: customerDto.topThreePeers,
          quotationNotes: customerDto.quotationNotes,
          listingNotes: customerDto.listingNotes,
          shippingNotes: customerDto.shippingNotes,

          salespersonId: customerDto.salesperson || null,
          createdAt: now,
          updatedAt: now,
          createdBy: userIdBigInt,
          updatedBy: userIdBigInt,
          
          // 创建财务信息
          financeInfo: customerDto.financeInfo ? {
            create: {
              id: generateSnowflakeId(),
              accountHolderName: customerDto.financeInfo.accountHolderName,
              bankName: customerDto.financeInfo.bankName,
              bankAccount: customerDto.financeInfo.bankAccount,
              accountPeriodBasis: customerDto.financeInfo.accountPeriodBasis,
              settlementMethod: customerDto.financeInfo.settlementMethod,
              settlementDate: customerDto.financeInfo.settlementDate,
              createdAt: now,
              updatedAt: now,
              createdBy: userIdBigInt,
              updatedBy: userIdBigInt
            }
          } : undefined,
          
          // 创建开票信息
          invoiceInfos: customerDto.invoiceInfos.length > 0 ? {
            create: customerDto.invoiceInfos.map(info => ({
              id: generateSnowflakeId(),
              invoiceTitle: info.invoiceTitle,
              taxNumber: info.taxNumber,
              companyAddress: info.companyAddress,
              companyPhone: info.companyPhone,
              bankName: info.bankName,
              bankAccount: info.bankAccount,

              // 新增字段
              invoiceAddress: info.invoiceAddress,
              invoicePhone: info.invoicePhone,
              createdAt: now,
              updatedAt: now,
              createdBy: userIdBigInt,
              updatedBy: userIdBigInt
            }))
          } : undefined,
          
          // 创建联系人信息
          contacts: customerDto.contacts.length > 0 ? {
            create: customerDto.contacts.map(contact => ({
              id: generateSnowflakeId(),
              contactName: contact.contactName,
              contactPhone: contact.contactPhone,
              wechatId: contact.wechatId,
              email: contact.email,
              department: contact.department,
              position: contact.position,
              birthday: contact.birthday,
              provinceId: contact.provinceId,
              cityId: contact.cityId,
              districtId: contact.districtId,
              detailAddress: contact.detailAddress,
              isDefault: contact.isDefault,
              remark: contact.remark,
              createdAt: now,
              updatedAt: now,
              createdBy: userIdBigInt,
              updatedBy: userIdBigInt
            }))
          } : undefined,
          
          // 创建附件信息
          attachments: customerDto.attachments && customerDto.attachments.length > 0 ? (() => {
            const validAttachments = customerDto.attachments
              .filter(attachment => attachment && attachment.fileUrl && attachment.fileUrl.trim() !== ''); // 只要求fileUrl

            return validAttachments.length > 0 ? {
              create: validAttachments.map(attachment => ({
                id: generateSnowflakeId(),
                fileName: attachment.fileName || '', // fileName可以为空
                fileUrl: attachment.fileUrl,
                fileSize: attachment.fileSize,
                fileType: attachment.fileType,
                createdAt: now,
                updatedAt: now,
                createdBy: userIdBigInt,
                updatedBy: userIdBigInt
              }))
            } : undefined;
          })() : undefined
        },
        include: {
          financeInfo: true,
          invoiceInfos: true,
          contacts: true,
          attachments: true
        }
      });

      console.log('✅ 客户创建成功!');
      console.log('创建的客户ID:', customer.id);
      console.log('客户名称:', customer.customerName);

      const result = handleBigInt(customer);

      // 将省市区字段组合为companyRegion数组
      const companyRegion = [];
      if (result.provinceId) companyRegion.push(result.provinceId);
      if (result.cityId) companyRegion.push(result.cityId);
      if (result.districtId) companyRegion.push(result.districtId);
      result.companyRegion = companyRegion.length > 0 ? companyRegion : null;

      // 处理联系人的省市区字段，将其组合为location数组
      if (result.contacts && Array.isArray(result.contacts)) {
        result.contacts = result.contacts.map(contact => {
          const contactLocation = [];
          if (contact.provinceId) contactLocation.push(contact.provinceId);
          if (contact.cityId) contactLocation.push(contact.cityId);
          if (contact.districtId) contactLocation.push(contact.districtId);

          return {
            ...contact,
            location: contactLocation.length > 0 ? contactLocation : null
          };
        });
      }

      console.log('返回结果处理完成');
      return result;
    } catch (error) {
      console.error('❌ 创建客户失败:');
      console.error('错误类型:', error.constructor.name);
      console.error('错误消息:', error.message);
      console.error('错误堆栈:', error.stack);

      // 如果是Prisma错误，打印更详细的信息
      if (error.code) {
        console.error('Prisma错误代码:', error.code);
      }
      if (error.meta) {
        console.error('Prisma错误元数据:', JSON.stringify(error.meta, null, 2));
      }

      throw new Error(`创建客户失败: ${error.message}`);
    }
  }

  /**
   * 获取客户列表
   * @param {CustomerQueryDto} queryDto 查询条件
   * @returns {Promise<Object>} 客户列表和分页信息
   */
  async getCustomerList(queryDto) {
    try {
      const {
        page, pageSize, customerName, source, industry, status,
        salespersonId, startDate, endDate,
        contactName, contactPhone, salespersonName
      } = queryDto;
      // 确保 page 和 pageSize 是数字类型
      const pageNum = parseInt(page) || 1;
      const pageSizeNum = parseInt(pageSize) || 10;
      const skip = (pageNum - 1) * pageSizeNum;

      // 构建查询条件
      const where = {
        deletedAt: null // 只查询未删除的客户
      };

      if (customerName) {
        where.customerName = {
          contains: customerName,
          mode: 'insensitive'
        };
      }

      if (source !== null && source !== undefined) {
        where.source = parseInt(source);
      }

      if (industry !== null && industry !== undefined) {
        where.industry = parseInt(industry);
      }

      if (status !== null && status !== undefined) {
        where.status = parseInt(status);
      }

      if (salespersonId) {
        // 支持按业务员ID筛选（包含查询）
        // 业务员ID可能是逗号分隔的字符串，需要精确匹配
        const salespersonStr = String(salespersonId);
        where.OR = [
          // 完全匹配（只有一个业务员的情况）
          { salespersonId: salespersonStr },
          // 开头匹配（第一个业务员）
          { salespersonId: { startsWith: salespersonStr + ',' } },
          // 中间匹配（中间的业务员）
          { salespersonId: { contains: ',' + salespersonStr + ',' } },
          // 结尾匹配（最后一个业务员）
          { salespersonId: { endsWith: ',' + salespersonStr } }
        ];
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }
        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // 联系人筛选条件
      if (contactName || contactPhone) {
        const contactWhere = {};

        if (contactName) {
          contactWhere.contactName = {
            contains: contactName,
            mode: 'insensitive'
          };
        }

        if (contactPhone) {
          contactWhere.contactPhone = {
            contains: contactPhone,
            mode: 'insensitive'
          };
        }

        where.contacts = {
          some: contactWhere
        };
      }

      // 业务员名称筛选条件（需要特殊处理，因为需要先查询业务员ID）
      let salespersonIds = [];
      if (salespersonName) {
        try {
          const salespersons = await this.prisma.baseSystemUser.findMany({
            where: {
              username: {
                contains: salespersonName,
                mode: 'insensitive'
              }
            },
            select: {
              id: true
            }
          });
          salespersonIds = salespersons.map(sp => sp.id);
        } catch (error) {
          console.error('查询业务员失败:', error);
        }

        // 如果有业务员名称筛选
        if (salespersonIds.length > 0) {
          // 找到了匹配的业务员，构建OR条件
          const nameFilterConditions = [];
          salespersonIds.forEach(id => {
            const idStr = String(id);
            nameFilterConditions.push(
              // 完全匹配（只有一个业务员的情况）
              { salespersonId: idStr },
              // 开头匹配（第一个业务员）
              { salespersonId: { startsWith: idStr + ',' } },
              // 中间匹配（中间的业务员）
              { salespersonId: { contains: ',' + idStr + ',' } },
              // 结尾匹配（最后一个业务员）
              { salespersonId: { endsWith: ',' + idStr } }
            );
          });

          // 如果已经有OR条件（比如业务员ID筛选），需要合并
          if (where.OR) {
            where.AND = [
              { OR: where.OR },
              { OR: nameFilterConditions }
            ];
            delete where.OR;
          } else {
            where.OR = nameFilterConditions;
          }
        } else {
          // 没有找到匹配的业务员，添加一个永远不会匹配的条件
          // 这样可以确保当搜索不存在的业务员名称时返回空结果
          where.salespersonId = 'NONEXISTENT_SALESPERSON_ID';
        }
      }

      // 查询总数
      const total = await this.prisma.crmCustomer.count({ where });

      // 查询列表
      const customers = await this.prisma.crmCustomer.findMany({
        where,
        skip,
        take: pageSizeNum,
        include: {
          financeInfo: true,
          invoiceInfos: true,
          contacts: true,
          attachments: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // 手动查询业务员信息
      const customersWithSalesperson = await Promise.all(
        customers.map(async (customer) => {
          const customerData = { ...customer };

          // 将省市区字段组合为companyRegion数组
          const companyRegion = [];
          if (customer.provinceId) companyRegion.push(customer.provinceId);
          if (customer.cityId) companyRegion.push(customer.cityId);
          if (customer.districtId) companyRegion.push(customer.districtId);
          customerData.companyRegion = companyRegion.length > 0 ? companyRegion : null;

          // 获取客户省市区名称
          const customerRegionNames = await this.getRegionNames(
            customer.provinceId,
            customer.cityId,
            customer.districtId
          );
          customerData.provinceName = customerRegionNames.provinceName;
          customerData.cityName = customerRegionNames.cityName;
          customerData.districtName = customerRegionNames.districtName;
          customerData.fullRegionName = customerRegionNames.fullRegionName;

          // 处理联系人的省市区字段，将其组合为location数组并添加名称
          if (customerData.contacts && Array.isArray(customerData.contacts)) {
            customerData.contacts = await Promise.all(customerData.contacts.map(async (contact) => {
              const contactLocation = [];
              if (contact.provinceId) contactLocation.push(contact.provinceId);
              if (contact.cityId) contactLocation.push(contact.cityId);
              if (contact.districtId) contactLocation.push(contact.districtId);

              // 获取联系人省市区名称
              const contactRegionNames = await this.getRegionNames(
                contact.provinceId,
                contact.cityId,
                contact.districtId
              );

              return {
                ...contact,
                location: contactLocation.length > 0 ? contactLocation : null,
                provinceName: contactRegionNames.provinceName,
                cityName: contactRegionNames.cityName,
                districtName: contactRegionNames.districtName,
                fullRegionName: contactRegionNames.fullRegionName
              };
            }));
          }

          // 查询业务员信息
          if (customer.salespersonId) {
            try {
              // 解析业务员ID字符串为数组
              const salespersonIds = customer.salespersonId.split(',').filter(id => id.trim());
              const salespersonIdsBigInt = salespersonIds.map(id => BigInt(id.trim()));

              if (salespersonIdsBigInt.length > 0) {
                const salespersons = await this.prisma.baseSystemUser.findMany({
                  where: {
                    id: { in: salespersonIdsBigInt }
                  },
                  select: {
                    id: true,
                    username: true,
                    nickname: true
                  }
                });

                // 按照原始ID顺序重新排序业务员数组
                const sortedSalespersons = salespersonIds.map(originalId => {
                  return salespersons.find(sp => sp.id.toString() === originalId);
                }).filter(Boolean); // 过滤掉未找到的业务员

                customerData.salespersons = sortedSalespersons;
              } else {
                customerData.salespersons = [];
              }
            } catch (error) {
              console.error('查询业务员信息失败:', error);
              customerData.salespersons = [];
            }
          } else {
            customerData.salespersons = [];
          }

          // 移除旧的业务员字段
          delete customerData.salesperson1Id;
          delete customerData.salesperson2Id;
          delete customerData.salesperson3Id;
          console.log('已删除旧业务员字段，客户ID:', customerData.id);

          return customerData;
        })
      );

      // 处理BigInt并移除所有旧业务员字段
      const processedCustomers = handleBigInt(customersWithSalesperson).map(customer => {
        // 移除所有旧的业务员相关字段
        const {
          salesperson1Id, salesperson2Id, salesperson3Id,
          salesperson1, salesperson2, salesperson3,
          salesperson1Name, salesperson2Name, salesperson3Name,
          ...cleanCustomer
        } = customer;
        return cleanCustomer;
      });

      return {
        items: processedCustomers,
        pageInfo: {
          total,
          currentPage: pageNum,
          totalPage: Math.ceil(total / pageSizeNum)
        }
      };
    } catch (error) {
      throw new Error(`获取客户列表失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取客户详情
   * @param {string} customerId 客户ID
   * @returns {Promise<Object>} 客户详情
   */
  async getCustomerById(customerId) {
    try {
      const customer = await this.prisma.crmCustomer.findFirst({
        where: {
          id: BigInt(customerId),
          deletedAt: null // 只查询未删除的客户
        },
        include: {
          financeInfo: true,
          invoiceInfos: true,
          contacts: true,
          attachments: true
        }
      });

      if (!customer) {
        throw new Error('客户不存在或已被删除');
      }

      // 手动查询业务员信息
      const customerData = { ...customer };

      // 将省市区字段组合为companyRegion数组
      const companyRegion = [];
      if (customer.provinceId) companyRegion.push(customer.provinceId);
      if (customer.cityId) companyRegion.push(customer.cityId);
      if (customer.districtId) companyRegion.push(customer.districtId);
      customerData.companyRegion = companyRegion.length > 0 ? companyRegion : null;

      // 获取客户省市区名称
      const customerRegionNames = await this.getRegionNames(
        customer.provinceId,
        customer.cityId,
        customer.districtId
      );
      customerData.provinceName = customerRegionNames.provinceName;
      customerData.cityName = customerRegionNames.cityName;
      customerData.districtName = customerRegionNames.districtName;
      customerData.fullRegionName = customerRegionNames.fullRegionName;

      // 处理联系人的省市区字段，将其组合为location数组并添加名称
      if (customerData.contacts && Array.isArray(customerData.contacts)) {
        customerData.contacts = await Promise.all(customerData.contacts.map(async (contact) => {
          const contactLocation = [];
          if (contact.provinceId) contactLocation.push(contact.provinceId);
          if (contact.cityId) contactLocation.push(contact.cityId);
          if (contact.districtId) contactLocation.push(contact.districtId);

          // 获取联系人省市区名称
          const contactRegionNames = await this.getRegionNames(
            contact.provinceId,
            contact.cityId,
            contact.districtId
          );

          return {
            ...contact,
            location: contactLocation.length > 0 ? contactLocation : null,
            provinceName: contactRegionNames.provinceName,
            cityName: contactRegionNames.cityName,
            districtName: contactRegionNames.districtName,
            fullRegionName: contactRegionNames.fullRegionName
          };
        }));
      }

      // 查询业务员信息
      if (customer.salespersonId) {
        try {
          // 解析业务员ID字符串为数组
          const salespersonIds = customer.salespersonId.split(',').filter(id => id.trim());
          const salespersonIdsBigInt = salespersonIds.map(id => BigInt(id.trim()));

          if (salespersonIdsBigInt.length > 0) {
            const salespersons = await this.prisma.baseSystemUser.findMany({
              where: {
                id: { in: salespersonIdsBigInt }
              },
              select: {
                id: true,
                username: true,
                nickname: true
              }
            });

            // 按照原始ID顺序重新排序业务员数组
            const sortedSalespersons = salespersonIds.map(originalId => {
              return salespersons.find(sp => sp.id.toString() === originalId);
            }).filter(Boolean); // 过滤掉未找到的业务员

            customerData.salespersons = sortedSalespersons;
          } else {
            customerData.salespersons = [];
          }
        } catch (error) {
          console.error('查询业务员信息失败:', error);
          customerData.salespersons = [];
        }
      } else {
        customerData.salespersons = [];
      }

      // 处理BigInt并移除所有旧业务员字段
      const processedData = handleBigInt(customerData);
      const {
        salesperson1Id, salesperson2Id, salesperson3Id,
        salesperson1, salesperson2, salesperson3,
        salesperson1Name, salesperson2Name, salesperson3Name,
        ...cleanData
      } = processedData;

      return cleanData;
    } catch (error) {
      throw new Error(`获取客户详情失败: ${error.message}`);
    }
  }

  /**
   * 更新客户信息
   * @param {string} customerId 客户ID
   * @param {UpdateCustomerDto} customerDto 更新数据
   * @param {string} userId 更新用户ID
   * @returns {Promise<Object>} 更新后的客户信息
   */
  async updateCustomer(customerId, customerDto, userId) {
    try {
      console.log('=== 开始更新客户 ===');
      console.log('客户ID:', customerId);
      console.log('客户DTO数据:', JSON.stringify(customerDto, null, 2));
      console.log('用户ID:', userId);

      // 检查客户是否存在
      console.log('检查客户是否存在...');
      const existingCustomer = await this.prisma.crmCustomer.findUnique({
        where: { id: BigInt(customerId) }
      });

      if (!existingCustomer) {
        console.log('❌ 客户不存在');
        throw new Error('客户不存在');
      }
      console.log('✅ 客户存在，当前客户名称:', existingCustomer.customerName);

      // 检查客户名称是否与其他客户重复
      console.log('检查客户名称重复...');
      if (customerDto.customerName !== existingCustomer.customerName) {
        console.log('客户名称有变更，检查是否与其他客户重复...');
        const duplicateCustomer = await this.prisma.crmCustomer.findFirst({
          where: {
            customerName: customerDto.customerName,
            id: { not: BigInt(customerId) }
          }
        });

        if (duplicateCustomer) {
          console.log('❌ 客户名称已存在');
          throw new Error('客户名称已存在');
        }
        console.log('✅ 客户名称检查通过');
      } else {
        console.log('✅ 客户名称未变更');
      }

      // 验证业务员是否存在
      if (customerDto.salesperson1) {
        const salesperson1 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson1) }
        });
        if (!salesperson1) {
          throw new Error('第一级业务员不存在');
        }
      }

      if (customerDto.salesperson2) {
        const salesperson2 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson2) }
        });
        if (!salesperson2) {
          throw new Error('第二级业务员不存在');
        }
      }

      if (customerDto.salesperson3) {
        const salesperson3 = await this.prisma.baseSystemUser.findUnique({
          where: { id: BigInt(customerDto.salesperson3) }
        });
        if (!salesperson3) {
          throw new Error('第三级业务员不存在');
        }
      }

      // 检查联系人中是否有多个默认联系人
      if (customerDto.contacts && customerDto.contacts.length > 0) {
        const defaultContacts = customerDto.contacts.filter(contact => contact.isDefault);
        if (defaultContacts.length > 1) {
          throw new Error('只能设置一个默认联系人');
        }
      }

      // 使用事务更新客户及关联数据
      console.log('开始执行数据库更新事务...');
      console.log('准备更新的新增字段:', {
        orderAccount: customerDto.orderAccount,
        orderPlatformId: customerDto.orderPlatformId,
        customerLevel: customerDto.customerLevel,
        enterpriseNature: customerDto.enterpriseNature
      });

      const updatedCustomer = await this.prisma.$transaction(async (prisma) => {
        // 更新客户基本信息
        console.log('更新客户基本信息...');
        const customer = await prisma.crmCustomer.update({
          where: { id: BigInt(customerId) },
          data: {
            customerName: customerDto.customerName,
            customerAlias: customerDto.customerAlias,
            parentGroup: customerDto.parentGroup,
            source: customerDto.source,
            industry: customerDto.industry,
            status: customerDto.status,
            companyScale: customerDto.companyScale,
            provinceId: customerDto.provinceId,
            cityId: customerDto.cityId,
            districtId: customerDto.districtId,
            detailAddress: customerDto.detailAddress,
            remark: customerDto.remark,

            // 新增字段
            orderAccount: customerDto.orderAccount,
            orderPlatformId: customerDto.orderPlatformId ? BigInt(customerDto.orderPlatformId) : null,
            customerLevel: customerDto.customerLevel,
            enterpriseNature: customerDto.enterpriseNature,
            businessScope: customerDto.businessScope,
            topThreePeers: customerDto.topThreePeers,
            quotationNotes: customerDto.quotationNotes,
            listingNotes: customerDto.listingNotes,
            shippingNotes: customerDto.shippingNotes,

            salespersonId: customerDto.salesperson || null,
            updatedBy: BigInt(userId)
          }
        });

        // 处理财务信息
        if (customerDto.financeInfo) {
          await prisma.crmCustomerFinanceInfo.upsert({
            where: { customerId: BigInt(customerId) },
            update: {
              accountHolderName: customerDto.financeInfo.accountHolderName,
              bankName: customerDto.financeInfo.bankName,
              bankAccount: customerDto.financeInfo.bankAccount,
              accountPeriodBasis: customerDto.financeInfo.accountPeriodBasis,
              settlementMethod: customerDto.financeInfo.settlementMethod,
              settlementDate: customerDto.financeInfo.settlementDate,
              updatedAt: BigInt(Date.now()),
              updatedBy: BigInt(userId)
            },
            create: {
              id: generateSnowflakeId(),
              customerId: BigInt(customerId),
              accountHolderName: customerDto.financeInfo.accountHolderName,
              bankName: customerDto.financeInfo.bankName,
              bankAccount: customerDto.financeInfo.bankAccount,
              accountPeriodBasis: customerDto.financeInfo.accountPeriodBasis,
              settlementMethod: customerDto.financeInfo.settlementMethod,
              settlementDate: customerDto.financeInfo.settlementDate,
              createdAt: BigInt(Date.now()),
              updatedAt: BigInt(Date.now()),
              createdBy: BigInt(userId),
              updatedBy: BigInt(userId)
            }
          });
        }

        // 处理开票信息 - 先删除旧的，再创建新的
        await prisma.crmCustomerInvoiceInfo.deleteMany({
          where: { customerId: BigInt(customerId) }
        });

        if (customerDto.invoiceInfos && customerDto.invoiceInfos.length > 0) {
          for (const invoiceInfo of customerDto.invoiceInfos) {
            await prisma.crmCustomerInvoiceInfo.create({
              data: {
                id: generateSnowflakeId(),
                customerId: BigInt(customerId),
                invoiceTitle: invoiceInfo.invoiceTitle,
                taxNumber: invoiceInfo.taxNumber,
                companyAddress: invoiceInfo.companyAddress,
                companyPhone: invoiceInfo.companyPhone,
                bankName: invoiceInfo.bankName,
                bankAccount: invoiceInfo.bankAccount,

                // 新增字段
                invoiceAddress: invoiceInfo.invoiceAddress,
                invoicePhone: invoiceInfo.invoicePhone,
                createdAt: BigInt(Date.now()),
                updatedAt: BigInt(Date.now()),
                createdBy: BigInt(userId),
                updatedBy: BigInt(userId)
              }
            });
          }
        }

        // 处理联系人信息 - 先删除旧的，再创建新的
        await prisma.crmCustomerContact.deleteMany({
          where: { customerId: BigInt(customerId) }
        });

        if (customerDto.contacts && customerDto.contacts.length > 0) {
          for (const contact of customerDto.contacts) {
            await prisma.crmCustomerContact.create({
              data: {
                id: generateSnowflakeId(),
                customerId: BigInt(customerId),
                contactName: contact.contactName,
                contactPhone: contact.contactPhone,
                wechatId: contact.wechatId,
                email: contact.email,
                department: contact.department,
                position: contact.position,
                birthday: contact.birthday ? BigInt(contact.birthday) : null,
                provinceId: contact.provinceId,
                cityId: contact.cityId,
                districtId: contact.districtId,
                detailAddress: contact.detailAddress,
                isDefault: contact.isDefault,
                remark: contact.remark,
                createdAt: BigInt(Date.now()),
                updatedAt: BigInt(Date.now()),
                createdBy: BigInt(userId),
                updatedBy: BigInt(userId)
              }
            });
          }
        }

        // 处理附件信息 - 先删除旧的，再创建新的
        await prisma.crmCustomerAttachment.deleteMany({
          where: { customerId: BigInt(customerId) }
        });

        if (customerDto.attachments && customerDto.attachments.length > 0) {
          for (const attachment of customerDto.attachments) {
            // 现在attachment是CreateCustomerAttachmentDto对象
            if (attachment.fileUrl) {
              await prisma.crmCustomerAttachment.create({
                data: {
                  id: generateSnowflakeId(),
                  customerId: BigInt(customerId),
                  fileName: attachment.fileName || '',
                  fileUrl: attachment.fileUrl,
                  fileSize: attachment.fileSize,
                  fileType: attachment.fileType,
                  createdAt: BigInt(Date.now()),
                  updatedAt: BigInt(Date.now()),
                  createdBy: BigInt(userId),
                  updatedBy: BigInt(userId)
                }
              });
            }
          }
        }

        return customer;
      });

      console.log('✅ 客户更新事务完成!');

      // 获取更新后的完整客户信息
      console.log('获取更新后的完整客户信息...');
      const result = await this.getCustomerById(customerId);
      console.log('✅ 客户更新成功，返回完整信息');
      return result;
    } catch (error) {
      console.error('❌ 更新客户失败:');
      console.error('错误类型:', error.constructor.name);
      console.error('错误消息:', error.message);
      console.error('错误堆栈:', error.stack);

      // 如果是Prisma错误，打印更详细的信息
      if (error.code) {
        console.error('Prisma错误代码:', error.code);
      }
      if (error.meta) {
        console.error('Prisma错误元数据:', JSON.stringify(error.meta, null, 2));
      }

      throw new Error(`更新客户失败: ${error.message}`);
    }
  }

  /**
   * 删除客户（软删除）
   * @param {string} customerId 客户ID
   * @param {string} userId 删除用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteCustomer(customerId, userId) {
    try {
      // 检查客户是否存在
      const existingCustomer = await this.prisma.crmCustomer.findUnique({
        where: { id: BigInt(customerId) }
      });

      if (!existingCustomer) {
        throw new Error('客户不存在');
      }

      // 检查客户是否已被删除
      if (existingCustomer.deletedAt) {
        throw new Error('客户已被删除');
      }

      // 软删除客户（设置deletedAt字段）
      const deletedCustomer = await this.prisma.crmCustomer.update({
        where: { id: BigInt(customerId) },
        data: {
          deletedAt: new Date(),
          updatedBy: userId && userId !== 'system' ? BigInt(userId) : null
        }
      });

      return handleBigInt(deletedCustomer);
    } catch (error) {
      throw new Error(`删除客户失败: ${error.message}`);
    }
  }

  /**
   * 批量删除客户（软删除）
   * @param {Array<string>} customerIds 客户ID数组
   * @param {string} userId 删除用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async batchDeleteCustomers(customerIds, userId) {
    try {
      if (!customerIds || !Array.isArray(customerIds) || customerIds.length === 0) {
        throw new Error('客户ID列表不能为空');
      }

      // 检查所有客户是否存在
      const existingCustomers = await this.prisma.crmCustomer.findMany({
        where: {
          id: { in: customerIds.map(id => BigInt(id)) },
          deletedAt: null // 只查询未删除的客户
        }
      });

      if (existingCustomers.length !== customerIds.length) {
        throw new Error('部分客户不存在或已被删除');
      }

      // 批量软删除
      const result = await this.prisma.crmCustomer.updateMany({
        where: {
          id: { in: customerIds.map(id => BigInt(id)) },
          deletedAt: null
        },
        data: {
          deletedAt: new Date(),
          updatedBy: userId && userId !== 'system' ? BigInt(userId) : null
        }
      });

      return {
        deletedCount: result.count,
        message: `成功删除 ${result.count} 个客户`
      };
    } catch (error) {
      throw new Error(`批量删除客户失败: ${error.message}`);
    }
  }

  /**
   * 检查客户名称是否存在
   * @param {string} customerName 客户名称
   * @param {string} excludeId 排除的客户ID（编辑时使用）
   * @returns {Promise<boolean>} 是否存在
   */
  async checkCustomerNameExists(customerName, excludeId = null) {
    try {
      const where = {
        customerName: customerName,
        deletedAt: null // 只检查未删除的客户
      };

      // 如果提供了excludeId，则排除该客户（用于编辑时检查）
      if (excludeId) {
        where.id = {
          not: BigInt(excludeId)
        };
      }

      const existingCustomer = await this.prisma.crmCustomer.findFirst({
        where
      });

      return !!existingCustomer;
    } catch (error) {
      throw new Error(`检查客户名称失败: ${error.message}`);
    }
  }

  /**
   * 获取客户枚举选项
   * @returns {Promise<Object>} 枚举选项
   */
  async getCustomerEnumOptions() {
    try {
      const { CustomerStatusEnum, CustomerStatusDescriptions } = require('../constants/CustomerStatusEnum');
      const { CustomerIndustryEnum, CustomerIndustryDescriptions } = require('../constants/CustomerIndustryEnum');
      const { CustomerSourceEnum, CustomerSourceDescriptions } = require('../constants/CustomerSourceEnum');
      const { CustomerLevelEnum, CustomerLevelDescriptions } = require('../constants/CustomerLevelEnum');
      const { EnterpriseNatureEnum, EnterpriseNatureDescriptions } = require('../constants/EnterpriseNatureEnum');

      return {
        statusOptions: Object.entries(CustomerStatusDescriptions).map(([value, label]) => ({
          label,
          value: parseInt(value)
        })),
        industryOptions: Object.entries(CustomerIndustryDescriptions).map(([value, label]) => ({
          label,
          value: parseInt(value)
        })),
        sourceOptions: Object.entries(CustomerSourceDescriptions).map(([value, label]) => ({
          label,
          value: parseInt(value)
        })),
        customerLevelOptions: Object.entries(CustomerLevelDescriptions).map(([value, label]) => ({
          label,
          value: parseInt(value)
        })),
        enterpriseNatureOptions: Object.entries(EnterpriseNatureDescriptions).map(([value, label]) => ({
          label,
          value: parseInt(value)
        }))
      };
    } catch (error) {
      throw new Error(`获取客户枚举选项失败: ${error.message}`);
    }
  }

  /**
   * 获取客户选项列表（用于下拉选择）
   * @param {Object} params 查询参数
   * @param {string} params.keyword 搜索关键词
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise<Object>} 客户选项列表
   */
  async getCustomerOptions(params = {}) {
    try {
      const { keyword = '', page = 1, pageSize = 50 } = params;
      const offset = (page - 1) * pageSize;

      // 使用原生SQL查询，因为Prisma模型映射可能有问题
      // 只过滤软删除，不过滤状态
      let whereClause = 'deleted_at IS NULL';
      let queryParams = [];

      if (keyword) {
        whereClause += ' AND (customer_name ILIKE $1 OR customer_alias ILIKE $2)';
        queryParams = [`%${keyword}%`, `%${keyword}%`];
      }

      // 查询总数
      const countQuery = `SELECT COUNT(*) as count FROM crm.customers WHERE ${whereClause}`;
      const countResult = await this.prisma.$queryRawUnsafe(countQuery, ...queryParams);
      const total = parseInt(countResult[0].count);

      // 查询数据
      const dataQuery = `
        SELECT id, customer_name, customer_alias, status
        FROM crm.customers
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
      `;
      const customers = await this.prisma.$queryRawUnsafe(dataQuery, ...queryParams, pageSize, offset);

      return {
        list: customers.map(customer => ({
          id: customer.id.toString(),
          customerName: customer.customer_name,
          customerAlias: customer.customer_alias,
          status: customer.status
        })),
        pagination: {
          page,
          pageSize,
          total
        }
      };
    } catch (error) {
      throw new Error(`获取客户选项失败: ${error.message}`);
    }
  }

  /**
   * 根据客户ID获取客户联系人列表
   * @param {string} customerId 客户ID
   * @returns {Promise<Array>} 客户联系人列表
   */
  async getCustomerContacts(customerId) {
    try {
      // 使用原生SQL查询联系人
      const contacts = await this.prisma.$queryRawUnsafe(`
        SELECT id, contact_name, contact_phone, email, department, position,
               province_id, city_id, district_id, detail_address, is_default
        FROM crm.customer_contacts
        WHERE customer_id = $1 AND deleted_at IS NULL
        ORDER BY is_default DESC, created_at DESC
      `, BigInt(customerId));

      return contacts.map(contact => {
        // 将省市区字段组合为location数组
        const location = [];
        if (contact.province_id) location.push(contact.province_id);
        if (contact.city_id) location.push(contact.city_id);
        if (contact.district_id) location.push(contact.district_id);

        return {
          id: contact.id.toString(),
          contactName: contact.contact_name,
          contactPhone: contact.contact_phone,
          email: contact.email,
          department: contact.department,
          position: contact.position,
          provinceId: contact.province_id,
          cityId: contact.city_id,
          districtId: contact.district_id,
          location: location.length > 0 ? location : null,
          detailAddress: contact.detail_address,
          isDefault: contact.is_default
        };
      });
    } catch (error) {
      throw new Error(`获取客户联系人失败: ${error.message}`);
    }
  }

  /**
   * 搜索联系人（包含客户信息）
   * @param {Object} params 查询参数
   * @param {string} params.keyword 搜索关键词
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise<Array>} 联系人列表（包含客户信息）
   */
  async searchContacts(params = {}) {
    try {
      const { keyword = '', page = 1, pageSize = 50 } = params;

      // 构建查询条件
      // c.status = 1 表示在营状态
      let whereClause = 'cc.deleted_at IS NULL AND c.deleted_at IS NULL AND c.status = 1';
      let queryParams = [];

      if (keyword) {
        whereClause += ' AND (cc.contact_name ILIKE $1 OR cc.contact_phone ILIKE $1 OR c.customer_name ILIKE $1)';
        queryParams = [`%${keyword}%`];
      }

      // 联表查询联系人和客户信息
      const offset = (page - 1) * pageSize;
      const dataQuery = `
        SELECT
          cc.id as contact_id,
          cc.contact_name,
          cc.contact_phone,
          cc.email,
          cc.department,
          cc.position,
          cc.province_id,
          cc.city_id,
          cc.district_id,
          cc.detail_address,
          cc.is_default,
          cc.customer_id,
          c.customer_name,
          c.customer_alias
        FROM crm.customer_contacts cc
        LEFT JOIN crm.customers c ON cc.customer_id = c.id
        WHERE ${whereClause}
        ORDER BY cc.is_default DESC, cc.created_at DESC
        LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
      `;

      const contacts = await this.prisma.$queryRawUnsafe(dataQuery, ...queryParams, pageSize, offset);

      return contacts.map(contact => {
        // 将省市区字段组合为location数组
        const location = [];
        if (contact.province_id) location.push(contact.province_id);
        if (contact.city_id) location.push(contact.city_id);
        if (contact.district_id) location.push(contact.district_id);

        return {
          id: contact.contact_id.toString(),
          contactName: contact.contact_name,
          contactPhone: contact.contact_phone,
          email: contact.email,
          department: contact.department,
          position: contact.position,
          provinceId: contact.province_id,
          cityId: contact.city_id,
          districtId: contact.district_id,
          location: location.length > 0 ? location : null,
          detailAddress: contact.detail_address,
          isDefault: contact.is_default,
          // 客户信息
          customerId: contact.customer_id.toString(),
          customerName: contact.customer_name,
          customerAlias: contact.customer_alias,
          // 显示名称：联系人姓名 - 客户名称
          displayName: `${contact.contact_name} - ${contact.customer_name}${contact.customer_alias ? `(${contact.customer_alias})` : ''}`
        };
      });
    } catch (error) {
      throw new Error(`搜索联系人失败: ${error.message}`);
    }
  }

  /**
   * 根据客户ID获取客户详细信息（包含联系人和地址）
   * @param {string} customerId 客户ID
   * @returns {Promise<Object>} 客户详细信息
   */
  async getCustomerDetailWithContacts(customerId) {
    try {
      // 查询客户基本信息
      const customerResult = await this.prisma.$queryRawUnsafe(`
        SELECT id, customer_name, customer_alias, status, salesperson_id,
               province_id, city_id, district_id
        FROM crm.customers
        WHERE id = $1 AND deleted_at IS NULL
      `, BigInt(customerId));

      if (customerResult.length === 0) {
        return null;
      }

      const customer = customerResult[0];

      // 查询客户联系人
      const contacts = await this.prisma.$queryRawUnsafe(`
        SELECT id, contact_name, contact_phone, email, department, position,
               province_id, city_id, district_id, detail_address, is_default
        FROM crm.customer_contacts
        WHERE customer_id = $1 AND deleted_at IS NULL
        ORDER BY is_default DESC, created_at DESC
      `, BigInt(customerId));

      // 查询业务员信息
      let salespersons = [];
      if (customer.salesperson_id) {
        try {
          // 解析业务员ID字符串为数组
          const salespersonIds = customer.salesperson_id.split(',').filter(id => id.trim());
          const salespersonIdsBigInt = salespersonIds.map(id => BigInt(id.trim()));

          if (salespersonIdsBigInt.length > 0) {
            const salespersonResults = await this.prisma.baseSystemUser.findMany({
              where: {
                id: { in: salespersonIdsBigInt }
              },
              select: {
                id: true,
                username: true,
                nickname: true
              }
            });

            // 按照原始ID顺序重新排序业务员数组
            salespersons = salespersonIds.map(originalId => {
              return salespersonResults.find(sp => sp.id.toString() === originalId);
            }).filter(Boolean).map(sp => ({
              id: sp.id.toString(),
              username: sp.username,
              nickname: sp.nickname
            })); // 过滤掉未找到的业务员并转换ID为字符串
          }
        } catch (error) {
          console.error('查询业务员信息失败:', error);
          salespersons = [];
        }
      }

      // 将客户省市区字段组合为companyRegion数组
      const companyRegion = [];
      if (customer.province_id) companyRegion.push(customer.province_id);
      if (customer.city_id) companyRegion.push(customer.city_id);
      if (customer.district_id) companyRegion.push(customer.district_id);

      return {
        id: customer.id.toString(),
        customerName: customer.customer_name,
        customerAlias: customer.customer_alias,
        status: customer.status,
        provinceId: customer.province_id,
        cityId: customer.city_id,
        districtId: customer.district_id,
        companyRegion: companyRegion.length > 0 ? companyRegion : null,
        salespersons: salespersons,
        contacts: contacts.map(contact => {
          // 将省市区字段组合为location数组
          const location = [];
          if (contact.province_id) location.push(contact.province_id);
          if (contact.city_id) location.push(contact.city_id);
          if (contact.district_id) location.push(contact.district_id);

          return {
            id: contact.id.toString(),
            contactName: contact.contact_name,
            contactPhone: contact.contact_phone,
            email: contact.email,
            department: contact.department,
            position: contact.position,
            provinceId: contact.province_id,
            cityId: contact.city_id,
            districtId: contact.district_id,
            location: location.length > 0 ? location : null,
            detailAddress: contact.detail_address,
            isDefault: contact.is_default
          };
        })
      };
    } catch (error) {
      throw new Error(`获取客户详细信息失败: ${error.message}`);
    }
  }
}

module.exports = CustomerService;
