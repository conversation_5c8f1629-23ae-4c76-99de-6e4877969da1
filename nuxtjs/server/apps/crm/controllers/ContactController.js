const ContactService = require('../services/ContactService');
const { ContactQueryDto, BindCustomerDto, CreateContactDto } = require('../dto/CustomerDto');

/**
 * 联系人管理控制器
 */
class ContactController {
  constructor(prisma) {
    this.contactService = new ContactService(prisma);
  }

  /**
   * 新增联系人
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createContact(req, res) {
    try {
      const createDto = new CreateContactDto(req.body);

      // 验证必填字段
      if (!createDto.contactName) {
        return res.status(200).json({
          code: 400,
          message: '联系人姓名不能为空',
          data: null
        });
      }

      if (!createDto.contactPhone) {
        return res.status(200).json({
          code: 400,
          message: '联系电话不能为空',
          data: null
        });
      }

      // 获取用户ID（从请求头或会话中获取）
      const userId = req.headers['user-id'] || req.user?.id || 'system';

      const result = await this.contactService.createContact(createDto, userId);

      res.status(200).json({
        code: 200,
        message: '新增成功',
        data: result
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }

  /**
   * 获取联系人列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getContactList(req, res) {
    try {
      const queryDto = new ContactQueryDto(req.query);
      const result = await this.contactService.getContactList(queryDto);

      res.status(200).json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }

  /**
   * 根据ID获取联系人详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getContactById(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(200).json({
          code: 400,
          message: '联系人ID不能为空',
          data: null
        });
      }

      const contact = await this.contactService.getContactById(id);

      res.status(200).json({
        code: 200,
        message: '获取成功',
        data: contact
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }

  /**
   * 更新联系人信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateContact(req, res) {
    try {
      const contactId = req.params.id;
      if (!contactId) {
        return res.status(200).json({
          code: 400,
          message: '联系人ID不能为空',
          data: null
        });
      }

      // 验证必填字段
      const { contactName, contactPhone } = req.body;
      if (!contactName || !contactPhone) {
        return res.status(200).json({
          code: 400,
          message: '联系人姓名和电话不能为空',
          data: null
        });
      }

      // 创建更新DTO
      const { UpdateContactDto } = require('../dto/CustomerDto');
      const updateDto = new UpdateContactDto({
        id: contactId,
        ...req.body
      });

      // 调用服务层更新联系人
      const result = await this.contactService.updateContact(contactId, updateDto, req.user?.id);

      res.json({
        code: 200,
        message: '更新成功',
        data: result
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }

  /**
   * 绑定联系人到客户
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async bindCustomer(req, res) {
    try {
      const bindDto = new BindCustomerDto(req.body);
      
      // 验证必填字段
      if (!bindDto.contactId) {
        return res.status(200).json({
          code: 400,
          message: '联系人ID不能为空',
          data: null
        });
      }

      if (!bindDto.customerId) {
        return res.status(200).json({
          code: 400,
          message: '客户ID不能为空',
          data: null
        });
      }

      // 获取用户ID（从请求头或会话中获取）
      const userId = req.headers['user-id'] || req.user?.id || 'system';

      const result = await this.contactService.bindCustomer(
        bindDto.contactId,
        bindDto.customerId,
        userId
      );
      res.status(200).json({
        code: 200,
        message: '绑定成功',
        data: result
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }

  /**
   * 解绑联系人与客户的关联
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async unbindCustomer(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(200).json({
          code: 400,
          message: '联系人ID不能为空',
          data: null
        });
      }

      // 获取用户ID（从请求头或会话中获取）
      const userId = req.headers['user-id'] || req.user?.id || 'system';
      const result = await this.contactService.unbindCustomer(id, userId);
      res.status(200).json({
        code: 200,
        message: '解绑成功',
        data: result
      });
    } catch (error) {
      res.status(200).json({
        code: 400,
        message: error.message,
        data: null
      });
    }
  }
}

module.exports = ContactController;
