<template>
  <a-drawer
    v-model:visible="visible"
    title="编辑在售平台"
    width="800px"
    placement="right"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 20 }"
    >
      <a-form-item label="SKU信息" field="skuInfo">
        <div class="sku-info">
          <div class="sku-name">{{ skuData?.skuName || "未知SKU" }}</div>
          <div class="sku-id">ID: {{ skuData?.id }}</div>
        </div>
      </a-form-item>

      <a-form-item label="在售平台" field="channels">
        <div class="channels-list">
          <div
            v-for="(channel, index) in formData.channels"
            :key="channel.id || index"
            class="channel-item"
          >
            <div class="channel-info">
              <a-checkbox
                v-model="channel.isEnabled"
                @change="handleChannelToggle(channel, $event)"
              >
                <div class="channel-content">
                  <i
                    v-if="channel.channelIcon"
                    :class="[channel.channelIcon, 'iconfont', 'channel-icon']"
                  ></i>
                  <span class="channel-name">{{ channel.channelName }}</span>
                </div>
              </a-checkbox>
            </div>

            <!-- 第三方SKU ID输入框 - 下一行显示 -->
            <div v-if="channel.isEnabled" class="sku-code-row">
              <span class="sku-code-label">
                第三方SKU ID：
                <span class="required-mark">*</span>
              </span>
              <a-input
                v-model="channel.thirdPartySkuId"
                placeholder="请输入第三方SKU ID（必填）"
                allow-clear
                size="small"
                class="sku-code-field"
                :class="{
                  error:
                    !channel.thirdPartySkuId ||
                    channel.thirdPartySkuId.trim() === '',
                }"
              />
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="loading" @click="handleSubmit"
            >保存</a-button
          >
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import goodsApi from "@/api/master/goods";
import orderApi from "@/api/master/order";

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref();
const skuData = ref(null);
const allChannels = ref([]);

// 表单数据
const formData = reactive({
  channels: [],
});

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
  }
);

watch(visible, (newVal) => {
  emit("update:modelValue", newVal);
});

// 获取所有渠道列表
const getAllChannels = async () => {
  try {
    const response = await orderApi.getChannelList({ page: 1, pageSize: 100 });
    if (response.code === 200) {
      allChannels.value = response.data.items || [];
    }
  } catch (error) {
    console.error("获取渠道列表失败:", error);
    Message.error("获取渠道列表失败");
  }
};

// 初始化表单数据
const initFormData = (sku) => {
  console.log("sku: ", sku);
  skuData.value = sku;

  // 创建渠道映射
  const existingChannels = new Map();
  if (sku.channels && Array.isArray(sku.channels)) {
    sku.channels.forEach((channel) => {
      existingChannels.set(channel.channelId, {
        id: channel.id,
        channelId: channel.channelId,
        channelName: channel.channelName,
        channelIcon: channel.channelIcon,
        thirdPartySkuId: channel.thirdPartySkuId || "",
        isEnabled: channel.isEnabled === 1,
      });
    });
  }

  // 合并所有渠道数据
  formData.channels = allChannels.value.map((channel) => {
    const existing = existingChannels.get(channel.id);
    return (
      existing || {
        id: null,
        channelId: channel.id,
        channelName: channel.name,
        channelIcon: channel.iconUrl,
        thirdPartySkuId: "",
        isEnabled: false,
      }
    );
  });
};

// 处理渠道启用/禁用
const handleChannelToggle = (channel, enabled) => {
  channel.isEnabled = enabled;
};

// 打开弹窗
const open = async (sku) => {
  await getAllChannels();
  initFormData(sku);
  visible.value = true;
};

// 关闭弹窗
const handleCancel = () => {
  visible.value = false;
  formData.channels = [];
  skuData.value = null;
};

// 提交表单
const handleSubmit = async () => {
  loading.value = true;

  // 获取已启用的渠道
  const enabledChannels = formData.channels.filter(
    (channel) => channel.isEnabled
  );

  // 验证勾选的平台必须填写第三方SKU ID
  const invalidChannels = enabledChannels.filter(
    (channel) =>
      !channel.thirdPartySkuId || channel.thirdPartySkuId.trim() === ""
  );

  if (invalidChannels.length > 0) {
    console.log("invalidChannels: ", invalidChannels);
    const channelNames = invalidChannels
      .map((channel) => channel.channelName)
      .join("、");
    Message.error(`请填写以下平台的第三方SKU ID：${channelNames}`);
    loading.value = false;
    return; // 停止执行，不继续提交
  }

  // 准备提交数据 - 构建完整的SKU数据结构
  const channelsData = enabledChannels.map((channel) => ({
    id: channel.id,
    channelId: channel.channelId,
    channelName: channel.channelName,
    channelIcon: channel.channelIcon,
    thirdPartySkuId: channel.thirdPartySkuId.trim(),
    isEnabled: 1,
  }));

  try {
    // 调用SKU渠道更新API
    const response = await goodsApi.sku.updateChannels({
      skuId: skuData.value.id,
      channels: channelsData,
    });

    if (response.code === 200) {
      Message.success("在售平台更新成功");
      emit("success");
      handleCancel();
    } else {
      Message.error(response.message || "更新失败");
    }
  } catch (error) {
    console.error("更新SKU渠道失败:", error);
    Message.error("更新失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  open,
});
</script>

<style scoped>
.sku-info {
  padding: 16px;
  background: linear-gradient(135deg, #f7f8fa 0%, #f2f3f5 100%);
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  margin-bottom: 8px;
  width: 100%;
}

.sku-name {
  font-weight: 600;
  font-size: 15px;
  color: #1d2129;
  margin-bottom: 6px;
  line-height: 1.4;
}

.sku-id {
  font-size: 12px;
  color: #86909c;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

.channels-list {
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  /* max-height: 450px; */
  overflow-y: auto;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  width: 100%;
}

.channel-item {
  border-bottom: 1px solid #f2f3f5;
  padding: 18px 20px;
  transition: all 0.2s ease;
  position: relative;
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-item:hover {
  background: #f8f9fa;
}

.channel-info {
  margin-bottom: 8px;
}

.channel-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.channel-icon {
  font-size: 24px;
  color: #165dff;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: rgba(22, 93, 255, 0.1);
  transition: all 0.2s ease;
}

.channel-item:hover .channel-icon {
  background: rgba(22, 93, 255, 0.15);
  transform: scale(1.05);
}

.channel-name {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  flex: 1;
}

.sku-code-row {
  display: flex;
  align-items: center;
  gap: 12px;
  /* margin-left: 44px; */
  margin-bottom: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f2f3f5;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sku-code-label {
  font-size: 13px;
  color: #4e5969;
  white-space: nowrap;
  font-weight: 500;
  min-width: 120px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.required-mark {
  color: #f53f3f;
  font-weight: 600;
  font-size: 14px;
}

.sku-code-field {
  flex: 1;
  /* max-width: 300px; */
}

/* .sku-code-field :deep(.arco-input) {
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  font-size: 13px;
  height: 32px;
  transition: all 0.2s ease;
  background: #fff;
}

.sku-code-field :deep(.arco-input:focus) {
  border-color: #165dff;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
} */

/* 错误状态样式 */
.sku-code-field.error :deep(.arco-input) {
  border-color: #f53f3f !important;
  background-color: #fff2f0;
}

.sku-code-field.error :deep(.arco-input:focus) {
  border-color: #f53f3f !important;
  box-shadow: 0 0 0 2px rgba(245, 63, 63, 0.1) !important;
}

.drawer-footer {
  text-align: right;
  margin: 0;
}

.drawer-footer .arco-btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 20px;
  height: auto;
}

.drawer-footer .arco-btn-primary {
  background: linear-gradient(135deg, #165dff 0%, #246fff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
}

.drawer-footer .arco-btn-primary:hover {
  background: linear-gradient(135deg, #0e4fd1 0%, #1c5cff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 93, 255, 0.4);
}

/* 自定义复选框样式 */
:deep(.arco-checkbox) {
  margin-right: 0;
}

:deep(.arco-checkbox-icon) {
  border-radius: 4px;
  border-width: 2px;
  transition: all 0.2s ease;
}

:deep(.arco-checkbox:hover .arco-checkbox-icon) {
  border-color: #165dff;
}

:deep(.arco-checkbox-checked .arco-checkbox-icon) {
  background: linear-gradient(135deg, #165dff 0%, #246fff 100%);
  border-color: #165dff;
}

/* 滚动条样式 */
.channels-list::-webkit-scrollbar {
  width: 6px;
}

.channels-list::-webkit-scrollbar-track {
  background: #f2f3f5;
  border-radius: 3px;
}

.channels-list::-webkit-scrollbar-thumb {
  background: #c9cdd4;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.channels-list::-webkit-scrollbar-thumb:hover {
  background: #a9aeb8;
}
</style>
