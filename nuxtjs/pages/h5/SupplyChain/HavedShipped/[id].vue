<template>
    <div class="haved-shipped-page">
      <!-- 固定顶部日期 -->
      <a-affix :offset-top="0" class="fixed-header">
        <div class="date-header">
          {{ formatDate(orderData.createTime) }}
        </div>
      </a-affix>

      <!-- 页面内容 -->
      <div class="content">
        <!-- 订单号 -->
        <div class="order-number">
          订单号: {{ orderData.orderNo }}
        </div>

        <!-- 收货信息 -->
        <div class="section">
          <h3 class="section-title">收货信息:</h3>
          <div class="info-item">
            <span class="label">收货人:</span>
            <span class="value">{{ orderData.receiverName }}</span>
          </div>
          <div class="info-item">
            <span class="label">配送地址:</span>
            <span class="value">{{ orderData.receiverAddress }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货电话:</span>
            <span class="value">{{ orderData.receiverPhone }}</span>
          </div>
        </div>
        

        <!-- 订单商品 -->
        <div class="section">
          <h3 class="section-title">订单商品:</h3>
          <div class="product-info">
            <div class="info-item clickable" @click="goToProductDetails">
              <span class="label">分类:</span>
              <span class="value link-text">{{ orderData.productCategory }}</span>
            </div>
            <div class="info-item clickable" @click="goToProductDetails">
              <span class="label">商品编号:</span>
              <span class="value link-text">{{ orderData.productCode }}</span>
            </div>
            <div class="info-item product-name clickable" @click="goToProductDetails">
              <span class="label">商品名称:</span>
              <span class="value link-text">{{ orderData.productName }}</span>
            </div>
            <div class="info-item">
              <span class="label">数量:</span>
              <span class="value">{{ orderData.quantity }}</span>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-actions">
          <!-- <a-button type="primary" size="large" block class="confirm-btn">
            已发货，点击查看发货详情
          </a-button> -->
           <a-button type="primary" size="large" block class="confirm-btn" @click="jump">
            去发货
          </a-button>
        </div>
      </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
definePageMeta({
  layout: 'false',
  name: 'h5-SupplyChain-HavedShipped',
  path: '/h5/SupplyChain/HavedShipped/:id'
})
// 路由实例
const router = useRouter()
const route = useRoute()
const orderId = route.params.id

// 订单数据
const orderData = ref({
  createTime: '2024-8-26 16:21:08',
  orderNo: '294544954069',
  receiverName: 'test',
  receiverAddress: '配送地址广东广州市番禺区test3-46666',
  receiverPhone: '13244446666',
  productId: '1', // 产品ID，用于跳转
  productCategory: '模拟器材',
  productCode: '100078596122',
  productName: '灵龙八方 地形骑游沙盘模型制作建筑高倍准定位地标划分沙盘 (一平方)',
  quantity: '2'
})

// 格式化日期
const formatDate = (dateStr) => {
  return dateStr || new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

const jump = () => {
  router.push({ path: `/h5/SupplyChain/SendGoods`})
}

// 跳转到产品详情页面
const goToProductDetails = () => {
  router.push({ path: `/h5/SupplyChain/ProductDetails/${orderData.value.productId}` })
}

onMounted(() => {
  // 这里可以根据 orderId 获取实际的订单数据
  console.log('订单ID:', orderId)
  // 实际项目中可以调用API获取订单详情
  // fetchOrderData(orderId)
})
</script>
<style lang="less">
.haved-shipped-page {
  background: #fff;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;

  // 固定头部样式
  .fixed-header {
    z-index: 100;

    .date-header {
      background: #fff;
      padding: 12px 16px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 页面内容
  .content {
    background: #fff;
    padding: 16px;
    // 订单号
    .order-number {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 24px;
      text-align: center;
    }

    // 区块样式
    .section {
      margin-bottom: 32px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    // 信息项样式
    .info-item {
      display: flex;
      margin-bottom: 12px;
      line-height: 1.6;

      .label {
        color: #666;
        font-size: 14px;
        min-width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        flex: 1;
        word-break: break-all;
      }

      &.clickable {
        cursor: pointer;
        padding: 8px;
        margin: -8px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background-color: #f5f5f5;

          .value.link-text {
            color: #1890ff;
          }
        }

        .value.link-text {
          color: inherit;
          text-decoration: none;
          transition: color 0.2s;
        }
      }

      // 商品名称特殊样式
      &.product-name {
        align-items: flex-start;

        .value {
          line-height: 1.5;
        }
      }
    }

    // 底部按钮
    .bottom-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0;
      background: #1989FA;
      z-index: 99;

      .confirm-btn {
        height: 60px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 0;
        background: #1989FA;
        border: none;
        color: #fff;
        width: 100%;

        &:hover {
          background: #0F7AE5;
        }

        &:focus {
          background: #1989FA;
        }
      }
    }
  }
}

// 响应式设计
@media (min-width: 500px) {
  .haved-shipped-page {
    margin: 0 auto;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;

    .content .bottom-actions {
      left: 50%;
      transform: translateX(-50%);
      max-width: 500px;
      width: 500px;
    }
  }
}

// 为内容区域添加底部间距，避免被固定按钮遮挡
.content {
  padding-bottom: 80px !important;
}
</style>