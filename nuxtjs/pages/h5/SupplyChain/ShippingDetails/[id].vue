<template>
  <div class="shipping-details-page">
    <!-- 固定顶部日期和返回按钮 -->
    <a-affix :offset-top="0" class="fixed-header">
      <div class="header-bar">
        <a-button type="text" class="back-btn" @click="goBack">
          <icon-left />
        </a-button>
        <div class="date-header">
          发货详情
        </div>
        <div class="placeholder"></div>
      </div>
    </a-affix>

    <!-- 订单号 -->
    <div class="order-info">
      <div class="order-number">
        订单号: {{ orderData.orderNo }}
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content">
      <!-- 发货信息展示 -->
      <div class="section">
        <h3 class="section-title">发货信息:</h3>

        <!-- 配送方式 -->
        <div class="form-item">
          <span class="label">配送方式:</span>
          <div class="input-wrapper">
            <input
              type="text"
              :value="shippingData.deliveryMethod"
              readonly
              class="form-input readonly"
            />
          </div>
        </div>

        <!-- 快递单号 -->
        <div class="form-item">
          <span class="label">快递单号:</span>
          <div class="input-wrapper">
            <input
              type="text"
              :value="shippingData.trackingNumber"
              readonly
              class="form-input readonly"
            />
          </div>
        </div>

        <!-- 发货地 -->
        <div class="form-item">
          <span class="label">发货地:</span>
          <div class="input-wrapper">
            <input
              type="text"
              :value="shippingData.deliveryLocation"
              readonly
              class="form-input readonly"
            />
          </div>
        </div>

        <!-- 配送运单照片 -->
        <div class="form-item photo-item">
          <span class="label">配送运单照片:</span>
          <div class="photo-display">
            <a-image
              v-if="shippingData.waybillPhoto"
              :src="shippingData.waybillPhoto"
              width="100"
              height="100"
              :preview="true"
              class="photo-item-img"
            />
            <div v-else class="no-photo">暂无照片</div>
          </div>
        </div>

        <!-- 包裹货物照片 -->
        <div class="form-item photo-item">
          <span class="label">包裹货物照片:</span>
          <div class="photo-display">
            <a-image
              v-if="shippingData.packagePhoto"
              :src="shippingData.packagePhoto"
              width="100"
              height="100"
              :preview="true"
              class="photo-item-img"
            />
            <div v-else class="no-photo">暂无照片</div>
          </div>
        </div>

        <div style="font-size: 12px; padding: 10px 10px; color: #999;">
          说明：仅支持jpg、png格式，单张大小不超过1M,若为厂直发货，
          需一并上传物流单，若不上传物流单，审核可能不通过，发货不成功!
        </div>

        <!-- 备注 -->
        <div class="form-item">
          <span class="label">备注:</span>
          <div class="input-wrapper">
            <textarea
              :value="shippingData.remark"
              readonly
              class="form-textarea readonly"
              rows="3"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'

definePageMeta({
  layout: 'false',
  name: 'h5-SupplyChain-ShippingDetails',
  path: '/h5/SupplyChain/ShippingDetails/:id'
})

const router = useRouter()
const route = useRoute()

// 获取订单ID
const orderId = route.params.id

// 订单数据
const orderData = ref({
  orderNo: '313300020216'
})

// 发货数据（只读展示）
const shippingData = ref({
  deliveryMethod: '京东快递',
  trackingNumber: 'JD1234567890123',
  deliveryLocation: '广东省深圳市南山区科技园',
  waybillPhoto: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/cd7a1aaea8e1c5e3d26fe2591e561798.png~tplv-uwbnlip3yd-webp.webp',
  packagePhoto: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/6480dbc69be1b5de95010289787d64f1.png~tplv-uwbnlip3yd-webp.webp',
  remark: '货物已按要求包装，请注意查收。如有问题请及时联系客服。'
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面加载时获取发货详情
onMounted(() => {
  // 这里可以根据orderId调用API获取真实的发货数据
  console.log('加载发货详情，订单ID:', orderId)
})
</script>

<style lang="less">
.shipping-details-page {
  background: #fff;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;

  // 固定头部样式
  .fixed-header {
    z-index: 100;

    .header-bar {
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;

      .back-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        font-size: 18px;
        color: #333;
      }

      .date-header {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
        text-align: center;
      }

      .placeholder {
        width: 32px;
      }
    }
  }

  // 订单信息
  .order-info {
    padding: 16px;
    text-align: center;

    .order-number {
      font-size: 14px;
      color: #666;
    }
  }

  // 页面内容
  .content {
    padding: 0 16px 40px;

    // 区块样式
    .section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    // 表单项样式
    .form-item {
      margin-bottom: 16px;

      .label {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .input-wrapper {
        position: relative;

        .form-input {
          width: 100%;
          height: 40px;
          padding: 0 12px;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          font-size: 14px;
          background: #fff;

          &.readonly {
            background: #f5f5f5;
            color: #333;
            cursor: default;
          }
        }

        .form-textarea {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          font-size: 14px;
          resize: none;
          min-height: 80px;

          &.readonly {
            background: #f5f5f5;
            color: #333;
            cursor: default;
          }
        }
      }
    }

    // 照片展示样式
    .photo-item {
      .photo-display {
        display: flex;
        align-items: center;
        gap: 12px;

        .photo-item-img {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
        }

        .no-photo {
          width: 100px;
          height: 100px;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          font-size: 12px;
          background: #fafafa;
        }
      }
    }
  }
}

// 响应式设计
@media (min-width: 500px) {
  .shipping-details-page {
    margin: 0 auto;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
  }
}
</style>