<template>
  <div class="product-details">
    <!-- 顶部导航 -->
   <a-affix :offset-top="0" class="fixed-header">
      <div class="header-bar">
        <a-button type="text" class="back-btn" @click="goBack">
          <icon-left />
        </a-button>
        <div class="date-header">
          商品详情
        </div>
        <div class="placeholder"></div>
      </div>
    </a-affix>

    <!-- 加载状态 -->
    <div v-if="!productData" class="loading-container">
      <a-spin size="large" />
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 产品内容 -->
    <div v-else>
      <!-- 产品图片轮播 -->
      <div class="product-images">
        <div class="image-carousel">
          <div class="carousel-container">
            <div class="carousel-slides" :style="{ transform: `translateX(-${currentImageIndex * 100}%)` }">
              <div v-for="(image, index) in productData.images" :key="index" class="carousel-slide">
                <a-image :src="image" :alt="`${productData.name} - 图片${index + 1}`" class="carousel-image" :preview="true" />
              </div>
            </div>
          </div>
          <!-- 指示器 -->
          <div class="carousel-indicators" v-if="productData.images.length > 1">
            <button
              v-for="(image, index) in productData.images"
              :key="index"
              @click="currentImageIndex = index"
              :class="['indicator', { active: index === currentImageIndex }]"
            ></button>
          </div>
          <!-- 左右箭头 -->
          <div class="carousel-arrows" v-if="productData.images.length > 1">
            <button class="arrow arrow-left" @click="prevImage">‹</button>
            <button class="arrow arrow-right" @click="nextImage">›</button>
          </div>
        </div>
      </div>

      <!-- 产品基本信息 -->
      <div class="product-info">
        <div class="product-name">{{ productData.name }}</div>

        <div class="product-details-grid">
          <div class="detail-row">
            <span class="detail-label">品牌：</span>
            <span class="detail-value">{{ productData.brand }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">分类：</span>
            <span class="detail-value">{{ productData.category }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">商品编号：</span>
            <span class="detail-value">{{ productData.code }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">型号：</span>
            <span class="detail-value">QLK-013</span>
          </div>
        </div>
      </div>

      <!-- 产品详情和规格参数 Tabs -->
      <div class="product-tabs-section">
        <a-tabs :active-key="activeTab" @change="(key) => activeTab = key" position="top" justify>
          <a-tab-pane key="details" title="商品详情">
          <div class="details-content">
            <div class="detail-item">
              <span class="label">商品名称</span>
              <span class="value">{{ productData.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品品牌</span>
              <span class="value">{{ productData.brand }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品分类</span>
              <span class="value">{{ productData.category }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品编号</span>
              <span class="value">{{ productData.code }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品价格</span>
              <span class="value">{{ productData.price }}</span>
            </div>
            <div class="detail-item">
              <span class="label">库存数量</span>
              <span class="value">{{ productData.stock }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品重量</span>
              <span class="value">{{ productData.weight }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品尺寸</span>
              <span class="value">{{ productData.size }}</span>
            </div>
            <div class="detail-item">
              <span class="label">生产厂家</span>
              <span class="value">{{ productData.manufacturer }}</span>
            </div>
            <div class="detail-item">
              <span class="label">产品描述</span>
              <span class="value description">{{ productData.description }}</span>
            </div>
          </div>
          </a-tab-pane>

          <a-tab-pane key="specifications" title="商品属性">
          <div class="specs-content">
            <div class="spec-item" v-for="spec in productData.specifications" :key="spec.name">
              <span class="spec-label">{{ spec.name }}</span>
              <span class="spec-value">{{ spec.value }}</span>
            </div>
          </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'false',
  name: 'h5-SupplyChain-ProductDetails',
  path: '/h5/SupplyChain/ProductDetails/:id'
})
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 获取产品ID
const productId = route.params.id

// 当前激活的 tab
const activeTab = ref('details')

// 当前图片索引
const currentImageIndex = ref(0)

// 产品数据
const productData = ref(null)

// 初始化产品数据
const initProductData = () => {
  productData.value = {
    id: productId,
    name: '艾晨君泰 模拟教学模拟沙训练器材 82-2黑色榴弹雷模型 (50个起订)',
    brand: '艾晨君泰',
    category: '模拟器材',
    code: '100078596122',
    price: '¥45.00',
    stock: '999件',
    weight: '0.5kg',
    size: '10cm x 5cm x 5cm',
    manufacturer: '艾晨君泰科技有限公司',
    description: '高仿真模拟训练器材，适用于军事训练、教学演示等场景。采用优质材料制作，外观逼真，手感真实。',
    images: [
      'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/cd7a1aaea8e1c5e3d26fe2591e561798.png~tplv-uwbnlip3yd-webp.webp',
      'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/6480dbc69be1b5de95010289787d64f1.png~tplv-uwbnlip3yd-webp.webp',
      'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/0265a04fddbd77a19602a15d9d55d797.png~tplv-uwbnlip3yd-webp.webp',
    ],
    specifications: [
      { name: '材质', value: '高强度塑料' },
      { name: '颜色', value: '黑色' },
      { name: '重量', value: '0.5kg' },
      { name: '尺寸', value: '10cm x 5cm x 5cm' },
      { name: '包装', value: '50个/箱' },
      { name: '用途', value: '训练教学' },
      { name: '保质期', value: '5年' },
      { name: '认证', value: 'ISO9001' }
    ]
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 轮播图控制函数
const nextImage = () => {
  if (productData.value && productData.value.images.length > 0) {
    currentImageIndex.value = (currentImageIndex.value + 1) % productData.value.images.length
  }
}

const prevImage = () => {
  if (productData.value && productData.value.images.length > 0) {
    currentImageIndex.value = (currentImageIndex.value - 1 + productData.value.images.length) % productData.value.images.length
  }
}

// 页面加载时获取产品详情
onMounted(() => {
  // 初始化产品数据
  initProductData()
  // 这里可以根据productId调用API获取真实的产品数据
  console.log('加载产品详情，ID:', productId)
})
</script>

<style lang="scss" scoped>
.product-details {
  min-height: 100vh;
  background-color: #f5f5f5;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;

    .loading-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 100;

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      cursor: pointer;
      color: #333;
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-right: 32px; // 平衡返回按钮的宽度
    }
  }

  .product-images {
    // margin-top: 44px;
    background-color: #fff;
    padding: 20px;

    .image-carousel {
      position: relative;
      width: 100%;
      margin: 0 auto;

      .carousel-container {
        overflow: hidden;
        border-radius: 2px;
        position: relative;
        // height: 300px;

        .carousel-slides {
          display: flex;
          transition: transform 0.3s ease-in-out;
          height: 100%;

          .carousel-slide {
            min-width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            :deep(.arco-image) {
              width: 100%;
              height: 100%;

              .arco-image-img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }
          }
        }
      }

      .carousel-indicators {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 16px;

        .indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          border: none;
          background-color: #ddd;
          cursor: pointer;
          transition: background-color 0.3s;

          &.active {
            background-color: #1890ff;
          }
        }
      }

      .carousel-arrows {
        .arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          border: none;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          font-size: 20px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.3s;
          z-index: 10;

          &:hover {
            background-color: rgba(0, 0, 0, 0.7);
          }

          &.arrow-left {
            left: 10px;
          }

          &.arrow-right {
            right: 10px;
          }
        }
      }
    }
  }

  .product-info {
    background-color: #fff;
    padding: 20px;
    margin-top: 8px;

    .product-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      line-height: 1.4;
      margin-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
    }

    .product-details-grid {
      .detail-row {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .detail-label {
          font-size: 14px;
          color: #666;
          min-width: 80px;
          flex-shrink: 0;
          font-weight: 500;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          flex: 1;

          &.price {
            color: #ff4d4f;
            font-size: 16px;
            font-weight: 600;
          }
        }
      }
    }
  }

  .product-tabs-section {
    background-color: #fff;
    margin-top: 8px;

    :deep(.arco-tabs) {
      .arco-tabs-nav-tab {
        padding: 0 20px;

        .arco-tabs-tab {
          font-size: 16px;
          font-weight: 500;

          &.arco-tabs-tab-active {
            color: #1890ff;
          }
        }
      }

      .arco-tabs-content {
        padding: 20px;
      }
    }

    .details-content,
    .specs-content {
      .detail-item,
      .spec-item {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .label,
        .spec-label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value,
        .spec-value {
          color: #333;
          font-size: 14px;
          flex: 1;
          word-break: break-all;

          &.description {
            line-height: 1.6;
          }
        }
      }
    }
  }
}
// 固定头部样式
  .fixed-header {
    z-index: 100;

    .header-bar {
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;

      .back-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        font-size: 18px;
        color: #333;
      }

      .date-header {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
        text-align: center;
      }

      .placeholder {
        width: 32px;
      }
    }
  }

// 响应式设计
@media (min-width: 500px) {
  .product-details {
    margin: 0 auto;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
  }
}
</style>
