<template>
  <div class="min-h-screen bg-gray-50 py-8 relative">
    <div class="absolute inset-0 z-0"
      :style="{ backgroundImage: `url(${bgImage})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center center', backgroundSize: 'cover' }">
    </div>
    <div class="relative z-10" v-show="currentStep === 1">
      <div class="register-title-box">
        <div class="text-gray-800 register-title">服务商入驻信息</div>
      </div>
      <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <!-- 企业信息表单 -->
        <div class="form-step">

          <a-form :style="{ width: '100%', maxWidth: '600px', margin: '0 auto' }" auto-label-width :model="companyForm"
            layout="horizontal" ref="companyFormRef">
            <div class="text-left line-box">营业证件信息</div>
            <!-- 1. 上传营业执照 -->
            <a-form-item field="businessLicense" label="营业执照" :rules="[{ required: true, message: '营业执照必填' }]">
              <a-upload list-type="picture-card" :limit="1" v-model="companyForm.businessLicense" :file-list="companyForm.businessLicenseList"
                @change="handleBusinessLicenseChange" :auto-upload="false" class="custom-upload">
                <template #upload-button>
                  <div class="upload-placeholder">
                    <icon-plus class="upload-icon" />
                    <div class="upload-text">营业执照</div>
                  </div>
                </template>
              </a-upload>
              <div class="upload-tip">请上传营业执照正本的扫描件或清晰照片</div>
            </a-form-item>
            <!-- 企业名称 -->
            <a-form-item field="companyName" label="企业名称" :rules="[{ required: true, message: '企业名称必填' }]">
              <a-input v-model="companyForm.companyName" placeholder="请输入企业名称" />
            </a-form-item>

            <!-- 法定代表人 -->
            <a-form-item field="legalRepresentative" label="法定代表人" :rules="[{ required: true, message: '法定代表人必填' }]">
              <a-input v-model="companyForm.legalRepresentative" placeholder="请输入法定代表人姓名" />
            </a-form-item>

            <!-- 统一社会信用代码 -->
            <a-form-item field="creditCode" label="统一社会信用代码" :rules="[{ required: true, message: '统一社会信用代码必填' }]">
              <a-input v-model="companyForm.creditCode" placeholder="请输入统一社会信用代码" />
            </a-form-item>
            <!-- 3. 选择纳税评级证明等级 -->
            <a-form-item field="taxLevel" label="纳税评级等级">
              <a-radio-group v-model="companyForm.taxLevel">
                <a-radio value="A">A</a-radio>
                <a-radio value="B">B</a-radio>
                <a-radio value="M">M</a-radio>
                <a-radio value="C">C</a-radio>
                <a-radio value="D">D</a-radio>

              </a-radio-group>
            </a-form-item>

            <!-- 4. 上传评级证明 -->
            <a-form-item field="taxCertificate" label="评级证明" :rules="[{ required: true, message: '评级证明必填' }]">

              <a-upload list-type="picture-card" :limit="1" v-model="companyForm.taxCertificate"
                :file-list="companyForm.taxCertificateList" @change="handleTaxCertificateChange" :auto-upload="false"
                class="custom-upload">
                <template #upload-button>
                  <div class="upload-placeholder" style="margin-left: 14px;">
                    <icon-plus class="upload-icon" />
                    <div class="upload-text">评级证明</div>
                  </div>
                </template>
              </a-upload>
            </a-form-item>
            <!-- 经营范围 -->
            <a-form-item field="businessScope" label="经营范围" :rules="[{ required: true, message: '经营范围必填' }]">
              <a-textarea v-model="companyForm.businessScope" placeholder="请输入经营范围"
                :auto-size="{ minRows: 2, maxRows: 5 }" />
            </a-form-item>
            <!-- 注册地址 -->
            <a-form-item field="registeredAddress" label="经营地址(执照)"
              :rules="[{ required: true, message: '经营地址(执照)必填' }]">
              <a-input v-model="companyForm.registeredAddress" placeholder="请输入营业执照上的经营地址" />
            </a-form-item>

            <!-- 5. 所在地区 -->
            <a-form-item field="regionCodes" label="所在地区" style="flex-direction: column;">
              <a-form-item label="省/市/区" field="regionCodes" :rules="[{ required: true, message: '请选择所在地区' }]">
                <a-cascader v-model="companyForm.regionCodes" :options="regionOptions" placeholder="请选择所在地区"
                  :style="{ width: '320px' }" allow-clear path-mode expand-trigger="click" check-strictly
                  @change="handleRegionChange" />
              </a-form-item>
            </a-form-item>

            <!-- 6. 办公地址 -->
            <a-form-item field="detailAddress" label="办公地址" :rules="[{ required: true, message: '办公地址必填' }]">
              <a-input v-model="companyForm.detailAddress" placeholder="请输入详细办公地址" />
            </a-form-item>
            <div class="text-left line-box">经营者信息</div>
            <!-- 2. 上传法人身份证 -->
            <a-form-item field="legalIdCard" label="法人身份证" style="flex-direction: column;">
              <div class="id-card-upload">

                <a-form-item field="idCardFrontList" label="人面像" :rules="[{ required: true, message: '人面像必填' }]">
                  <a-upload list-type="picture-card" :limit="1" v-model="companyForm.idCardFrontList" :file-list="companyForm.idCardFrontList"
                    @change="handleIdCardFrontChange" :auto-upload="false" class="custom-upload">
                    <template #upload-button>
                      <div class="upload-placeholder" style="margin-left: 14px;">
                        <icon-plus class="upload-icon" />
                        <div class="upload-text">人像面</div>
                      </div>
                    </template>
                  </a-upload>
                </a-form-item>
                <a-form-item field="idCardBackList" label="国徽面" :rules="[{ required: true, message: '国徽面必填' }]">
                  <a-upload list-type="picture-card" :limit="1" v-model="companyForm.idCardBackList" :file-list="companyForm.idCardBackList"
                    @change="handleIdCardBackChange" :auto-upload="false" class="custom-upload">
                    <template #upload-button>
                      <div class="upload-placeholder" style="margin-left: 14px;">
                        <icon-plus class="upload-icon" />
                        <div class="upload-text">国徽面</div>
                      </div>
                    </template>
                  </a-upload>
                </a-form-item>
              </div>
            </a-form-item>
            <!-- 经营者姓名 -->
            <a-form-item field="operatorName" label="经营者姓名" :rules="[{ required: true, message: '经营者姓名必填' }]">
              <a-input v-model="companyForm.operatorName" placeholder="请输入经营者姓名" />
            </a-form-item>
            <!-- 身份证号码 -->
            <a-form-item field="idCardNumber" label="身份证号码"
              :rules="[{ required: true, message: '身份证号码必填' }, { match: /^\d{17}[\dXx]$/, message: '请输入正确的身份证号码' }]">
              <a-input v-model="companyForm.idCardNumber" placeholder="请输入法定代表人身份证号码" />
            </a-form-item>
            <div class="text-left line-box">联系人信息</div>
            <!-- 6. 联系人 -->
            <a-form-item field="contacts" label="联系人" :rules="[{ required: true, message: '至少添加一个联系人' }]"
              style="flex-direction: column;">
              <div class="contacts-list">
                <div v-for="(contact, index) in companyForm.contacts" :key="index" class="contact-item">
                  <div class="contact-header">
                    <span>联系人 {{ index + 1 }}</span>
                    <a-button v-if="index > 0" type="text" status="danger" @click="removeContact(index)">
                      <template #icon>
                        <icon-delete />
                      </template>
                      删除
                    </a-button>
                  </div>
                  <div class="contact-form">
                    <a-form-item :field="`contacts[${index}].position`" label="职务"
                      :rules="[{ required: true, message: '职务必填' }]">
                      <a-input v-model="contact.position" placeholder="职务" />
                    </a-form-item>
                    <a-form-item :field="`contacts[${index}].name`" label="姓名"
                      :rules="[{ required: true, message: '姓名必填' }]">
                      <a-input v-model="contact.name" placeholder="姓名" />
                    </a-form-item>
                    <a-form-item :field="`contacts[${index}].phone`" label="电话"
                      :rules="[{ required: true, message: '电话必填' }]">
                      <a-input v-model="contact.phone" placeholder="电话" />
                    </a-form-item>
                    <a-form-item :field="`contacts[${index}].email`" label="邮箱">
                      <a-input v-model="contact.email" placeholder="邮箱" />
                    </a-form-item>
                  </div>
                </div>
                <div class="add-contact">
                  <a-button type="outline" @click="addContact">
                    <template #icon>
                      <icon-plus />
                    </template>
                    添加联系人
                  </a-button>
                </div>
              </div>
            </a-form-item>
            <div class="text-left line-box">财务信息</div>
            <!-- 7. 银行信息 -->
            <a-form-item field="bankInfo" label="银行信息" style="flex-direction: column;">
              <a-form-item field="bankName" label="开户银行" :rules="[{ required: true, message: '开户银行必填' }]">
                <a-input v-model="companyForm.bankName" placeholder="请输入开户银行" />
              </a-form-item>
              <a-form-item field="bankBranch" label="开户网点" :rules="[{ required: true, message: '开户网点必填' }]">
                <a-input v-model="companyForm.bankBranch" placeholder="请输入开户网点" />
              </a-form-item>
              <a-form-item field="bankAccount" label="对公银行账号" :rules="[{ required: true, message: '对公银行账号必填' }]">
                <a-input v-model="companyForm.bankAccount" placeholder="请输入对公银行账号" />
              </a-form-item>
              <a-form-item field="bankCertificate" label="开户证明" :rules="[{ required: true, message: '开户证明必填' }]">
                <a-upload list-type="picture-card" :limit="1" v-model="companyForm.bankCertificate" :file-list="companyForm.bankCertificateList"
                  @change="handleBankCertificateChange" :auto-upload="false" class="custom-upload">
                  <template #upload-button>
                    <div class="upload-placeholder">
                      <icon-plus class="upload-icon" />
                      <div class="upload-text">开户证明</div>
                    </div>
                  </template>
                </a-upload>
              </a-form-item>
            </a-form-item>

            <div class="flex justify-center mt-8">
              <a-button @click="goBack" class="mr-5" style="width: 200px">再想想</a-button>
              <a-button type="primary" @click="nextStep"
                class="bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold"
                style="width: 200px">提交</a-button>
            </div>
          </a-form>
        </div>
      </div>
    </div>
    <!-- 完成注册 -->
    <div v-show="currentStep === 2" class="relative z-10 register-gradient">
      <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8" style="width: 100%">
        <div class="register-success">
          <icon-check-circle class="success-icon text-6xl text-green-500 mb-4" />
          <h2 class="text-2xl font-semibold mb-2">提交成功</h2>
          <p class="text-gray-600 mb-6">您的企业信息资料已提交，请耐心等待平台审核</p>
          <div class="flex justify-center">
            <a-button type="primary" @click="goToLogin"
              class="w-48 bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold">返回登录</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Message } from "@arco-design/web-vue";
import commonApi from "~/api/common";
import providerAuthApi from "~/api/provider/auth";
import providerSystemApi from "~/api/provider/system";
const bgImage = new URL(
  "@/assets/images/provider/base-map.jpg",
  import.meta.url
).href;
definePageMeta({
  layout: false,
  name: "provider-information",
  path: "/provider/information",
  title: "企业信息上传"
});

// 当前步骤
let currentStep = ref(1);

// 表单引用
const basicFormRef = ref(null);
const companyFormRef = ref(null);

// 获取路由信息，检查是否是从审核失败页面跳转过来（编辑模式）
const route = useRoute();
const isEditMode = ref(false);
const loading = ref(false);

// 格式化图片URL的工具函数
const formatImageUrl = url => {
  if (!url) return "";
  if (url.startsWith("blob:")) {
    return url;
  } else if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  } else {
    return url;
  }
};

// 基础信息表单数据
const basicForm = reactive({
  username: "",
  nickname: "",
  password: "",
  confirmPassword: "",
  email: "",
  phone: "",
  verificationCode: ""
});

// 企业信息表单数据
const companyForm = reactive({
  // 基本信息
  companyName: "",

  // 新增必填字段
  legalRepresentative: "", // 法定代表人
  operatorName: "", // 经营者姓名
  idCardNumber: "", // 身份证号码
  creditCode: "", // 统一社会信用代码
  registeredAddress: "", // 注册地址
  businessScope: "", // 经营范围

  // 营业执照
  businessLicense: "",
  businessLicenseList: [],

  // 法人身份证
  idCardFrontList: [],
  idCardBackList: [],

  // 纳税评级
  taxLevel: "A",
  taxCertificateList: [],
  taxCertificate: "", // 评级证明URL

  // 办公地址
  regionCodes: [], // 存储省市区编码（字符串类型，用于级联选择器）
  regionCodesNumber: [], // 存储省市区编码（数字类型，用于API提交）
  regionNames: [], // 存储省市区名称
  detailAddress: "",

  // 联系人信息
  contacts: [
    {
      position: "", // 职务
      name: "", // 姓名
      phone: "", // 电话
      email: "" // 邮箱
    }
  ],

  // 银行信息
  bankName: "", // 开户银行
  bankBranch: "", // 开户网点
  bankAccount: "", // 对公银行账号
  bankCertificateList: [], // 开户证明列表
  bankCertificate: "" // 开户证明URL
});

// 省市区级联选择器数据
const regionOptions = ref([]);

// 获取省市区数据
const fetchRegionTree = async () => {
  try {
    // 获取token
    const tool = await import("@/utils/tool");
    const token = tool.default.local.get("token_provider");

    // 设置请求参数
    const params = {
      parentId: 0,
      excludeStreet: false // 修改为false，允许获取到街道级别数据
    };

    // 显示加载中提示
    Message.loading("正在加载省市区数据...");

    // 直接使用axios调用接口，手动设置请求头
    const axios = (await import("axios")).default;
    const { baseUrl } = await import("@/api/config");
    const result = await axios({
      url: `${baseUrl}/api/v1/master/region/tree`,
      method: "get",
      params,
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // 处理响应数据
    const response = result.data;
    // debugger

    if (response && response.code === 200 && response.data) {
      console.log("获取到省市区数据:", response.data);

      // 处理返回的数据，转换为级联选择器所需格式
      const formattedData = formatRegionData(response.data);
      regionOptions.value = formattedData;

      console.log("格式化后的省市区数据:", formattedData);
      Message.success("省市区数据加载成功");
    } else {
      console.error("获取省市区数据失败:", response);
      Message.error(response?.message || "获取省市区数据失败");
    }
  } catch (error) {
    console.error("获取省市区数据失败:", error);
    Message.error("获取省市区数据失败，请稍后再试");
  }
};

// 格式化省市区数据为级联选择器所需格式
const formatRegionData = data => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 检查是否有子级数据
    const hasChildren =
      item.children && Array.isArray(item.children) && item.children.length > 0;

    return {
      value: item.code,
      label: item.name,
      // 只有子级数据存在时才递归处理
      children: hasChildren ? formatRegionData(item.children) : []
    };
  });
};

// 处理区域选择变化
const handleRegionChange = (value, selectedOptions) => {
  console.log("=== 省市区选择变化 ===");
  console.log("原始值 value:", value);
  console.log("选中选项 selectedOptions:", selectedOptions);

  // 清空之前的数据
  companyForm.regionNames = [];
  companyForm.regionCodesNumber = [];

  // 确保 value 是数组
  if (Array.isArray(value) && value.length > 0) {
    // 保存选中的区域名称（省、市、区的名称）
    if (Array.isArray(selectedOptions) && selectedOptions.length > 0) {
      companyForm.regionNames = selectedOptions.map(option => option.label);
      console.log("保存的区域名称:", companyForm.regionNames);
    }

    // 保存所有层级的编码（省code、市code、区code）
    companyForm.regionCodesNumber = value.map(code => parseInt(code));

    console.log("保存的区域编码（所有层级）:", companyForm.regionCodesNumber);
    console.log("选择层级数:", companyForm.regionCodesNumber.length);

    // 根据选择的层级显示不同的信息
    if (companyForm.regionCodesNumber.length === 1) {
      console.log("用户选择了省级:", companyForm.regionNames[0]);
    } else if (companyForm.regionCodesNumber.length === 2) {
      console.log("用户选择了市级:", companyForm.regionNames.join(" > "));
    } else if (companyForm.regionCodesNumber.length === 3) {
      console.log("用户选择了区级:", companyForm.regionNames.join(" > "));
    }

  } else {
    // 处理单个值的情况（非数组）
    if (value) {
      console.log("收到非数组值:", value);
      // 如果是单个值，转换为数组处理
      const codeValue = parseInt(value);
      if (!isNaN(codeValue)) {
        companyForm.regionCodesNumber = [codeValue];
        console.log("转换后的区域编码:", companyForm.regionCodesNumber);
      }
    } else {
      // 清空值
      console.log("清空区域选择");
    }
  }

  console.log("最终保存的数据:");
  console.log("- regionNames:", companyForm.regionNames);
  console.log("- regionCodesNumber:", companyForm.regionCodesNumber);
  console.log("===================");
};

// 获取服务商详情信息
const fetchProviderInfo = async () => {
  loading.value = true;
  try {
    const res = await providerAuthApi.getInfoDetail(JSON.parse(localStorage.getItem('user_provider')).id);
    if (res && res.code === 200 && res.data) {
      const { business_info } = res.data;

      // 填充表单数据
      if (business_info) {
        // 基本信息
        companyForm.companyName = business_info.cardData.name || "";
        companyForm.legalRepresentative = business_info.cardData.legalPerson || '';
        companyForm.creditCode = business_info.cardData.registerNumber || '';
        companyForm.registeredAddress = business_info.cardData.address || '';
        companyForm.businessScope = business_info.cardData.business || '';
        companyForm.idCardNumber = business_info.cardData.idCardNumber || '';
        companyForm.operatorName = business_info.cardData.legalPerson || '';
        companyForm.idCardNumber = business_info.id_card_number || '';
        // 营业执照
        if (business_info.businessLicense) {
          const url = formatImageUrl(business_info.businessLicense);
          companyForm.businessLicense = url;
          companyForm.businessLicenseList = [
            {
              uid: "1",
              name: "营业执照",
              url: url,
              status: "done"
            }
          ];
        }

        // 法人身份证
        if (business_info.identity_card_portrait) {
          const url = formatImageUrl(business_info.identity_card_portrait);
          companyForm.idCardFrontList = [
            {
              uid: "1",
              name: "身份证人像面",
              url: url,
              status: "done"
            }
          ];
        }

        if (business_info.identity_card_national_emblem) {
          const url = formatImageUrl(
            business_info.identity_card_national_emblem
          );
          companyForm.idCardBackList = [
            {
              uid: "1",
              name: "身份证国徽面",
              url: url,
              status: "done"
            }
          ];
        }

        // 纳税评级
        companyForm.taxLevel = business_info.grade || "";

        if (business_info.rating) {
          const url = formatImageUrl(business_info.rating);
          companyForm.taxCertificateList = [
            {
              uid: "1",
              name: "纳税评级证明",
              url: url,
              status: "done"
            }
          ];
        }

        // 办公地址
        companyForm.detailAddress = business_info.comaddress || "";

        // 处理区域编码数组
        if (
          business_info.area &&
          Array.isArray(business_info.area) &&
          business_info.area.length > 0
        ) {
          // 将数字转为字符串，因为级联选择器需要字符串类型的值
          companyForm.regionCodes = business_info.area.map(code =>
            String(code)
          );
          // 同时设置数字类型的区域编码，用于API提交
          companyForm.regionCodesNumber = business_info.area;
          console.log("设置区域编码（字符串）：", companyForm.regionCodes);
          console.log("设置区域编码（数字）：", companyForm.regionCodesNumber);
        }

        // 联系人信息
        if (business_info.contacts && business_info.contacts.length > 0) {
          companyForm.contacts = business_info.contacts.map(
            (contact, index) => ({
              position: contact.position || "",
              name: contact.name || "",
              phone: contact.phone || "",
              email: contact.email || ""
            })
          );
        }

        // 银行信息
        companyForm.bankName = business_info.bank || "";
        companyForm.bankBranch = business_info.branch || "";
        companyForm.bankAccount = business_info.bankNumber || "";

        if (business_info.license) {
          const url = formatImageUrl(business_info.license);
          companyForm.bankCertificateList = [
            {
              uid: "1",
              name: "开户证明",
              url: url,
              status: "done"
            }
          ];
        }
      }

      Message.success("已加载您之前提交的信息，请修改后重新提交");
      isEditMode.value = true;
    }
  } catch (error) {
    console.error("获取服务商信息失败:", error);
    Message.error("获取服务商信息失败，将以新提交方式处理");
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取省市区数据和检查是否需要加载已有信息
onMounted(async () => {
  // 先加载省市区数据
  await fetchRegionTree();

  // 检查是否从审核失败页面跳转过来
  if (route.query.edit === "true") {
    await fetchProviderInfo();
  }
});

// 通用图片上传处理函数
const handleImageUpload = async (
  fileInfo,
  fileList,
  fieldName,
  listFieldName
) => {
  try {
    // 防御性检查，确保参数正确
    if (!fileInfo || !fileList || !listFieldName) {
      console.error("上传图片参数错误:", {
        fileInfo,
        fileList,
        fieldName,
        listFieldName
      });
      Message.error("上传图片参数错误");
      return fileList;
    }

    // 获取文件对象
    const file =
      fileInfo.file ||
      (fileList.length > 0 ? fileList[fileList.length - 1] : null);

    if (!file || !file.file) {
      console.error("没有找到有效的文件对象:", file);
      Message.error("没有找到有效的文件");
      return fileList;
    }

    // 创建FormData对象
    const formData = new FormData();
    formData.append("file", file.file);

    // 显示上传中提示
    Message.loading("正在上传图片...");

    // 调用common.js中的uploadImage方法上传图片
    const response = await commonApi.uploadImage(formData);

    if (
      response &&
      response.code === 200 &&
      response.data &&
      response.data.fileUrl
    ) {
      // 上传成功，获取文件URL
      const fileUrl = response.data.fileUrl;

      // 更新文件列表和URL
      const newFileList = [...fileList];
      if (newFileList.length > 0) {
        newFileList[newFileList.length - 1].url = fileUrl;
        newFileList[newFileList.length - 1].status = "done";
      }

      // 更新表单数据
      companyForm[listFieldName] = newFileList;
      console.log(companyForm[listFieldName], "companyForm[listFieldName]");
      if (fieldName) {
        companyForm[fieldName] = fileUrl;
        console.log(companyForm.taxCertificate, "companyForm[fieldName]");
      }

      Message.success("图片上传成功");
      return newFileList;
    } else {
      console.error("图片上传失败，响应:", response);
      Message.error("图片上传失败：" + (response?.message || "未知错误"));
      // 移除上传失败的文件
      return fileList.length > 0 ? fileList.slice(0, fileList.length - 1) : [];
    }
  } catch (error) {
    console.error("上传图片时发生错误:", error);
    Message.error("上传图片时发生错误：" + (error.message || "未知错误"));
    // 移除上传失败的文件
    return fileList.length > 0 ? fileList.slice(0, fileList.length - 1) : [];
  }
};

// 处理营业执照上传
const handleBusinessLicenseChange = async fileInfo => {
  console.log("=== 营业执照文件变化 ===");
  console.log("fileInfo:", fileInfo);

  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleBusinessLicenseChange: fileInfo为空");
    return;
  }

  // 兼容不同的参数格式
  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file =
    fileInfo.file ||
    (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  console.log("fileList:", fileList);
  console.log("fileList.length:", fileList.length);
  console.log("file:", file);

  // 如果有文件且状态为上传中
  if (
    (fileList.length > 0 && file && file.status === "uploading") ||
    file.status === "init"
  ) {
    // 上传图片
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "businessLicense",
      "businessLicenseList"
    );
    companyForm.businessLicenseList = updatedFileList || [];
    console.log("handleBusinessLicenseChange: updatedFileList", companyForm.businessLicenseList);
    // 设置businessLicense字段，确保表单验证通过
    if (
      updatedFileList &&
      updatedFileList.length > 0 &&
      updatedFileList[0].url
    ) {
      const fileUrl = updatedFileList[0].url;
      companyForm.businessLicense = fileUrl;

      // 立即触发表单验证，清除必填提示
      // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
      setTimeout(() => {
        if (companyFormRef.value) {
          // 使用validateField只验证单个字段
          companyFormRef.value.validateField('businessLicense');
        }
      }, 0);

      // 调用OCR识别接口
      try {
        Message.loading({
          content: "正在识别营业执照信息...",
          duration: 0
        });

        // 创建FormData对象

        const formData = new FormData();
        formData.append("imageUrl", fileUrl);

        const ocrResponse = await providerSystemApi.ocr.businessLicense(
          formData
        );

        Message.clear();

        if (ocrResponse && ocrResponse.code === 200 && ocrResponse.data) {
          const ocrData = JSON.parse(ocrResponse.data.data).data;
          console.log(ocrData, "ocrData");
          // 自动填充表单数据
          companyForm.companyName =
            ocrData.companyName || companyForm.companyName;
          companyForm.creditCode = ocrData.creditCode || companyForm.creditCode;
          companyForm.registeredAddress =
            ocrData.businessAddress || companyForm.registeredAddress;
          companyForm.businessScope =
            ocrData.businessScope || companyForm.businessScope;
          companyForm.operatorName =
            ocrData.legalPerson || companyForm.operatorName;
          companyForm.legalRepresentative =
            ocrData.legalPerson || companyForm.legalRepresentative;
          // 更新cardData对象，用于提交
          companyForm.cardData = {
            address: ocrData.businessAddress || "",
            angle: 0,
            business: ocrData.businessScope || "",
            capital: ocrData.registeredCapital || "",
            emblem: {},
            establishDate: ocrData.RegistrationDate || "",
            legalPerson: ocrData.legalPerson || "",
            name: ocrData.companyName || "",
            QRCode: {},
            registerNumber: ocrData.creditCode || "",
            stamp: {},
            title: { text: ocrData.title || "营业执照" },
            type: ocrData.companyType || "",
            validPeriod: ocrData.validPeriod || "",
            url: fileUrl
          };

          Message.success("营业执照识别成功，已自动填充相关信息");
        } else {
          Message.error(
            "营业执照识别失败：" + (ocrResponse?.message || "未知错误")
          );
        }
      } catch (error) {
        Message.clear();
        console.error("营业执照OCR识别失败:", error);
        Message.error("营业执照识别失败：" + (error.message || "未知错误"));
      }
    }
  } else if (fileList.length === 0) {
    // 如果没有文件，清空表单数据
    companyForm.businessLicense = "";
    companyForm.businessLicenseList = [];
    companyForm.cardData = {};
    // 提醒用户营业执照为必填项
    Message.warning("营业执照为必填项，请重新上传");
  }
};

// 处理法人身份证正面上传
const handleIdCardFrontChange = async fileInfo => {
  console.log("=== 身份证正面文件变化 ===");
  console.log("fileInfo:", fileInfo);

  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleIdCardFrontChange: fileInfo为空");
    return;
  }

  // 兼容不同的参数格式
  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file =
    fileInfo.file ||
    (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  console.log("fileList:", fileList);
  console.log("fileList.length:", fileList.length);

  // 如果有文件且状态为上传中
  if (
    (fileList.length > 0 && file && file.status === "uploading") ||
    file.status === "init"
  ) {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      null,
      "idCardFrontList"
    );
    companyForm.idCardFrontList = updatedFileList || [];

    // 立即触发表单验证，清除必填提示
    // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
    setTimeout(() => {
      if (companyFormRef.value) {
        // 使用validateField只验证单个字段
        companyFormRef.value.validateField('idCardFrontList');
      }
    }, 0);
  } else if (fileList.length === 0) {
    companyForm.idCardFrontList = [];
    // 提醒用户人面像为必填项
    Message.warning("法人身份证人面像为必填项，请重新上传");
  }
};

// 处理法人身份证背面上传
const handleIdCardBackChange = async fileInfo => {
  console.log("=== 身份证背面文件变化 ===");
  console.log("fileInfo:", fileInfo);

  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleIdCardBackChange: fileInfo为空");
    return;
  }

  // 兼容不同的参数格式
  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file =
    fileInfo.file ||
    (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  console.log("fileList:", fileList);
  console.log("fileList.length:", fileList.length);

  // 如果有文件且状态为上传中
  if (
    (fileList.length > 0 && file && file.status === "uploading") ||
    file.status === "init"
  ) {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      null,
      "idCardBackList"
    );
    companyForm.idCardBackList = updatedFileList || [];

    // 立即触发表单验证，清除必填提示
    // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
    setTimeout(() => {
      if (companyFormRef.value) {
        // 使用validateField只验证单个字段
        companyFormRef.value.validateField('idCardBackList');
      }
    }, 0);
  } else if (fileList.length === 0) {
    companyForm.idCardBackList = [];
    // 提醒用户国徽面为必填项
    Message.warning("法人身份证国徽面为必填项，请重新上传");
  }
};

// 处理纳税评级证明上传
const handleTaxCertificateChange = async fileInfo => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleTaxCertificateChange: fileInfo为空");
    return;
  }

  // 确保taxCertificateList已初始化为数组
  if (!companyForm.taxCertificateList) {
    companyForm.taxCertificateList = [];
  }

  // 兼容不同的参数格式
  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file =
    fileInfo.file ||
    (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  // 如果有文件且状态为上传中
  if (
    (fileList.length > 0 && file && file.status === "uploading") ||
    (file && file.status === "init")
  ) {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "taxCertificate", // 单个文件URL字段名
      "taxCertificateList" // 文件列表字段名
    );

    console.log(companyForm.taxCertificate, "taxCertificate");

    companyForm.taxCertificateList = updatedFileList || [];
    // 设置taxCertificate字段，确保表单验证通过
    if (
      updatedFileList &&
      updatedFileList.length > 0 &&
      updatedFileList[0].url
    ) {
      companyForm.taxCertificate = updatedFileList[0].url;

      // 立即触发表单验证，清除必填提示
      // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
      setTimeout(() => {
        if (companyFormRef.value) {
          // 使用validateField只验证单个字段
          companyFormRef.value.validateField('taxCertificate');
        }
      }, 0);
    }
  } else if (fileList.length === 0) {
    companyForm.taxCertificateList = [];
    companyForm.taxCertificate = "";
    // 提醒用户评级证明为必填项
    Message.warning("纳税评级证明为必填项，请重新上传");
  }
};

// 处理银行开户证明上传
const handleBankCertificateChange = async fileInfo => {
  console.log("=== 开户证明文件变化 ===");
  console.log("fileInfo:", fileInfo);

  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleBankCertificateChange: fileInfo为空");
    return;
  }

  // 兼容不同的参数格式
  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file =
    fileInfo.file ||
    (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  console.log("fileList:", fileList);
  console.log("fileList.length:", fileList.length);

  // 如果有文件且状态为上传中
  if (
    (fileList.length > 0 && file && file.status === "uploading") ||
    file.status === "init"
  ) {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "bankCertificate",
      "bankCertificateList"
    );
    companyForm.bankCertificateList = updatedFileList || [];
    // 设置bankCertificate字段，确保表单验证通过
    if (
      updatedFileList &&
      updatedFileList.length > 0 &&
      updatedFileList[0].url
    ) {
      companyForm.bankCertificate = updatedFileList[0].url;

      // 立即触发表单验证，清除必填提示
      // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
      setTimeout(() => {
        if (companyFormRef.value) {
          // 使用validateField只验证单个字段
          companyFormRef.value.validateField('bankCertificate');
        }
      }, 0);
    }
  } else if (fileList.length === 0) {
    companyForm.bankCertificateList = [];
    companyForm.bankCertificate = "";
    // 提醒用户开户证明为必填项
    Message.warning("银行开户证明为必填项，请重新上传");
  }
};

// 添加联系人
const addContact = () => {
  companyForm.contacts.push({
    position: "",
    name: "",
    phone: "",
    email: ""
  });
};

// 删除联系人
const removeContact = index => {
  if (companyForm.contacts.length > 1) {
    companyForm.contacts.splice(index, 1);
  } else {
    Message.warning("至少需要保留一个联系人");
  }
};

// 短信验证码相关
const countdown = ref(0);
const timer = ref(null);

// 获取短信验证码
const getVerificationCode = async () => {
  if (countdown.value > 0) return;

  // 验证手机号
  if (!basicForm.phone || !/^1[3-9]\d{9}$/.test(basicForm.phone)) {
    Message.error("请输入正确的手机号");
    return;
  }

  try {
    // 这里应该调用发送短信验证码的API
    // 模拟发送成功
    countdown.value = 60;
    Message.success("验证码已发送到您的手机");

    // 倒计时
    timer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer.value);
      }
    }, 1000);
  } catch (error) {
    console.error("发送验证码失败:", error);
    Message.error("发送验证码失败，请稍后重试");
  }
};

// 提交表单
const nextStep = async () => {
  try {
    // 打印当前表单数据，用于调试
    console.log("当前表单数据:", companyForm);

    console.log("=== 开始验证必填字段 ===");
    console.log("当前表单数据:", companyForm);

    // 验证必填字段（使用与表单 field 属性一致的字段名）
    const requiredFields = {
      // 文件上传字段
      businessLicense: "营业执照",
      idCardFrontList: "法人身份证人像面",
      idCardBackList: "法人身份证国徽面",
      taxCertificate: "纳税评级证明",
      bankCertificate: "开户证明",

      // 基本信息字段
      companyName: "企业名称",
      legalRepresentative: "法定代表人",
      creditCode: "统一社会信用代码",
      businessScope: "经营范围",
      registeredAddress: "经营地址(执照)",
      detailAddress: "办公地址",
      operatorName: "经营者姓名",
      idCardNumber: "身份证号码",

      // 省市区
      regionCodes: "省市区",

      // 联系人
      contacts: "联系人",

      // 银行信息
      bankName: "开户银行",
      bankBranch: "开户网点",
      bankAccount: "对公银行账号"
    };

    // 检查必填字段是否填写
    const missingFields = [];
    for (const [field, label] of Object.entries(requiredFields)) {
      // 打印每个字段的值，用于调试
      console.log(`检查字段 ${field}:`, companyForm[field]);

      // 特殊处理文件上传字段
      if (field === "businessLicense") {
        console.log(`检查营业执照: businessLicenseList =`, companyForm.businessLicenseList);
        if (!companyForm.businessLicenseList || companyForm.businessLicenseList.length === 0) {
          console.log(`营业执照文件为空`);
          missingFields.push(label);
        }
        continue;
      }
      if (field === "taxCertificate") {
        console.log(`检查评级证明: taxCertificateList =`, companyForm.taxCertificateList);
        if (!companyForm.taxCertificateList || companyForm.taxCertificateList.length === 0) {
          console.log(`评级证明文件为空`);
          missingFields.push(label);
        }
        continue;
      }
      if (field === "bankCertificate") {
        console.log(`检查开户证明: bankCertificateList =`, companyForm.bankCertificateList);
        if (!companyForm.bankCertificateList || companyForm.bankCertificateList.length === 0) {
          console.log(`开户证明文件为空`);
          missingFields.push(label);
        }
        continue;
      }
      // 特殊处理文件上传列表（身份证）
      if (field.endsWith("List")) {
        console.log(`检查文件列表 ${field}:`, companyForm[field]);
        if (!companyForm[field] || companyForm[field].length === 0) {
          console.log(`文件列表 ${field} 为空`);
          missingFields.push(label);
        }
        continue;
      }
      // 特殊处理联系人数组
      if (field === "contacts") {
        if (!companyForm[field] || companyForm[field].length === 0) {
          console.log("联系人数组为空");
          missingFields.push(label);
        } else {
          // 检查每个联系人的必填字段
          const contactErrors = [];
          companyForm[field].forEach((contact, index) => {
            const contactNumber = index + 1;

            // 检查职务
            if (!contact.position || contact.position.trim() === '') {
              contactErrors.push(`联系人${contactNumber}的职务`);
            }

            // 检查姓名
            if (!contact.name || contact.name.trim() === '') {
              contactErrors.push(`联系人${contactNumber}的姓名`);
            }

            // 检查电话
            if (!contact.phone || contact.phone.trim() === '') {
              contactErrors.push(`联系人${contactNumber}的电话`);
            }

            console.log(`联系人${contactNumber}验证:`, {
              position: contact.position,
              name: contact.name,
              phone: contact.phone,
              email: contact.email
            });
          });

          if (contactErrors.length > 0) {
            missingFields.push(...contactErrors);
          }
        }
        continue;
      }
      // 特殊处理区域编码数组
      if (field === "regionCodes") {
        if (!companyForm[field] || companyForm[field].length === 0) {
          console.log("区域编码数组为空");
          missingFields.push(label);
        }
        continue;
      }
      // 其他普通字段（字符串类型）
      const fieldValue = companyForm[field];
      if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
        console.log(`字段 ${field} 为空或只包含空格`);
        missingFields.push(label);
      }
    }

    // 打印缺失的字段，用于调试
    console.log("缺失的字段:", missingFields);

    // 如果有未填写的必填字段，显示错误信息
    console.log("=== 验证结果 ===");
    console.log("缺失字段:", missingFields);
    console.log("验证通过:", missingFields.length === 0);
    console.log("================");

    if (missingFields.length > 0) {
      throw new Error(`请填写以下必填项：${missingFields.join("、")}`);
    }

    try {
      // 验证表单
      await companyFormRef.value.validate();
      console.log("表单验证通过");

      // 显示加载状态
      const loading = Message.loading({
        content: "正在提交入驻信息，请稍候...",
        duration: 0
      });

      // 同步手动填写的表单内容到cardData对象
      if (!companyForm.cardData) {
        companyForm.cardData = {};
      }

      // 更新cardData对象，将手动填写的内容也同步进去
      companyForm.cardData = {
        ...companyForm.cardData,
        // 使用手动填写的内容更新cardData
        name: companyForm.companyName || companyForm.cardData.name || '',
        legalPerson: companyForm.operatorName || companyForm.legalRepresentative || companyForm.cardData.legalPerson || '',
        address: companyForm.registeredAddress || companyForm.cardData.address || '',
        business: companyForm.businessScope || companyForm.cardData.business || '',
        registerNumber: companyForm.creditCode || companyForm.cardData.registerNumber || '',
        // 确保图片URL也被正确设置
        url: companyForm.businessLicenseList && companyForm.businessLicenseList.length > 0 ? companyForm.businessLicenseList[0].url : companyForm.businessLicense || ''
      };

      console.log('更新后的cardData:', companyForm.cardData);

      // 构建提交数据
      const submitData = {
        user_id: localStorage.getItem("user_provider")
          ? JSON.parse(localStorage.getItem("user_provider")).id
          : "",
        // user_id: "587926350434674688",
        business_info: {
          // 纳税评级证明等级
          grade: companyForm.taxLevel || "A",

          // 区域ID数组，包含所有选中层级的编码（省、市、区）
          area:
            companyForm.regionCodesNumber &&
              companyForm.regionCodesNumber.length > 0
              ? companyForm.regionCodesNumber
              : Array.isArray(companyForm.regionCodes) &&
                companyForm.regionCodes.length > 0
                ? companyForm.regionCodes.map(code => parseInt(code))
                : [],

          // 联系人信息
          contacts: companyForm.contacts.map(contact => ({
            position: contact.position,
            name: contact.name,
            phone: contact.phone,
            email: contact.email
          })),

          // 营业执照信息
          cardData: companyForm.cardData,
          // 营业执照URL
          businessLicense: companyForm.businessLicenseList[0]?.url || "",

          // 企业名称
          comname: companyForm.companyName || "广州吉诺汽车投资有限公司",

          // 新增必填字段
          legal_representative: companyForm.legalRepresentative || "", // 法定代表人
          unified_social_credit_code: companyForm.creditCode || "", // 统一社会信用代码
          registered_address: companyForm.registeredAddress || "", // 注册地址
          business_scope: companyForm.businessScope || "", // 经营范围
          id_card_number: companyForm.idCardNumber || "", // 身份证号码

          // 身份证正面URL
          identity_card_portrait: companyForm.idCardFrontList[0]?.url || "",

          // 身份证反面URL
          identity_card_national_emblem:
            companyForm.idCardBackList[0]?.url || "",

          // 办公地址
          comaddress: companyForm.detailAddress || "",

          // 评级证明URL
          rating: companyForm.taxCertificateList[0]?.url || "",

          // 银行信息
          bank: companyForm.bankName || "",
          branch: companyForm.bankBranch || "",
          bankNumber: companyForm.bankAccount || "",

          // 开户证明文件URL
          license: companyForm.bankCertificateList[0]?.url || ""
        }
      };

      console.log("=== 提交数据详情 ===");
      console.log("完整提交数据:", submitData);
      console.log("区域相关数据:");
      console.log("- regionNames:", companyForm.regionNames);
      console.log("- regionCodes:", companyForm.regionCodes);
      console.log("- regionCodesNumber:", companyForm.regionCodesNumber);
      console.log("- 最终提交的area字段:", submitData.business_info.area);
      console.log("- area字段包含的层级数:", submitData.business_info.area.length);
      console.log("==================");

      // 根据是否是编辑模式决定调用哪个API
      let res;
      if (isEditMode.value) {
        // 编辑模式，调用更新API
        res = await providerAuthApi.updateInfo(submitData);
        loading.close();
        if (res.code === 200) {
          Message.success("信息更新成功，请等待重新审核");
          // 切换到完成步骤
          currentStep.value = 2;
        } else {
          Message.error(res.message || "信息更新失败");
        }
      } else {
        // 新提交模式，调用提交API
        res = await providerAuthApi.submitInfo(submitData);
        loading.close();
        if (res.code === 200) {
          Message.success("入驻信息提交成功，请等待审核");
          // 切换到完成步骤
          currentStep.value = 2;
        } else {
          Message.error(res.message || "入驻信息提交失败");
        }
      }
    } catch (validateError) {
      console.error("表单验证或提交失败:", validateError);
      Message.error(validateError.message || "提交失败，请稍后重试");
      throw validateError;
    }
  } catch (error) {
    console.error("表单验证失败:", error);
    // 如果是必填项的错误，直接显示错误信息
    if (error.message.includes("请填写以下必填项")) {
      Message.error(error.message);
    } else {
      // 如果是其他验证错误，显示默认错误信息
      Message.error("请填写完整的信息后再提交");
    }
  }
};

// 返回登录页
const goToLogin = () => {
  navigateTo("/provider/index");
};

// 返回上一步
const goBack = () => {
  navigateTo("/provider/index");
};
</script>

<style scoped lang="less">
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.register {
  position: absolute;
  font-size: 20px;
  top: 173px;
  left: 563px;
  font-weight: 400;
}

.Improve {
  margin-left: -675px;
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 15px;
}

.register-content {
  width: 100%;
  max-width: 800px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.steps-wrapper {
  margin-bottom: 40px;

  :deep(.arco-steps-item-title) {
    font-weight: 500;
  }

  ::v-deep .arco-steps-item-node {
    display: inline-block;
    // margin-right: 12px;
    font-weight: 500;
    font-size: 16px;
    // vertical-align: top;
  }
}

::v-deep .arco-form-item-content-flex {
  display: flex;
  align-items: flex-start;
  justify-items: flex-start;
  flex-direction: column;
}

.step-icon {
  background-color: #e8f3ff;
  color: #4080ff;

  &.active {
    background-color: #4080ff;
    color: #fff;
  }
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-step {
  min-height: 400px;
  display: flex;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.verification-code {
  display: flex;
  gap: 10px;

  .get-code-btn {
    white-space: nowrap;
    min-width: 120px;
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.id-card-upload {
  display: flex;
  gap: 20px;
  flex-direction: column;
  position: relative;
}

.contacts-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  //   background-color: #fafafa;
}

.contact-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e5e6eb;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
}

.contact-form {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
}

.add-contact {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 自定义上传组件样式 */
.custom-upload {
  :deep(.arco-upload-picture-card) {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
    transition: border-color 0.3s;

    &:hover {
      border-color: #4080ff;
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    color: #999;
    border: 1px dashed;
  }

  .upload-icon {
    font-size: 24px;
    color: #c0c4cc;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 12px;
    color: #909399;
  }
}

.register-success {
  text-align: center;
  padding: 40px 0;

  .success-icon {
    font-size: 60px;
    color: #00b42a;
    margin-bottom: 16px;
  }

  h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #1d2129;
  }

  p {
    font-size: 16px;
    color: #4e5969;
    margin-bottom: 30px;
  }
}

:deep(.arco-form-item-label-col) {
  label::before {
    margin-right: 4px;
  }
}

.register-gradient {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.register-title {
  width: 768px;

  margin-bottom: 15px;
  font-size: 20px;
  font-weight: 400;
  color: #ffffff;
}

::v-deep .arco-form-item-label-col {
  flex: 0 0 36px !important;
}

.register-title-box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.line-box {
  width: 100%;
  height: 40px;
  background: #f2f2f2;
  display: flex;
  align-items: center;
  margin: 15px 0;
  padding: 5px;
}
</style>
