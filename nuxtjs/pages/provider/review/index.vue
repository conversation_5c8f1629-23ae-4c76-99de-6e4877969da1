<template>
  <div class="min-h-screen bg-gray-50 py-8 relative">
    <div
      class="absolute inset-0 z-0"
      :style="{ backgroundImage: `url(${bgImage})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center center', backgroundSize: 'cover' }"
    ></div>
    
    <!-- 审核状态部分 -->
    <div class="relative z-10 mb-6">
      <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-5 text-center">
        <div class="review-status">
          <div class="status-icon mb-2">
            <!-- <icon class="text-blue-500 text-4xl "/> -->
            <i class="iconfont icon-dai-shen   " style="font-size: 48px;"/> 
            <!-- <icon-loading class="text-blue-500 text-4xl animate-spin" /> -->
          </div>
          <h2 class="text-xl font-bold mb-2 text-gray-800">待审核</h2>
          <p class="text-gray-600">您提交的企业信息资料正在审核中，请耐心等待</p>
        </div>
      </div>
    </div>
    
    <!-- 用户上传的内容部分 -->
    <div class="relative z-10">
      <!-- <div class="register-title-box">
        <div class="text-gray-800 register-title " style="color:#fff">您提交的入驻信息</div>
      </div> -->
      <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <!-- 企业信息展示 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 pb-2 border-b border-gray-200">企业信息</h3>
          
          <!-- 企业名称 -->
          <div class="info-item">
            <div class="info-label">企业名称：</div>
            <div class="info-content">{{ companyInfo.companyName || '未提供' }}</div>
          </div>
          
          <!-- 法定代表人 -->
          <div class="info-item">
            <div class="info-label">法定代表人：</div>
            <div class="info-content">{{ companyInfo.legalRepresentative || '未提供' }}</div>
          </div>
          
          <!-- 统一社会信用代码 -->
          <div class="info-item">
            <div class="info-label">统一社会信用代码：</div>
            <div class="info-content">{{ companyInfo.creditCode || '未提供' }}</div>
          </div>
          
          <!-- 注册地址 -->
          <div class="info-item">
            <div class="info-label">注册地址：</div>
            <div class="info-content">{{ companyInfo.registeredAddress || '未提供' }}</div>
          </div>
          
          <!-- 经营范围 -->
          <div class="info-item">
            <div class="info-label">经营范围：</div>
            <div class="info-content">{{ companyInfo.businessScope || '未提供' }}</div>
          </div>

          <!-- 省市区 -->
          <div class="info-item">
            <div class="info-label">所在地区：</div>
            <div class="info-content">{{ regionDisplay || '未提供' }}</div>
          </div>
               <!-- 经营范围 -->
          <div class="info-item">
            <div class="info-label">经营者姓名：</div>
            <div class="info-content">{{ companyInfo.nameOperator || '未提供' }}</div>
          </div>
          
          <!-- 身份证号码 -->
          <div class="info-item">
            <div class="info-label">身份证号码：</div>
            <div class="info-content">{{ companyInfo.idCardNumber || '未提供' }}</div>
          </div>
          
          <!-- 营业执照 -->
          <div class="info-item">
            <div class="info-label">营业执照：</div>
            <div class="info-content">
              <a-image
                v-if="companyInfo.businessLicenseUrl"
                :src="formatImageUrl(companyInfo.businessLicenseUrl)"
                width="120"
                height="120"
                class="rounded"
              />
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
            <!-- 评级证明 -->
          <div class="info-item">
            <div class="info-label">评级证明：</div>
            <div class="info-content">
              <a-image
                v-if="companyInfo.ratingCertificateUrl"
                :src="formatImageUrl(companyInfo.ratingCertificateUrl)"
                width="120"
                height="120"
                class="rounded"
              />
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
          
          <!-- 法人身份证 -->
          <div class="info-item">
            <div class="info-label">法人身份证：</div>
            <div class="info-content id-card-images">
              <div class="id-card-item">
                <div class="id-card-label">人像面</div>
                <a-image
                  v-if="companyInfo.idCardFrontUrl"
                  :src="formatImageUrl(companyInfo.idCardFrontUrl)"
                  width="120"
                  height="120"
                  class="rounded"
                />
                <div v-else class="no-image">暂无图片</div>
              </div>
              <div class="id-card-item">
                <div class="id-card-label">国徽面</div>
                <a-image
                  v-if="companyInfo.idCardBackUrl"
                  :src="formatImageUrl(companyInfo.idCardBackUrl)"
                  width="120"
                  height="120"
                  class="rounded"
                />
                <div v-else class="no-image">暂无图片</div>
              </div>
            </div>
          </div>
          
          <!-- 纳税评级 -->
          <div class="info-item">
            <div class="info-label">纳税评级：</div>
            <div class="info-content">
              <span class="tax-level">{{ companyInfo.taxLevel || '未提供' }}</span>
              <!-- <a-image
                v-if="companyInfo.taxCertificateUrl"
                :src="companyInfo.taxCertificateUrl"
                width="120"
                height="120"
                class="rounded ml-4"
              /> -->
            </div>
          </div>
          
          <!-- 办公地址 -->
          <div class="info-item">
            <div class="info-label">办公地址：</div>
            <div class="info-content">{{ companyInfo.address || '未提供' }}</div>
          </div>
        </div>
        
        <!-- 联系人信息 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 pb-2 border-b border-gray-200">联系人信息</h3>
          <div v-if="companyInfo.contacts && companyInfo.contacts.length > 0">
            <div v-for="(contact, index) in companyInfo.contacts" :key="index" class="contact-card">
              <div class="contact-header">
                <span class="font-medium">联系人 {{ index + 1 }}</span>
              </div>
              <div class="contact-body">
                <div class="contact-info-item">
                  <span class="contact-info-label">职务：</span>
                  <span>{{ contact.position || '未提供' }}</span>
                </div>
                <div class="contact-info-item">
                  <span class="contact-info-label">姓名：</span>
                  <span>{{ contact.name || '未提供' }}</span>
                </div>
                <div class="contact-info-item">
                  <span class="contact-info-label">手机号：</span>
                  <span>{{ contact.phone || '未提供' }}</span>
                </div>
                <div class="contact-info-item">
                  <span class="contact-info-label">邮箱：</span>
                  <span>{{ contact.email || '未提供' }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500">暂无联系人信息</div>
        </div>
        
        <!-- 财务信息 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 pb-2 border-b border-gray-200">财务信息</h3>
          <div v-if="companyInfo.finance">
            <div class="info-item">
              <div class="info-label">开户银行：</div>
              <div class="info-content">{{ companyInfo.finance.bankName || '未提供' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">开户名称：</div>
              <div class="info-content">{{ companyInfo.finance.accountName || '未提供' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">银行账号：</div>
              <div class="info-content">{{ companyInfo.finance.accountNumber || '未提供' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">开户证明：</div>
              <div class="info-content">
                <a-image
                  v-if="companyInfo.finance.proofUrl"
                  :src="formatImageUrl(companyInfo.finance.proofUrl)"
                  width="120"
                  height="120"
                  class="rounded"
                />
                <div v-else class="no-image">暂无图片</div>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500">暂无财务信息</div>
        </div>
        
        <!-- 底部按钮 -->
        <div class="flex justify-center mt-8">
          <a-button type="outline" size="large" @click="goToLogin">返回登录</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import providerAuth from '@/api/provider/auth';

const bgImage = new URL(
  "@/assets/images/provider/base-map.jpg",
  import.meta.url
).href;

definePageMeta({
  layout: false,
  name: "provider-review",
  path: "/provider/review",
  title: "审核中"
});

// 初始化公司信息数据
const companyInfo = reactive({
  // 新增字段
  companyName: '',
  legalRepresentative: '',
  creditCode: '',
  registeredAddress: '',
  businessScope: '',
  idCardNumber: '',
  // 原有字段
  businessLicenseUrl: '',
  nameOperator:'',
  ratingCertificateUrl:"",
  idCardFrontUrl: '',
  idCardBackUrl: '',
  taxLevel: '',
  taxCertificateUrl: '',
  address: '',
  contacts: [],
  finance: {
    bankName: '',
    accountName: '',
    accountNumber: '',
    proofUrl: ''
  }
});

// 加载状态
const loading = ref(false);

// 省市区相关数据
const regionOptions = ref([]);
const regionDisplay = ref('');

// 获取省市区数据
const fetchRegionTree = async () => {
  try {
    // 获取token
    const tool = await import("@/utils/tool");
    const token = tool.default.local.get("token_provider");

    // 设置请求参数
    const params = {
      parentId: 0,
      excludeStreet: false
    };

    // 直接使用axios调用接口，手动设置请求头
    const axios = (await import("axios")).default;
    const { baseUrl } = await import("@/api/config");
    const result = await axios({
      url: `${baseUrl}/api/v1/master/region/tree`,
      method: "get",
      params,
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // 处理响应数据
    const response = result.data;

    if (response && response.Code === 200 && response.data) {
      console.log("获取到省市区数据:", response.data);

      // 格式化数据为 value/label 格式
      const formattedData = formatRegionData(response.data);
      regionOptions.value = formattedData;

      console.log("格式化后的省市区数据:", formattedData);
    } else {
      console.error("获取省市区数据失败:", response);
    }
  } catch (error) {
    console.error("获取省市区数据失败:", error);
  }
};

// 格式化省市区数据为级联选择器所需格式
const formatRegionData = data => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 检查是否有子级数据
    const hasChildren =
      item.children && Array.isArray(item.children) && item.children.length > 0;

    return {
      value: item.code,
      label: item.name,
      // 只有子级数据存在时才递归处理
      children: hasChildren ? formatRegionData(item.children) : []
    };
  });
};

// 根据区域编码数组获取区域名称
const getRegionNames = (areaCodes) => {
  console.log('=== 开始获取区域名称 ===');
  console.log('输入的区域编码:', areaCodes);
  console.log('当前省市区数据:', regionOptions.value);

  if (!areaCodes || !Array.isArray(areaCodes) || areaCodes.length === 0) {
    console.log('区域编码为空，返回空字符串');
    return '';
  }


  const names = [];

  // 递归查找区域名称
  const findRegionName = (regions, code) => {
    console.log(`查找编码 ${code} 在区域:`, regions);

    for (const region of regions) {
      console.log(`检查区域: ${region.value} - ${region.label}`);

      if (region.value === code) {
        console.log(`找到匹配: ${code} -> ${region.label}`);
        return region.label;
      }

      if (region.children && region.children.length > 0) {
        const childName = findRegionName(region.children, code);
        if (childName) return childName;
      }
    }

    console.log(`未找到编码 ${code} 对应的名称`);
    return null;
  };

  // 遍历区域编码，获取对应名称
  for (const code of areaCodes) {
    console.log(`处理编码: ${code}`);
    const name = findRegionName(regionOptions.value, code);
    if (name) {
      console.log(`编码 ${code} 对应名称: ${name}`);
      names.push(name);
    } else {
      console.log(`编码 ${code} 未找到对应名称`);
    }
  }

  const result = names.join('/');
  console.log('最终结果:', result);
  console.log('=== 区域名称获取完成 ===');

  return result;
};

// 获取服务商详情信息
const fetchProviderInfo = async () => {
  loading.value = true;
  try {
    // 调用获取服务商详情接口
    const res = await providerAuth.getInfoDetail(JSON.parse(localStorage.getItem('user_provider')).id);
    
    if (res && res.code === 200 && res.data) {
      const { business_info } = res.data;
      console.log(business_info,'服务商详情');
      
      // 更新公司信息
      // 新增字段映射
      companyInfo.companyName = business_info.comname || '';
      companyInfo.legalRepresentative = business_info.legal_representative || '';
      companyInfo.creditCode = business_info.credit_code || '';
      companyInfo.registeredAddress = business_info.registered_address || '';
      companyInfo.businessScope = business_info.business_scope || '';
      companyInfo.idCardNumber = business_info.id_card_number || '';
      
      // 原有字段映射
      companyInfo.businessLicenseUrl = business_info.businessLicense || '';
      companyInfo.nameOperator=business_info.cardData.legalPerson || '';
      companyInfo.ratingCertificateUrl= business_info.rating || '';
      companyInfo.idCardFrontUrl = business_info.identity_card_portrait || '';
      companyInfo.idCardBackUrl = business_info.identity_card_national_emblem || '';
      companyInfo.taxLevel = business_info.grade || '';
      companyInfo.taxCertificateUrl = business_info.rating || '';
      companyInfo.address = business_info.comaddress || '';
      // companyInfo.legalRepresentative = business_info.cardData.legalPerson||''
      companyInfo.creditCode = business_info.cardData.registerNumber||''
      // 更新联系人信息
      companyInfo.contacts = business_info.contacts || [];
      
      // 更新财务信息
      companyInfo.finance = {
        bankName: business_info.bank || '',
        accountName: business_info.branch || '',
        accountNumber: business_info.bankNumber || '',
        proofUrl: business_info.license || ''
      };

      // 处理省市区数据
      if (business_info.area && Array.isArray(business_info.area)) {
        console.log('获取到的省市区编码:', business_info.area);
        // 等待省市区数据加载完成后再处理
        await nextTick();
        regionDisplay.value = getRegionNames(business_info.area);
        console.log('省市区显示文本:', regionDisplay.value);
      }
      
      console.log('获取服务商详情成功:', companyInfo);
    } else {
      Message.error('获取服务商详情失败');
    }
  } catch (error) {
    console.error('获取服务商详情异常:', error);
    Message.error('获取服务商详情异常');
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取服务商详情
onMounted(async () => {
  // 先加载省市区数据
  await fetchRegionTree();
  // 再获取服务商详情
  await fetchProviderInfo();
});


// 格式化图片URL，处理blob格式
const formatImageUrl = (url) => {
  if (!url) return '';
  
  // 如果是blob格式的URL，直接返回
  if (url.startsWith('blob:')) {
    return url;
  }
  
  // 如果是相对路径，添加基础路径
  if (url.startsWith('/')) {
    return url;
  }
  
  // 其他情况直接返回原始URL
  return url;
};

// 返回登录页
const goToLogin = () => {
  navigateTo('/provider/index');
};
</script>

<style lang="less" scoped>
.register-title-box {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.register-title {
  width: 768px;
  font-size: 20px;
  font-weight: 400;
  color: #333;
}

.review-status {
  padding: 10px 0;
  
  .status-icon {
    margin-bottom: 10px;
  }
  
  .estimated-time {
    display: inline-block;
    margin-top: 10px;
    padding: 6px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
}

.info-item {
  display: flex;
  margin-bottom: 20px;
  
  .info-label {
    width: 126px;
    font-weight: 500;
    color: #606266;
    flex-shrink: 0;
  }
  
  .info-content {
    flex: 1;
  }
}

.id-card-images {
  display: flex;
  gap: 20px;
  
  .id-card-item {
    .id-card-label {
      margin-bottom: 8px;
      font-size: 14px;
      color: #909399;
    }
  }
}

.no-image {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px dashed #d9d9d9;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
}

.contact-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: hidden;
  
  .contact-header {
    background-color: #f5f7fa;
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .contact-body {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .contact-info-item {
    .contact-info-label {
      color: #606266;
      margin-right: 8px;
    }
  }
}

.tax-level {
  display: inline-block;
  padding: 4px 8px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 14px;
}
</style>