<template>
  <div class="center-page">
    <div class="account-center-container">
      <!-- 账号信息卡片 -->
      <div class="info-card mb-4">
        <div class="info-card-header">
          <div class="info-card-title">账号信息</div>
          <div class="info-card-actions">
            <a-button type="primary" size="small" @click="editAccountInfo">
              <template #icon>
                <icon-edit />
              </template>
              修改账号信息
            </a-button>
            <a-button type="outline" status="warning" size="small" @click="resetPassword">
              <template #icon>
                <icon-lock />
              </template>
              重置密码
            </a-button>
          </div>
        </div>

        <div class="info-card-content">
          <a-descriptions
            :data="accountInfoData"
            layout="inline-vertical"
            :column="2"
            :label-style="{ 'font-weight': 'bold' }"
          />
        </div>
      </div>

      <!-- 企业信息卡片 -->
      <div class="info-section">
        <!-- <div class="info-section-title">公司及联系人信息</div> -->

        <!-- 基础信息 -->
        <div class="info-card mb-4">
          <div class="info-card-header">
            <div class="info-card-title">基础信息</div>
          </div>

          <div class="info-card-content">
            <a-descriptions
              :data="baseInfoData"
              layout="inline-vertical"
              :column="2"
              :label-style="{ 'font-weight': 'bold' }"
            />

            <div class="mt-4">
              <div class="image-group">
                <div class="image-item">
                  <div class="image-title">营业执照</div>
                  <div class="image-container">
                    <a-image
                      width="120"
                      height="120"
                      :src="companyInfo.businessLicense || '/assets/images/placeholder.png'"
                      fit="cover"
                    />
                  </div>
                </div>

                <div class="image-item">
                  <div class="image-title">评级证明</div>
                  <div class="image-container">
                    <a-image
                      width="120"
                      height="120"
                      :src="companyInfo.ratingCertificate || '/assets/images/placeholder.png'"
                      fit="cover"
                    />
                  </div>
                </div>

                <div class="image-item">
                  <div class="image-title">法人身份证</div>
                  <div class="image-container">
                    <a-image
                      width="120"
                      height="120"
                      :src="companyInfo.legalIdCard || '/assets/images/placeholder.png'"
                      fit="cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系人信息 -->
        <div class="info-card mb-4">
          <div class="info-card-header">
            <div class="info-card-title">联系人信息</div>
            <div class="info-card-actions">
              <a-button type="primary" size="small" @click="addContactInfo">
                <template #icon>
                  <icon-plus />
                </template>
                新增联系人
              </a-button>
            </div>
          </div>

          <div class="info-card-content">
            <a-table :columns="contactColumns" :data="contactData" :pagination="false">
              <template #contactOperations="{ record }">
                <a-button type="text" size="small" @click="editContactInfo(record)">
                  <template #icon>
                    <icon-edit />
                  </template>
                  修改
                </a-button>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="info-card mb-4">
          <div class="info-card-header">
            <div class="info-card-title">财务信息</div>
          </div>

          <div class="info-card-content">
            <a-table :columns="financeColumns" :data="financeData" :pagination="false">
              <template #bankProof="{ record }">
                <a-image
                  width="80"
                  height="80"
                  :src="record.bankProof || '/assets/images/placeholder.png'"
                  fit="cover"
                />
              </template>
              <template #financeOperations="{ record }">
                <a-button type="text" size="small" @click="editFinanceInfo(record)">
                  <template #icon>
                    <icon-edit />
                  </template>
                  修改
                </a-button>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 框架协议 -->
        <div class="info-card">
          <div class="info-card-header">
            <div class="info-card-title">框架协议</div>
            <div class="info-card-actions">
              <a-button type="primary" size="small" @click="showUploadAgreementDialog">
                <template #icon>
                  <icon-plus />
                </template>
                上传框架协议
              </a-button>
            </div>
          </div>

          <div class="info-card-content">
            <a-table
              :columns="agreementColumns"
              :data="agreementData"
              :pagination="{
                total: agreementTotal,
                current: agreementPagination.page,
                pageSize: agreementPagination.pageSize,
                showTotal: true,
                showJumper: true,
                showPageSize: true,
                pageSizeOptions: [10, 20, 50, 100]
              }"
              @page-change="handleAgreementPageChange"
              @page-size-change="handleAgreementPageSizeChange"
            >
              <template #operations="{ record }">
                <a-button type="text" size="small" @click="downloadAgreement(record)">
                  <template #icon>
                    <icon-download />
                  </template>
                  下载
                </a-button>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 上传框架协议对话框 -->
        <upload-agreement-dialog
          :visible="uploadAgreementDialogVisible"
          @update:visible="(val) => uploadAgreementDialogVisible = val"
          @upload-success="handleAgreementUploadSuccess"
        />
      </div>
    </div>

    <!-- 修改账号信息抽屉 -->
    <a-drawer
      :visible="accountDrawerVisible"
      @update:visible="(val) => accountDrawerVisible = val"
      title="修改账号信息"
      width="500px"
      :footer="true"
      unmountOnClose
    >
      <template #footer>
        <div class="drawer-footer">
          <a-button @click="accountDrawerVisible = false" class="mr-2">取消</a-button>
          <a-button type="primary" @click="saveAccountInfo">保存</a-button>
        </div>
      </template>

      <a-form ref="accountFormRef" :model="accountForm" auto-label-width>
        <a-form-item
          field="accountName"
          label="账号名"
          :rules="[{ required: true, message: '请输入账号名' }]"
        >
          <a-input v-model="accountForm.accountName" placeholder="请输入账号名" :disabled="true" />
        </a-form-item>
        <!-- <a-form-item field="nickname" label="昵称" :rules="[{ required: true, message: '请输入昵称' }]">
          <a-input v-model="accountForm.nickname" placeholder="请输入昵称" />
        </a-form-item>-->
        <a-form-item field="phone" label="手机" :rules="[{ required: true, message: '请输入手机号' }]">
          <a-input v-model="accountForm.phone" placeholder="请输入手机号" />
        </a-form-item>
        <!-- <a-form-item field="email" label="邮箱">
          <a-input v-model="accountForm.email" placeholder="请输入邮箱" />
        </a-form-item>-->
      </a-form>
    </a-drawer>

    <!-- 重置密码抽屉 -->
    <a-drawer
      :visible="passwordDrawerVisible"
      @update:visible="(val) => passwordDrawerVisible = val"
      title="重置密码"
      width="500px"
      :footer="true"
      unmountOnClose
    >
      <template #footer>
        <div class="drawer-footer">
          <a-button @click="passwordDrawerVisible = false" class="mr-2">取消</a-button>
          <a-button type="primary" @click="saveNewPassword">保存</a-button>
        </div>
      </template>

      <a-form ref="passwordFormRef" :model="passwordForm" auto-label-width>
        <a-form-item
          field="oldPassword"
          label="原密码"
          :rules="[{ required: true, message: '请输入原密码' }]"
        >
          <a-input-password v-model="passwordForm.oldPassword" placeholder="请输入原密码" />
        </a-form-item>
        <a-form-item
          field="newPassword"
          label="新密码"
          :rules="[{ required: true, message: '请输入新密码' }]"
        >
          <a-input-password v-model="passwordForm.newPassword" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item
          field="confirmPassword"
          label="确认密码"
          :rules="[{ required: true, message: '请确认新密码' }, { validator: validateConfirmPassword }]"
        >
          <a-input-password v-model="passwordForm.confirmPassword" placeholder="请确认新密码" />
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 修改联系人信息弹窗 -->
    <a-modal
      :visible="contactDrawerVisible"
      @update:visible="(val) => contactDrawerVisible = val"
      :title="contactFormMode === 'add' ? '新增联系人信息' : '修改联系人信息'"
      width="500px"
      :footer="false"
      unmountOnClose
    >
      <a-form ref="contactFormRef" :model="contactForm" layout="vertical">
        <a-form-item
          field="contactName"
          label="联系人名称"
          :rules="[{ required: true, message: '请输入联系人名称' }]"
        >
          <a-input v-model="contactForm.contactName" placeholder="请输入联系人名称" />
        </a-form-item>
        <a-form-item
          field="contactPhone"
          label="电话"
          :rules="[{ required: true, message: '请输入电话' }]"
        >
          <a-input v-model="contactForm.contactPhone" placeholder="请输入电话" />
        </a-form-item>
        <a-form-item field="contactPosition" label="职务"     :rules="[{ required: true, message: '请输入职务' }]">
          <a-input v-model="contactForm.contactPosition" placeholder="请输入职务" />
        </a-form-item>
        <a-form-item field="contactEmail" label="邮箱">
          <a-input v-model="contactForm.contactEmail" placeholder="请输入邮箱" />
        </a-form-item>
      </a-form>
      <div class="dialog-footer">
        <a-button @click="contactDrawerVisible = false" class="mr-2">取消</a-button>
        <a-button type="primary" @click="saveContactInfo">保存</a-button>
      </div>
    </a-modal>

    <!-- 修改财务信息弹窗 -->
    <a-modal
      :visible="financeDrawerVisible"
      @update:visible="(val) => financeDrawerVisible = val"
      title="修改财务信息"
      width="500px"
      :footer="false"
      unmountOnClose
    >
      <a-form ref="financeFormRef" :model="financeForm" layout="vertical">
        <a-form-item
          field="bankName"
          label="开户银行"
          :rules="[{ required: true, message: '请输入开户银行' }]"
        >
          <a-input v-model="financeForm.bankName" placeholder="请输入开户银行" />
        </a-form-item>
        <a-form-item
          field="bankAccount"
          label="银行账号"
          :rules="[{ required: true, message: '请输入银行账号' }]"
        >
          <a-input v-model="financeForm.bankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        <a-form-item field="bankBranch" label="开户网点"      :rules="[{ required: true, message: '请输入开户银行' }]">
          <a-input v-model="financeForm.bankBranch" placeholder="请输入开户网点" />
        </a-form-item>
        <a-form-item field="bankProof" label="开户证明">
          <div class="upload-container">
            <a-upload
              :custom-request="handleBankProofUpload"
              list-type="picture-card"
              :file-list="[]"
              :show-upload-button="!financeForm.bankProof"
              :auto-upload="true"
              accept=".jpg, .jpeg, .png, .gif"
            >
              <template #upload-button>
                <div class="upload-button">
                  <icon-plus />
                  <div>上传图片</div>
                </div>
              </template>
            </a-upload>
            <div class="upload-preview" v-if="financeForm.bankProof">
              <div class="image-container">
                <a-image
                  width="100"
                  height="100"
                  :src="financeForm.bankProof || '/assets/images/placeholder.png'"
                  fit="cover"
                />
                <div class="image-actions">
                  <a-button type="text" status="danger" size="mini" @click="deleteBankProof">
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-form>
      <div class="dialog-footer">
        <a-button @click="financeDrawerVisible = false" class="mr-2">取消</a-button>
        <a-button type="primary" @click="saveFinanceInfo">保存</a-button>
      </div>
    </a-modal>

    <!-- 上传框架协议对话框 -->
    <upload-agreement-dialog
      v-model:visible="uploadAgreementDialogVisible"
      @upload-success="handleAgreementUploadSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import providerSystemApi from "@/api/provider/system";
import commonApi from "@/api/common";
import UploadAgreementDialog from "./UploadAgreementDialog.vue";

// 定义页面元数据
definePageMeta({
  name: "provider-system-center",
  path: "/provider/system/center",
  title: "个人中心"
});

// 移除ma-crud相关配置

// 账号信息数据
const accountInfo = reactive({
  accountName: "",
  nickname: "",
  phone: "",
  email: "",
  createdTime: "",
  createdIp: "",
  lastLoginTime: "",
  lastLoginIp: ""
});

// 从本地存储获取用户信息
const getUserInfoFromStorage = () => {
  try {
    // 从本地存储中获取用户信息
    const userInfoStr = localStorage.getItem("user_provider");
    if (!userInfoStr) {
      Message.error("未找到用户信息，请重新登录");
      return null;
    }

    // 解析用户信息
    const userInfo = JSON.parse(userInfoStr);
    if (!userInfo || !userInfo.id) {
      Message.error("用户信息不完整，请重新登录");
      return null;
    }

    return userInfo;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    Message.error("获取用户信息失败，请重新登录");
    return null;
  }
};

// 获取账号信息
const fetchAccountInfo = async () => {
  try {
    // 从本地存储获取用户ID
    const userInfo = getUserInfoFromStorage();
    if (!userInfo) return;

    // 调用接口获取账号信息
    const result = await providerSystemApi.user.getAccountInfo(userInfo.id);

    if (result && result.code === 200 && result.data) {
      console.log(result.data, "result.data");
      // 更新账号信息
      accountInfo.accountName = result.data.username || "";
      accountInfo.phone = result.data.phone || "";

      // 格式化时间
      if (result.data.createdAt) {
        // 时间戳是秒级，需要乘以1000转换为毫秒
        const createdDate = new Date(parseInt(result.data.createdAt) * 1000);
        accountInfo.createdTime = createdDate.toLocaleString("zh-CN");
      }

      if (result.data.lastLoginTime) {
        // 时间戳是秒级，需要乘以1000转换为毫秒
        const lastLoginDate = new Date(parseInt(result.data.lastLoginTime) * 1000);
        accountInfo.lastLoginTime = lastLoginDate.toLocaleString("zh-CN");
      }

      // 设置登录IP
      accountInfo.lastLoginIp = result.data.lastLoginIp || "-";
    } else {
      Message.error(result?.message || "获取账号信息失败");
    }
  } catch (error) {
    console.error("获取账号信息失败:", error);
    Message.error("获取账号信息失败，请稍后再试");
  }
};

// 企业信息数据
const companyInfo = reactive({
  // 基础信息
  companyName: "广东八零科技发展有限公司",
  legalRepresentative: "刘志全",
  creditCode: "91110000123456789X",
  registeredAddress: "番禺区海珠街道路140-214号五洲城C座2003、2005-2006房",
  officeAddress: "番禺区海珠街道路140-214号五洲城C座2003、2005-2006房",
  businessScope:
    "电子产品、计算机软硬件、通讯设备的技术开发、技术咨询、技术服务、销售",
  businessLicense: "/assets/images/placeholder.png",
  ratingCertificate: "/assets/images/placeholder.png",
  legalIdCard: "/assets/images/placeholder.png",

  // 联系人信息
  contactName: "吴锦标",
  contactPhone: "***********",
  contactPosition: "B端业务负责人",
  contactEmail: "<EMAIL>",

  // 财务信息
  bankProof: "/assets/images/placeholder.png",
  bankName: "中国银行",
  bankAccount: "6222 0000 0000 0000 000",
  bankBranch: "广州分行"
});

// 框架协议数据
const agreementData = ref([]);
// 框架协议总数
const agreementTotal = ref(0);
// 框架协议分页参数
const agreementPagination = reactive({
  page: 1,
  pageSize: 10
});
let baseInfoData = reactive([]);
// 框架协议表格列配置
const agreementColumns = [
  {
    title: "上传时间",
    dataIndex: "createdAt",
    width: 180,
    render: ({ record }) => {
      // 将时间戳转换为可读时间
      const timestamp = parseInt(record.createdAt) * 1000;
      return new Date(timestamp).toLocaleString("zh-CN");
    }
  },
  {
    title: "快递公司",
    dataIndex: "expressCompany",
    width: 120
  },
  {
    title: "快递单号",
    dataIndex: "expressNo",
    width: 150
  },
  {
    title: "协议文件名",
    dataIndex: "fileName",
    width: 200
  },
  {
    title: "操作",
    dataIndex: "operations",
    slotName: "operations",
    width: 100
  }
];

// 账号信息描述列表数据 - 使用计算属性确保响应式更新
const accountInfoData = computed(() => [
  {
    label: "账号名",
    value: accountInfo.accountName
  },
  {
    label: "手机",
    value: accountInfo.phone
  },
  {
    label: "创建时间",
    value: accountInfo.createdTime
  },
  {
    label: "最后登录时间",
    value: accountInfo.lastLoginTime
  },
  {
    label: "登录IP",
    value: accountInfo.lastLoginIp
  }
]);

// 联系人信息表格数据
const contactData = ref([
  {
    id: "1",
    contactName: companyInfo.contactName,
    contactPhone: companyInfo.contactPhone,
    contactPosition: companyInfo.contactPosition,
    contactEmail: companyInfo.contactEmail
  }
]);

// 联系人信息表格列配置
const contactColumns = [
  {
    title: "联系人名称",
    dataIndex: "contactName",
    width: 150
  },
  {
    title: "电话",
    dataIndex: "contactPhone",
    width: 150
  },
  {
    title: "职务",
    dataIndex: "contactPosition",
    width: 150
  },
  {
    title: "邮箱",
    dataIndex: "contactEmail",
    width: 200
  },
  {
    title: "操作",
    dataIndex: "operations",
    slotName: "contactOperations",
    width: 100,
    fixed: "right"
  }
];

// 财务信息表格数据
const financeData = ref([
  {
    id: "1",
    bankName: companyInfo.bankName,
    bankAccount: companyInfo.bankAccount,
    bankBranch: companyInfo.bankBranch,
    bankProof: companyInfo.bankProof
  }
]);

// 财务信息表格列配置
const financeColumns = [
  {
    title: "开户银行",
    dataIndex: "bankName",
    width: 150
  },
  {
    title: "银行账号",
    dataIndex: "bankAccount",
    width: 150
  },
  {
    title: "开户网点",
    dataIndex: "bankBranch",
    width: 150
  },
  {
    title: "开户证明",
    dataIndex: "bankProof",
    slotName: "bankProof",
    width: 200
  },
  {
    title: "操作",
    dataIndex: "operations",
    slotName: "financeOperations",
    width: 100,
    fixed: "right"
  }
];

// 账号信息抽屉控制
const accountDrawerVisible = ref(false);
const accountFormRef = ref(null);
const accountForm = reactive({
  accountName: "",
  nickname: "",
  phone: "",
  email: ""
});

// 密码抽屉控制
const passwordDrawerVisible = ref(false);
const passwordFormRef = ref(null);
const passwordForm = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});

// 联系人信息抽屉控制
const contactDrawerVisible = ref(false);
const contactFormRef = ref(null);
const contactFormMode = ref("edit"); // 'add' 或 'edit'
const currentContactId = ref(null);
const contactForm = reactive({
  contactName: "",
  contactPhone: "",
  contactPosition: "",
  contactEmail: ""
});

// 财务信息抽屉控制
const financeDrawerVisible = ref(false);
const financeFormRef = ref(null);
const financeForm = reactive({
  bankName: "",
  bankAccount: "",
  bankBranch: "",
  bankProof: ""
});

// 上传框架协议对话框控制
const uploadAgreementDialogVisible = ref(false);

// 确认密码验证函数
const validateConfirmPassword = (value, cb) => {
  if (value !== passwordForm.newPassword) {
    return cb("两次输入的密码不一致");
  }
  return cb();
};

// 编辑账号信息
function editAccountInfo() {
  // 填充表单数据
  accountForm.accountName = accountInfo.accountName;
  accountForm.nickname = accountInfo.nickname;
  accountForm.phone = accountInfo.phone;
  accountForm.email = accountInfo.email;

  // 显示抽屉
  accountDrawerVisible.value = true;
}

// 新增联系人信息
function addContactInfo() {
  // 重置表单数据
  contactForm.contactName = "";
  contactForm.contactPhone = "";
  contactForm.contactPosition = "";
  contactForm.contactEmail = "";

  // 设置当前操作为新增
  contactFormMode.value = "add";

  // 显示抽屉
  contactDrawerVisible.value = true;
}

// 编辑联系人信息
function editContactInfo(record) {
  // 填充表单数据
  contactForm.contactName = record.contactName;
  contactForm.contactPhone = record.contactPhone;
  contactForm.contactPosition = record.contactPosition;
  contactForm.contactEmail = record.contactEmail;

  // 设置当前操作为编辑
  contactFormMode.value = "edit";
  // 保存当前编辑的记录ID
  currentContactId.value = record.id;

  // 显示抽屉
  contactDrawerVisible.value = true;
}

// 编辑财务信息
function editFinanceInfo(record) {
  // 填充表单数据
  financeForm.bankName = record.bankName;
  financeForm.bankAccount = record.bankAccount;
  financeForm.bankBranch = record.bankBranch;
  financeForm.bankProof = record.bankProof;

  // 显示抽屉
  financeDrawerVisible.value = true;
}

// 保存账号信息
async function saveAccountInfo() {
  // 表单验证
  try {
    if (!accountFormRef.value) {
      console.warn("表单引用不存在");
      return;
    }

    // 校验手机号
    if (!accountForm.phone) {
      Message.error("请输入手机号");
      return;
    }
    // 校验手机号是否合法
    if (!/^1[3-9]\d{9}$/.test(accountForm.phone)) {
      Message.error("请输入正确的手机号");
      return;
    }
    // 校验邮箱
    // if (!accountForm.email) {
    //   Message.error('请输入邮箱')
    //   return
    // }
    // 校验邮箱是否合法
    // if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(accountForm.email)) {
    //   Message.error('请输入正确的邮箱')
    //   return
    // }

    // 安全地获取验证方法
    const formInstance = accountFormRef.value;
    if (typeof formInstance.validate !== "function") {
      console.warn("表单验证方法不存在");
      // 直接使用表单数据进行API调用
      await updateUserAccountInfo();
      return;
    }

    // 表单验证
    await formInstance.validate();

    // 调用API更新账号信息
    await updateUserAccountInfo();
  } catch (error) {
    console.error("表单验证错误:", error);
    Message.error("保存失败，请检查表单内容");
  }
}

// 调用API更新账号信息
async function updateUserAccountInfo() {
  try {
    // 从本地存储获取用户ID
    const userInfo = getUserInfoFromStorage();
    if (!userInfo) return;

    // 准备请求数据
    const requestData = {
      username: accountForm.accountName,
      phone: accountForm.phone
    };

    // 调用接口更新账号信息
    const result = await providerSystemApi.user.updateAccountInfo(
      userInfo.id,
      requestData
    );

    if (result && result.code === 200) {
      // 更新本地数据
      accountInfo.accountName = accountForm.accountName;
      accountInfo.phone = accountForm.phone;

      // 更新描述列表数据
      const accountInfoDataValue = accountInfoData.value || accountInfoData;
      if (Array.isArray(accountInfoDataValue)) {
        const nameItem = accountInfoDataValue.find(
          item => item.label === "账号名"
        );
        const phoneItem = accountInfoDataValue.find(
          item => item.label === "手机"
        );

        if (nameItem) nameItem.value = accountInfo.accountName;
        if (phoneItem) phoneItem.value = accountInfo.phone;
      }

      // 关闭抽屉
      accountDrawerVisible.value = false;

      // 显示成功提示
      Message.success(result.message || "账号信息已更新");
    } else {
      // 显示错误提示
      Message.error(result?.message || "账号信息更新失败");
    }
  } catch (error) {
    console.error("更新账号信息失败:", error);
    Message.error("账号信息更新失败，请稍后再试");
  }
}

// 重置密码
function resetPassword() {
  // 重置表单数据
  passwordForm.oldPassword = "";
  passwordForm.newPassword = "";
  passwordForm.confirmPassword = "";

  // 显示抽屉
  passwordDrawerVisible.value = true;
}

// 保存新密码
async function saveNewPassword() {
  // 表单验证
  try {
    if (!passwordFormRef.value) {
      console.warn("密码表单引用不存在");
      return;
    }

    // 安全地获取验证方法
    const formInstance = passwordFormRef.value;
    if (typeof formInstance.validate !== "function") {
      console.warn("密码表单验证方法不存在");

      // 检查密码一致性
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        Message.error("两次输入的密码不一致");
        return;
      }

      // 直接调用API
      try {
        const result = await providerSystemApi.user.resetPassword({
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword,
          confirmPassword: passwordForm.confirmPassword
        });

        if (result && result.code === 200) {
          // 关闭抽屉
          passwordDrawerVisible.value = false;

          // 显示成功提示
          Message.success(result.message || "密码已重置");
        } else {
          // 显示错误提示
          Message.error(result?.message || "密码重置失败");
        }
      } catch (apiError) {
        console.error("重置密码接口调用错误:", apiError);
        Message.error("密码重置失败，请稍后再试");
      }

      return;
    }

    // 正常验证流程
    await formInstance.validate();

    // 调用重置密码接口
    try {
      const result = await providerSystemApi.user.resetPassword({
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      });

      if (result && result.code === 200) {
        // 关闭抽屉
        passwordDrawerVisible.value = false;

        // 显示成功提示
        Message.success(result.message || "密码已重置");
      } else {
        // 显示错误提示
        Message.error(result?.message || "密码重置失败");
      }
    } catch (apiError) {
      console.error("重置密码接口调用错误:", apiError);
      Message.error("密码重置失败，请稍后再试");
    }
  } catch (error) {
    console.error("表单验证错误:", error);
  }
}

// 保存联系人信息
async function saveContactInfo() {
  try {
    if (!contactFormRef.value) {
      console.warn("联系人表单引用不存在");
      return;
    }

    // 表单验证
    await contactFormRef.value.validate();

    // 从本地存储获取用户ID
    const userInfo = getUserInfoFromStorage();
    if (!userInfo) {
      return;
    }

    // 构建联系人数据
    const contactItem = {
      position: contactForm.contactPosition,
      name: contactForm.contactName,
      phone: contactForm.contactPhone,
      email: contactForm.contactEmail || ""
    };

    // 构建请求参数
    const requestData = {
      business_info: {
        contacts: [contactItem]
      },
      user_id: userInfo.id
    };

    // 显示加载中状态
    const loadingInstance = Message.loading({
      content: "正在保存联系人信息...",
      duration: 0
    });

    try {
      // 根据模式选择不同的API接口
      let result;
      if (contactFormMode.value === "add") {
        // 新增联系人
        result = await providerSystemApi.userInfo.addContacts(requestData);
      } else {
        // 编辑联系人
        // 需要将所有联系人数据一起传递给后端
        const allContacts = [];

        // 先遍历当前所有联系人数据
        if (contactData.value && contactData.value.length > 0) {
          contactData.value.forEach(item => {
            // 跳过当前编辑的联系人
            if (item.id !== currentContactId.value) {
              allContacts.push({
                position: item.contactPosition,
                name: item.contactName,
                phone: item.contactPhone,
                email: item.contactEmail || ""
              });
            }
          });
        }

        // 添加当前编辑的联系人
        allContacts.push(contactItem);

        // 更新请求参数
        requestData.business_info.contacts = allContacts;

        result = await providerSystemApi.userInfo.updateContacts(requestData);
      }

      // 关闭加载提示
      loadingInstance.close();

      if (result && result.code === 200) {
        // 更新本地联系人数据
        if (result.data && result.data.contacts) {
          // 清空原有数据
          contactData.value = [];

          // 添加新数据
          result.data.contacts.forEach((contact, index) => {
            contactData.value.push({
              id: String(index + 1),
              contactName: contact.name,
              contactPhone: contact.phone,
              contactPosition: contact.position,
              contactEmail: contact.email || ""
            });
          });
        }
        fetchProviderDetail();
        // 显示成功提示
        const successMsg =
          contactFormMode.value === "add"
            ? "联系人信息已添加"
            : "联系人信息已更新";
        Message.success(result.message || successMsg);

        // 关闭抽屉
        contactDrawerVisible.value = false;
      } else {
        const errorMsg =
          contactFormMode.value === "add" ? "添加联系人失败" : "更新联系人失败";
        Message.error(result?.message || errorMsg);
      }
    } catch (error) {
      // 关闭加载提示
      loadingInstance.close();
      const errorMsg =
        contactFormMode.value === "add" ? "添加联系人失败" : "更新联系人失败";
      console.error(`${errorMsg}:`, error);
      Message.error(`${errorMsg}，请重试`);
    }
  } catch (error) {
    console.error("表单验证错误:", error);
  }
}

// 保存财务信息
async function saveFinanceInfo() {
  try {
    if (!financeFormRef.value) {
      console.warn("财务表单引用不存在");
      return;
    }

    // 表单验证
    await financeFormRef.value.validate();

    // 从本地存储获取用户信息
    const userInfoStr = localStorage.getItem("user_provider");
    if (!userInfoStr) {
      Message.error("未找到用户信息，请重新登录");
      return;
    }

    const userInfo = JSON.parse(userInfoStr);
    if (!userInfo || !userInfo.id) {
      Message.error("用户信息不完整，请重新登录");
      return;
    }

    // 构建请求参数
    const requestData = {
      business_info: {
        bank: financeForm.bankName,
        branch: financeForm.bankBranch,
        bankNumber: financeForm.bankAccount,
        license: financeForm.bankProof
      },
      user_id: userInfo.id
    };

    // 调用API更新财务信息
    const result = await providerSystemApi.userInfo.updateFinance(requestData);

    if (result && result.code === 200) {
      // 更新本地数据
      financeData.value[0].bankName = financeForm.bankName;
      financeData.value[0].bankAccount = financeForm.bankAccount;
      financeData.value[0].bankBranch = financeForm.bankBranch;
      financeData.value[0].bankProof = financeForm.bankProof;

      // 关闭抽屉
      financeDrawerVisible.value = false;

      // 显示成功提示
      Message.success(result.message || "财务信息已更新");
    } else {
      Message.error(result?.message || "更新财务信息失败");
    }
  } catch (error) {
    console.error("保存财务信息失败:", error);
    Message.error("保存财务信息失败，请重试");
  }
}

// 处理银行开户证明上传
async function handleBankProofUpload(options) {
  try {
    const { fileItem } = options;
    if (!fileItem || !fileItem.file) {
      Message.error("无效的文件");
      return;
    }

    // 创建FormData
    const formData = new FormData();
    formData.append("file", fileItem.file);

    // 显示上传中状态
    const loadingInstance = Message.loading({
      content: "正在上传开户证明...",
      duration: 0
    });

    // 调用上传接口
    const result = await commonApi.uploadImage(formData);

    // 关闭加载提示
    loadingInstance.close();
    // debugger
    if (result && result.code === 200) {
      // 使用返回的URL更新表单
      financeForm.bankProof = result.data?.fileUrl || "";
      Message.success("开户证明上传成功");
      return {
        success: true,
        url: result.data?.url
      };
    } else {
      Message.error(result?.message || "开户证明上传失败");
      return {
        success: false,
        message: result?.message || "上传失败"
      };
    }
  } catch (error) {
    console.error("开户证明上传失败:", error);
    Message.error("开户证明上传失败，请重试");
    return {
      success: false,
      message: "上传失败，请重试"
    };
  }
}

// 删除银行开户证明图片
function deleteBankProof() {
  // 实际项目中应该调用API删除服务器上的图片
  financeForm.bankProof = "";
  Message.success("开户证明已删除");
}

// 显示上传框架协议对话框
function showUploadAgreementDialog() {
  uploadAgreementDialogVisible.value = true;
}

// 处理框架协议上传成功
function handleAgreementUploadSuccess(agreement) {
  fetchAgreementList();
  // 添加到列表头部
  // agreementData.value.unshift(agreement)
}

// 下载框架协议
function downloadAgreement(record) {
  try {
    // 检查是否有文件URL
    if (!record.fileUrl) {
      Message.error("文件URL不存在，无法下载");
      return;
    }

    // 使用window.open在新标签页中打开文件URL
    const newWindow = window.open(record.fileUrl, "_blank");

    // 如果新窗口被阻止，提示用户
    if (
      !newWindow ||
      newWindow.closed ||
      typeof newWindow.closed === "undefined"
    ) {
      Message.error("浏览器阻止了弹出窗口，请允许弹出窗口后重试");
      return;
    }

    // 显示成功消息
    Message.success(
      `已在新标签页打开文件: ${record.fileName || "框架协议文件"}`
    );
  } catch (error) {
    console.error("下载框架协议失败:", error);
    Message.error("下载失败，请稍后再试");
  }
}

// 处理框架协议分页变化
function handleAgreementPageChange(page) {
  agreementPagination.page = page;
  fetchAgreementList();
}

// 处理框架协议每页条数变化
function handleAgreementPageSizeChange(pageSize) {
  agreementPagination.pageSize = pageSize;
  agreementPagination.page = 1; // 重置到第一页
  fetchAgreementList();
}

// 获取框架协议列表
async function fetchAgreementList() {
  try {
    // 从本地存储获取用户ID
    const userInfo = JSON.parse(localStorage.getItem("user_provider"));
    if (!userInfo || !userInfo.id) {
      console.warn("未找到用户信息，无法获取框架协议列表");
      return;
    }

    // 构建请求参数，使用动态分页参数
    const params = {
      page: agreementPagination.page,
      pageSize: agreementPagination.pageSize,
      user_id: userInfo.id
    };

    // 调用接口获取列表数据
    const result = await providerSystemApi.agreement.getList(params);

    if (result && result.code === 200) {
      // 更新列表数据
      agreementData.value = result.data?.items || [];
      // 更新总数
      agreementTotal.value = result.data?.pageInfo?.total || 0;
    } else {
      console.warn("获取框架协议列表失败:", result?.message);
    }
  } catch (error) {
    console.error("获取框架协议列表失败:", error);
  }
}

// 获取服务商详细信息
async function fetchProviderDetail() {
  try {
    // 从本地存储获取用户ID
    const userInfo = getUserInfoFromStorage();
    if (!userInfo) {
      return;
    }

    // 调用接口获取详细信息
    const result = await providerSystemApi.userInfo.getDetail(userInfo.id);

    if (result && result.code === 200) {
      const detailData = result.data;

      // 更新基础信息
      if (detailData.business_info) {
        const businessInfo = detailData.business_info;
        const cardData = businessInfo.cardData || {};

        // 更新公司基础信息
        companyInfo.companyName = businessInfo.comname || "";
        companyInfo.legalRepresentative =
          businessInfo.legal_representative || cardData.legalPerson || "";
        companyInfo.creditCode =
          businessInfo.unified_social_credit_code ||
          cardData.registerNumber ||
          "";
        companyInfo.registeredAddress =
          businessInfo.registered_address || cardData.address || "";
        companyInfo.officeAddress = businessInfo.comaddress || "";
        companyInfo.businessScope =
          businessInfo.business_scope || cardData.business || "";

        // 更新证件图片
        companyInfo.businessLicense = businessInfo.businessLicense || "";
        companyInfo.ratingCertificate = businessInfo.rating || "";
        companyInfo.legalIdCard = businessInfo.identity_card_portrait || "";

        baseInfoData = [
          {
            label: "公司名称",
            value: companyInfo.companyName
          },
          {
            label: "法定代表人",
            value: companyInfo.legalRepresentative
          },
          {
            label: "统一社会信用代码",
            value: companyInfo.creditCode
          },
          {
            label: "注册地址",
            value: companyInfo.registeredAddress
          },
          {
            label: "办公地址",
            value: companyInfo.officeAddress
          },
          {
            label: "经营范围",
            value: companyInfo.businessScope
          }
        ];
      }

      // 更新联系人信息
      if (
        detailData.business_info &&
        detailData.business_info.contacts &&
        Array.isArray(detailData.business_info.contacts)
      ) {
        // 清空原有数据
        contactData.value = [];

        // 添加新数据
        detailData.business_info.contacts.forEach((contact, index) => {
          contactData.value.push({
            id: String(index + 1),
            contactName: contact.name,
            contactPhone: contact.phone,
            contactPosition: contact.position,
            contactEmail: contact.email || ""
          });
        });
      }

      // 更新财务信息
      if (detailData.business_info) {
        const businessInfo = detailData.business_info;

        financeData.value = [
          {
            id: "1",
            bankName: businessInfo.bank || "",
            bankAccount: String(businessInfo.bankNumber || ""), // 转换为字符串避免类型错误
            bankBranch: businessInfo.branch || "",
            bankProof: businessInfo.license || ""
          }
        ];

        // 同步更新表单数据
        financeForm.bankName = businessInfo.bank || "";
        financeForm.bankAccount = String(businessInfo.bankNumber || "");
        financeForm.bankBranch = businessInfo.branch || "";
        financeForm.bankProof = businessInfo.license || "";
      }

      // 打印接口返回的数据，便于调试
      console.log("服务商详细信息获取成功:", detailData);
    } else {
      console.warn("获取服务商详细信息失败:", result?.message);
    }
  } catch (error) {
    console.error("获取服务商详细信息失败:", error);
  }
}
console.log(companyInfo, "companyInfo");
// 基础信息描述列表数据

console.log(baseInfoData, "baseInfoData");
// 页面加载时获取数据
onMounted(() => {
  // 获取账号信息
  fetchAccountInfo();

  // 获取服务商详细信息
  fetchProviderDetail();

  // 获取框架协议列表
  fetchAgreementList();

  // 获取其他数据
  // 当前使用的是模拟数据
});
</script>

<style lang="less" scoped>
.center-page {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.account-center-container {
  max-width: 100%;
  margin: 0 auto;
}

.info-section {
  margin-bottom: 24px;

  &-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }
}

.info-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 16px;

  &-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .info-card-actions {
      display: flex;
      gap: 8px;
    }
  }

  &-content {
    padding: 20px;
  }
}

/* 描述列表样式覆盖 */
:deep(.arco-descriptions) {
  width: 100%;
}

:deep(.arco-descriptions-item) {
  width: 50%;
  vertical-align: top;
  padding-bottom: 12px;
}

:deep(.arco-descriptions-item-label) {
  color: #86909c;
  font-weight: bold;
  width: 100px;
  text-align: right;
  padding-right: 12px;
}

:deep(.arco-descriptions-item-value) {
  color: #1d2129;
  text-align: left;
}

.image-group {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 16px;

  .image-item {
    text-align: left;

    .image-title {
      font-size: 14px;
      color: #4e5969;
      margin-bottom: 8px;
      font-weight: bold;
    }

    .image-container {
      border: 1px solid #e5e6eb;
      border-radius: 4px;

      padding: 4px;
      display: inline-block;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.table-header {
  background-color: #fafafa;
}

:deep(.arco-table-th) {
  background-color: #f2f3f5;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  width: 100px;
  height: 100px;
  text-align: center;
  border: 1px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.upload-preview {
  .image-container {
    position: relative;
    width: 100px;
    height: 100px;
  }
}

.image-actions {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 0 0 0 4px;
}
</style>