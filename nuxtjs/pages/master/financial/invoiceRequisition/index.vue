<template>
  <div class="ma-content-block p-4">
    <!-- 状态卡片 -->
    <div class="stats-cards-container">
      <div
        class="stats-scroll-btn stats-scroll-left"
        @click="scrollStatsCards('left')"
      >
        <icon-left />
      </div>
      <div class="stats-cards" ref="statsCardsRef">
        <div
          v-for="card in statusCards"
          :key="card.key"
          class="stats-card"
          :class="{ active: currentStatusCard === card.key }"
          @click="handleStatusCardClick(card.key)"
        >
          <div class="stats-corner-mark" v-if="currentStatusCard === card.key">
            <icon-check class="check-icon" />
          </div>
          <div class="stats-title">{{ card.title }}</div>
          <div class="stats-value">{{ card.count }}</div>
        </div>
      </div>
      <div
        class="stats-scroll-btn stats-scroll-right"
        @click="scrollStatsCards('right')"
      >
        <icon-right />
      </div>
    </div>

    <ma-crud
      :key="crudKey"
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
    >
      <!-- 自定义状态搜索字段 -->
      <template #search-status="{ searchForm, component }">
        <a-select
          v-model="searchForm.status"
          :placeholder="`请选择${component.title}`"
          allow-clear
          :options="statusOptions"
          @change="handleStatusSearchChange"
        />
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space @click.stop>
          <a-button
            type="primary"
            size="small"
            :disabled="record.status !== 0"
            @click.stop="handleInvoiceApplication(record)"
          >
            开票审核
          </a-button>
          <a-button
            type="outline"
            size="small"
            :disabled="record.status !== 1"
            @click.stop="handleUploadInvoice(record)"
          >
            上传发票
          </a-button>
          <a-button
            type="outline"
            size="small"
            :disabled="record.status !== 3"
            @click.stop="handleViewInvoice(record)"
          >
            查看发票
          </a-button>
          <!-- <a-button
            type="outline"
            size="small"
            @click.stop="handleVoidApplication(record)"
          >
            作废审核
          </a-button> -->
          <!-- <a-button
            v-if="record.orderSource === '君网BG'"
            type="text"
            size="small"
            @click.stop="handleBGAuditRecord(record)"
          >
            BG审核记录
          </a-button>
          <a-button
            v-else
            type="text"
            size="small"
            @click.stop="handleBangWAuditRecord(record)"
          >
            邦W审核记录
          </a-button> -->
          <!-- <a-button
            type="text"
            size="small"
            @click.stop="handleAuditRecord(record)"
          >
            审核记录
          </a-button> -->
        </a-space>
      </template>
      <!-- 申请状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ InvoiceAuditStatusLabels[record.status] || record.statusLabel }}
        </a-tag>
      </template>
      <!-- 发票抬头 -->
      <template #invoiceHeader="{ record }">
        {{ record.invoiceHeader?.name || "-" }}
      </template>
      <!-- 数据来源 -->
      <template #sourceType="{ record }">
        {{ SourceTypeLabels[record.sourceType] || record.sourceTypeLabel }}
      </template>
      <!-- 申请金额 -->
      <template #totalAmount="{ record }">
        ¥{{ (record.totalAmount || 0).toFixed(2) }}
      </template>
      <!-- 税额 -->
      <template #taxAmount="{ record }">
        ¥{{ (record.taxAmount || 0).toFixed(2) }}
      </template>
      <!-- 不含税金额 -->
      <template #untaxedAmount="{ record }">
        ¥{{ (record.untaxedAmount || 0).toFixed(2) }}
      </template>
      <!-- 创建时间 -->
      <template #createdAt="{ record }">
        {{ formatAuditTime(record.createdAt) }}
      </template>
      <!-- 商务审核时间 -->
      <template #businessAuditTime="{ record }">
        {{ formatAuditTime(record.businessAuditTime) }}
      </template>
      <!-- 财务审核时间 -->
      <template #financeAuditTime="{ record }">
        {{ formatAuditTime(record.financeAuditTime) }}
      </template>
    </ma-crud>

    <!-- 作废审核弹窗 -->
    <VoidApplicationDialog ref="voidDialogRef" />

    <!-- 审核记录弹窗 -->
    <AuditRecordDialog ref="auditRecordDialogRef" />

    <!-- BG审核记录弹窗 -->
    <BGAuditRecordDialog ref="BGauditRecordDialogRef" />

    <!-- BW审核记录弹窗 -->
    <BWAuditRecordDialog ref="BWauditRecordDialogRef" />

    <!-- 上传发票弹窗 -->
    <InvoiceUploadModal ref="invoiceUploadModalRef" />

    <!-- 查看发票弹窗 -->
    <InvoiceViewModal ref="invoiceViewModalRef" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, toRaw, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import VoidApplicationDialog from "./components/VoidApplicationDialog.vue";
import AuditRecordDialog from "./components/AuditRecordDialog.vue";
import BGAuditRecordDialog from "./components/BGAuditRecordDialog.vue";
import BWAuditRecordDialog from "./components/BWAuditRecordDialog.vue";
import InvoiceUploadModal from "./components/InvoiceUploadModal.vue";
import InvoiceViewModal from "./components/InvoiceViewModal.vue";
import upstreamApi from "~/api/master/upstream";
import {
  invoiceAuditApi,
  InvoiceAuditStatusLabels,
  SourceTypeLabels,
} from "~/api/finance/invoiceAudit";

// 定义页面路由元信息
definePageMeta({
  name: "master-financial-invoiceRequisition",
  path: "/master/financial/invoiceRequisition",
});
const voidDialogRef = ref();
const auditRecordDialogRef = ref();
const BWauditRecordDialogRef = ref();
const BGauditRecordDialogRef = ref();
const invoiceUploadModalRef = ref();
const invoiceViewModalRef = ref();
const crudRef = ref();
const crudKey = ref(0); // 用于强制重新渲染 crud 组件

// 渠道选项
const platformSourceOptions = ref([{ label: "全部", value: "" }]);

// 获取渠道列表
const getChannelList = async () => {
  try {
    console.log("开始获取渠道列表...");
    const res = await upstreamApi.platform.getChannelList({
      page: 1,
      pageSize: 100, // 获取足够多的渠道
    });

    console.log("API响应: ", res);

    if (res && res.data && res.data.items && Array.isArray(res.data.items)) {
      const channelOptions = res.data.items.map((item) => ({
        label: item.name,
        value: item.id, // 使用id作为value
      }));

      // 保留"全部"选项，并添加从API获取的渠道选项
      platformSourceOptions.value = [
        { label: "全部", value: "" },
        ...channelOptions,
      ];
      console.log("platformSourceOptions: ", platformSourceOptions.value);
      console.log("API获取的渠道数据: ", res.data.items);

      // 手动更新 columns 中的渠道列配置
      const channelColumn = columns.find(
        (col) => col.dataIndex === "orderSource"
      );
      if (channelColumn) {
        channelColumn.dict.data = toRaw(platformSourceOptions.value);
        console.log("已更新渠道列配置: ", channelColumn.dict.data);
      }
    }
  } catch (error) {
    Message.error("获取渠道列表失败");
    console.error("获取渠道列表失败:", error);
  }
};

// 状态卡片数据
const currentStatusCard = ref("all");
const statusCardsData = ref({
  all: 0,
  pending: 0,
  financeAuditPassed: 0,
  financeAuditRejected: 0,
  invoiced: 0,
});

const statusCards = computed(() => {
  return [
    {
      key: "all",
      title: "全部",
      count: statusCardsData.value.all,
      status: null,
    },
    {
      key: "pending",
      title: "待审核",
      count: statusCardsData.value.pending,
      status: 0,
    },
    {
      key: "financeAuditPassed",
      title: "财务审核通过",
      count: statusCardsData.value.financeAuditPassed,
      status: 1,
    },
    {
      key: "financeAuditRejected",
      title: "财务审核驳回",
      count: statusCardsData.value.financeAuditRejected,
      status: 2,
    },
    {
      key: "invoiced",
      title: "已开票",
      count: statusCardsData.value.invoiced,
      status: 3,
    },
  ];
});

// 获取状态统计数据
const getStatusStatistics = async () => {
  try {
    const res = await invoiceAuditApi.getStatistics();
    if (res && res.data) {
      statusCardsData.value = {
        all: res.data.total || 0,
        pending: res.data.pendingAudit || 0,
        financeAuditPassed: res.data.financeApproved || 0,
        financeAuditRejected: res.data.financeRejected || 0,
        invoiced: res.data.invoiced || 0,
      };
    }
  } catch (error) {
    console.error("获取状态统计数据失败:", error);
    Message.error("获取状态统计数据失败");
  }
};

// 状态卡片滚动相关
const statsCardsRef = ref(null);

// 滚动状态卡片
const scrollStatsCards = (direction) => {
  if (!statsCardsRef.value) return;

  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;

  if (direction === "left") {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: "smooth",
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: "smooth",
    });
  }
};

// 处理状态卡片点击
const handleStatusCardClick = async (key) => {
  currentStatusCard.value = key;

  // 根据选中的状态卡片过滤数据
  if (crudRef.value) {
    const selectedCard = statusCards.value.find((card) => card.key === key);
    if (selectedCard) {
      // 构建搜索参数
      let statusValue;

      if (selectedCard.status === null) {
        // 全部状态，传空字符串
        statusValue = "";
      } else {
        // 特定状态
        statusValue = selectedCard.status;
      }

      // 直接更新搜索表单的值，然后触发搜索
      await updateSearchFormAndTrigger(statusValue);
    }
  }
};

// 更新搜索表单并触发搜索
const updateSearchFormAndTrigger = async (statusValue) => {
  if (crudRef.value) {
    // 构建搜索参数
    const searchParams = { status: statusValue };

    try {
      // 更新requestParams
      Object.assign(crudRef.value.requestParams, searchParams);
      crudRef.value.requestParams.page = 1;

      // 调用requestData来刷新数据
      await crudRef.value.requestData();

    } catch (error) {
      console.log('搜索失败:', error);
    }
  }
};



// 根据搜索参数同步状态卡片选中状态
const syncStatusCardFromSearch = (statusValue) => {
  // 根据状态值找到对应的状态卡片key
  let targetKey = "all"; // 默认为全部

  if (statusValue === "" || statusValue === null || statusValue === undefined) {
    targetKey = "all";
  } else {
    const targetCard = statusCards.value.find(card => card.status === statusValue);
    if (targetCard) {
      targetKey = targetCard.key;
    }
  }

  // 更新当前选中的状态卡片，但不触发搜索（避免循环调用）
  currentStatusCard.value = targetKey;
};

// 处理自定义状态搜索字段的变化
const handleStatusSearchChange = (value) => {
  // 同步更新状态卡片
  syncStatusCardFromSearch(value);
};

// 格式化审核时间
const formatAuditTime = (timestamp) => {
  if (!timestamp) return "-";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 状态颜色映射
const getStatusColor = (status) => {
  const statusColorMap = {
    0: "orange", // 待审核
    1: "blue", // 财务审核通过
    2: "red", // 财务审核驳回
    3: "green", // 已开票
  };
  return statusColorMap[status] || "gray";
};

// 操作处理函数
const handleInvoiceApplication = (record) => {
  import("@/utils/common").then((module) => {
    const { $router } = useNuxtApp();
    module.navigateWithTag(
      $router,
      `/master/financial/invoiceRequisition/${record.id}`
    );
  });

  // 这里可以打开开票申请弹窗或跳转到申请页面
};

const handleVoidApplication = (record) => {
  voidDialogRef.value?.open(record);
};

// 打开审核记录弹窗
const handleAuditRecord = (record) => {
  auditRecordDialogRef.value?.open(record);
};

// 上传发票处理函数
const handleUploadInvoice = (record) => {
  // 打开上传发票弹窗，传入订单ID和成功回调
  invoiceUploadModalRef.value?.open(record.id, () => {
    // 上传成功后刷新列表
    crudRef.value?.refresh();
    Message.success("发票上传成功");
  });
};

// 查看发票处理函数
const handleViewInvoice = (record) => {
  // 打开查看发票弹窗，传入发票文件URL数组
  invoiceViewModalRef.value?.open(record.invoiceFileUrl || []);
};

// ma-crud 配置
const crudOptions = reactive({
  rowSelection: { showCheckedAll: true },
  // 使用真实API
  api: invoiceAuditApi.getList,
  // 搜索配置
  searchColNumber: 3,
  searchLabelWidth: "130px", // 设置搜索表单label宽度，适应中文标签
  searchLabelAlign: "right", // 设置label右对齐
  // 搜索前处理参数
  beforeSearch(params) {
    // 处理创建时间范围 - 使用 startDate 和 endDate 字段
    if (params.createdAt && params.createdAt.length === 2) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startDate = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endDate = endDate.getTime();

      delete params.createdAt;
    } else {
      delete params.startDate;
      delete params.endDate;
    }

    // 处理财务审核时间范围 - 使用 auditStartDate 和 auditEndDate 字段
    if (params.financeAuditTime && params.financeAuditTime.length === 2) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.financeAuditTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.auditStartDate = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.financeAuditTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.auditEndDate = endDate.getTime();

      delete params.financeAuditTime;
    } else {
      delete params.auditStartDate;
      delete params.auditEndDate;
    }

    return params;
  },
  // 搜索后处理，用于同步状态卡片
  afterSearch(params) {
    // 根据搜索参数中的status值同步状态卡片选中状态
    syncStatusCardFromSearch(params.status);
  },
});

// 申请状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "待审核", value: 0 },
  { label: "财务审核通过", value: 1 },
  { label: "财务审核驳回", value: 2 },
  { label: "已开票", value: 3 },
];

// 数据来源类型选项
const sourceTypeOptions = [
  { label: "全部", value: "" },
  { label: "系统订单", value: 0 },
  { label: "第三方订单", value: 1 },
];

// 发票类型选项
const invoiceTypeOptions = [
  { label: "全部", value: "" },
  { label: "电子普票", value: "电子普票" },
  { label: "电子专票", value: "电子专票" },
  { label: "纸质普票", value: "纸质普票" },
  { label: "纸质专票", value: "纸质专票" },
];

// 表格列定义
const columns = reactive([
  {
    title: "申请单号",
    dataIndex: "applicationNo",
    width: 160,
    search: true,
    formType: "input",
  },
  {
    title: "订单ID",
    dataIndex: "orderId",
    width: 160,
    search: true,
    formType: "input",
  },
  // {
  //   title: "数据来源",
  //   dataIndex: "sourceType",
  //   width: 100,
  //   search: true,
  //   formType: "select",
  //   dict: { data: sourceTypeOptions },
  //   slotName: "sourceType",
  // },
  // {
  //   title: "发票类型",
  //   dataIndex: "invoiceType",
  //   width: 100,
  //   search: true,
  //   formType: "select",
  //   dict: { data: invoiceTypeOptions },
  // },
  {
    title: "申请金额",
    dataIndex: "totalAmount",
    width: 120,
    align: "right",
    // search: true,
    formType: "number",
    slotName: "totalAmount",
  },
  {
    title: "税额",
    dataIndex: "taxAmount",
    width: 100,
    align: "right",
    slotName: "taxAmount",
  },
  {
    title: "不含税金额",
    dataIndex: "untaxedAmount",
    width: 120,
    align: "right",
    slotName: "untaxedAmount",
  },
  {
    title: "申请状态",
    dataIndex: "status",
    width: 120,
    align: "center",
    search: true,
    formType: "select",
    dict: {
      data: statusOptions,
      translation: true, // 启用字典翻译
      props: { label: 'label', value: 'value' } // 明确指定字典的label和value字段
    },
    slotName: "status",
    // 设置搜索默认值，这样可以确保搜索表单正确显示当前状态
    searchDefaultValue: "",
    // 添加change事件监听，实现搜索表单到状态卡片的同步
    onChange: (value) => {
      // 当搜索表单中的状态发生变化时，同步更新状态卡片
      syncStatusCardFromSearch(value);
    },
  },
  {
    title: "发票抬头",
    dataIndex: "invoiceHeader",
    width: 150,
    slotName: "invoiceHeader",
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
    slotName: "createdAt",
  },
  // {
  //   title: "商务审核时间",
  //   dataIndex: "businessAuditTime",
  //   width: 170,
  //   align: "center",
  //   search: true,
  //   formType: "range",
  //   slotName: "businessAuditTime",
  // },
  {
    title: "财务审核时间",
    dataIndex: "financeAuditTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
    slotName: "financeAuditTime",
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 350,
    fixed: "right",
    slotName: "operation",
  },
]);

// 监听渠道选项变化，强制更新组件
watch(
  platformSourceOptions,
  () => {
    // 当渠道选项更新时，强制重新渲染 crud 组件
    crudKey.value++;
    console.log("渠道选项已更新，强制重新渲染组件");
  },
  { deep: true }
);

// 监听当前选中的状态卡片，同步更新搜索表单的值
watch(
  currentStatusCard,
  async (newKey) => {
    // 根据状态卡片更新对应的搜索表单状态值
    const selectedCard = statusCards.value.find(card => card.key === newKey);
    if (selectedCard && crudRef.value) {
      // 等待组件渲染完成
      await nextTick();

      // 直接更新搜索表单的状态值
      try {
        const searchFormRef = crudRef.value.crudSearchRef?.getSearchFormRef();
        if (searchFormRef && searchFormRef.model) {
          if (selectedCard.status === null) {
            searchFormRef.model.status = "";
          } else {
            searchFormRef.model.status = selectedCard.status;
          }
        }
      } catch (error) {
        console.log('更新搜索表单状态失败:', error);
      }
    }
  }
);

// 生命周期钩子
onMounted(() => {
  // 页面加载时主动加载数据
  crudRef.value && crudRef.value.refresh();

  // 设置当前状态卡片
  currentStatusCard.value = "all";

  // 获取渠道列表
  getChannelList();

  // 获取状态统计数据
  getStatusStatistics();
});
</script>

<script>
export default {
  name: "master-financial-invoiceRequisition",
  path: "/master/financial/invoiceRequisition",
};
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

/* 状态卡片样式 */
.stats-cards-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stats-cards {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  background-color: #fff;
  border-radius: 4px;
  flex: 1;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.stats-cards::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.stats-card {
  flex: 0 0 auto;
  width: 250px;
  margin: 5px;
  padding: 18px;
  border-radius: 4px;
  text-align: left;
  border: 1px solid #e5e6eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.stats-card:hover {
  border-color: #d9e1ff;
  background-color: #f9fafc;
}

.stats-card.active {
  border: 1px solid #165dff;
  background-color: #f0f6ff;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.stats-card.active .stats-title {
  color: #165dff;
  font-weight: 500;
}

.stats-card.active .stats-value {
  color: black;
  font-weight: 500;
}

.stats-corner-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 24px 24px 0;
  border-color: transparent #165dff transparent transparent;
}

.check-icon {
  position: absolute;
  top: 1px;
  right: -23px;
  color: white;
  font-size: 14px;
}

.stats-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 500;
  color: #1d2129;
}

.stats-scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
}

.stats-scroll-btn:hover {
  background-color: #f2f3f5;
}

.stats-scroll-btn.stats-scroll-left {
  margin-right: 8px;
}

.stats-scroll-btn.stats-scroll-right {
  margin-left: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
