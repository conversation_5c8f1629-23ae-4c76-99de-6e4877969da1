<!--
 - 联系人列表页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义联系人姓名列 -->
      <template #contactName="{ record }">
        <a-link @click="viewDetail(record)">{{ record.contactName }}</a-link>
      </template>

      <!-- 自定义客户名称列 -->
      <template #customerName="{ record }">
        <span v-if="record.customerName">
          <a-link @click="viewCustomerDetail(record)">{{
            record.customerName
          }}</a-link>
          <a-tag
            v-if="record.customerAlias"
            color="blue"
            size="small"
            class="ml-1"
          >
            {{ record.customerAlias }}
          </a-tag>
        </span>
        <span v-else class="text-gray-400">未绑定客户</span>
      </template>

      <!-- 自定义默认联系人列 -->
      <template #isDefault="{ record }">
        <a-tag :color="record.isDefault ? 'green' : 'gray'">
          {{ record.isDefault ? "是" : "否" }}
        </a-tag>
      </template>

      <!-- 自定义详细地址列 -->
      <template #detailAddress="{ record }">
        <span>{{
          (record.fullRegionName || "") + (record.detailAddress || "")
        }}</span>
      </template>

      <!-- 自定义创建时间列 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt" format="yyyy-MM-dd hh:mm:ss"></div>
      </template>

      <!-- 自定义操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="editContact(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="viewDetail(record)">
            <template #icon><icon-eye /></template>
            查看
          </a-button>
          <a-button
            v-if="!record.customerId"
            type="text"
            size="small"
            @click="bindCustomer(record)"
          >
            <template #icon><icon-link /></template>
            绑定客户
          </a-button>
          <a-button
            v-else
            type="text"
            size="small"
            @click="unbindCustomer(record)"
          >
            <template #icon><icon-link /></template>
            解绑客户
          </a-button>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableBeforeButtons>
        <a-button
          type="primary"
          status="primary"
          class="w-full lg:w-auto mt-2 lg:mt-0"
          @click="handleAddContact"
        >
          <template #icon><icon-plus /></template>
          新增联系人
        </a-button>
      </template>
    </ma-crud>

    <!-- 联系人详情抽屉 -->
    <ContactDetail
      v-model:visible="detailDrawer.visible"
      :title="detailDrawer.title"
      :contact-id="detailDrawer.contactId"
      @close="closeDetailDrawer"
      @confirm="closeDetailDrawer"
      @edit="editContact"
    />

    <!-- 绑定客户弹窗 -->
    <BindCustomerModal
      v-model:visible="bindModal.visible"
      :contact="bindModal.contact"
      @confirm="handleBindConfirm"
      @cancel="closeBind"
    />

    <!-- 新增联系人弹窗 -->
    <AddContactModal
      v-model:visible="addModal.visible"
      :title="addModal.title"
      @confirm="handleAddConfirm"
      @cancel="closeAdd"
    />

    <!-- 编辑联系人弹窗 -->
    <EditContactModal
      v-model:visible="editModal.visible"
      :contact-data="editModal.contactData"
      @confirm="handleEditConfirm"
      @cancel="closeEdit"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import ContactDetail from "./components/ContactDetail.vue";
import BindCustomerModal from "./components/BindCustomerModal.vue";
import AddContactModal from "./components/AddContactModal.vue";
import EditContactModal from "./components/EditContactModal.vue";
import dayjs from "dayjs";
import { Message } from "@arco-design/web-vue";
import { contactApi, DefaultContactDescriptions } from "@/api/crm/contact.js";

definePageMeta({
  name: "master-contacts",
  path: "/master/customer/management/contacts",
});

// 引用
const crudRef = ref();

// 详情抽屉状态
const detailDrawer = reactive({
  visible: false,
  title: "联系人详情",
  contactId: null,
});

// 绑定客户弹窗状态
const bindModal = reactive({
  visible: false,
  contact: null,
});

// 新增联系人弹窗状态
const addModal = reactive({
  visible: false,
  title: "新增联系人",
});

// 编辑联系人弹窗状态
const editModal = reactive({
  visible: false,
  contactData: null,
});

// 表格列配置
const columns = reactive([
  {
    title: "联系人姓名",
    dataIndex: "contactName",
    slotName: "contactName",
    width: 120,
    search: true,
  },
  {
    title: "联系电话",
    dataIndex: "contactPhone",
    width: 130,
    search: true,
  },
  {
    title: "客户名称",
    dataIndex: "customerName",
    slotName: "customerName",
    width: 150,
    search: true,
    searchKey: "customerName",
  },
  {
    title: "部门",
    dataIndex: "department",
    width: 100,
    search: true,
  },
  {
    title: "职位",
    dataIndex: "position",
    width: 100,
    search: true,
  },
  {
    title: "邮箱",
    dataIndex: "email",
    width: 150,
    search: true,
  },
  {
    title: "微信号",
    dataIndex: "wechatId",
    width: 120,
    search: true,
  },
  {
    title: "关键联系人",
    dataIndex: "isDefault",
    slotName: "isDefault",
    width: 100,
  },
  {
    title: "详细地址",
    dataIndex: "detailAddress",
    slotName: "detailAddress",
    width: 200,
    ellipsis: true,
    tooltip: true,
    search: true,
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    width: 150,
    slotName: "createdAt",
  },
]);

// CRUD配置
const crud = reactive({
  showTools: false,
  api: contactApi.getList,
  // 表格配置
  showIndex: true,
  pageLayout: "fixed",
  searchLabelWidth: "120px",
  // 操作列配置
  operationColumn: true,
  operationColumnWidth: 300,
  // 禁用默认的增删改操作
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  // 搜索前置处理
  beforeSearch: (requestParams) => {
    // 处理时间范围参数
    if (requestParams.startDate && requestParams.endDate) {
      requestParams.startDate = dayjs(requestParams.startDate).format(
        "YYYY-MM-DD"
      );
      requestParams.endDate = dayjs(requestParams.endDate).format("YYYY-MM-DD");
    }
    return requestParams;
  },
});

// 查看详情
const viewDetail = (record) => {
  detailDrawer.contactId = record.id;
  detailDrawer.title = `联系人详情 - ${record.contactName}`;
  detailDrawer.visible = true;
};

// 查看客户详情
const viewCustomerDetail = (record) => {
  if (record.customerId) {
    import("@/utils/common").then((module) => {
      const router = useRouter();
      module.navigateWithTag(
        router,
        `/master/customer/management/customer_list/${record.customerId}`
      );
    });
  }
};

// 关闭详情抽屉
const closeDetailDrawer = () => {
  detailDrawer.visible = false;
  detailDrawer.contactId = null;
};

// 编辑联系人
const editContact = (record) => {
  console.log("编辑联系人:", record);
  editModal.contactData = { ...record };
  editModal.visible = true;
};

// 新增联系人
const handleAddContact = () => {
  addModal.visible = true;
};

// 关闭新增弹窗
const closeAdd = () => {
  addModal.visible = false;
};

// 处理新增确认
const handleAddConfirm = (newContact) => {
  // 刷新列表
  if (crudRef.value) {
    crudRef.value.refresh();
  }
  closeAdd();
};

// 关闭编辑弹窗
const closeEdit = () => {
  editModal.visible = false;
  editModal.contactData = null;
};

// 处理编辑确认
const handleEditConfirm = (updatedContact) => {
  console.log("编辑确认:", updatedContact);
  // 刷新列表
  if (crudRef.value) {
    crudRef.value.refresh();
  }
  closeEdit();
  Message.success("联系人更新成功");
};

// 绑定客户
const bindCustomer = (record) => {
  bindModal.contact = record;
  bindModal.visible = true;
};

// 处理绑定确认
const handleBindConfirm = async (customerId) => {
  try {
    await contactApi.bindCustomer({
      contactId: bindModal.contact.id,
      customerId: customerId,
    });

    Message.success("绑定客户成功");
    closeBind();
    // 刷新列表
    crudRef.value.refresh();
  } catch (error) {
    console.error("绑定客户失败:", error);
    Message.error(error.message || "绑定客户失败");
  }
};

// 关闭绑定弹窗
const closeBind = () => {
  bindModal.visible = false;
  bindModal.contact = null;
};

// 解绑客户
const unbindCustomer = async (record) => {
  try {
    await contactApi.unbindCustomer(record.id);
    Message.success("解绑客户成功");
    // 刷新列表
    crudRef.value.refresh();
  } catch (error) {
    console.error("解绑客户失败:", error);
    Message.error(error.message || "解绑客户失败");
  }
};

onMounted(() => {
  console.log("联系人列表页面已挂载");
});
</script>

<style scoped>
.ma-content-block {
  background: #fff;
  border-radius: 8px;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-600:hover {
  color: #1d4ed8;
}

.text-green-600 {
  color: #16a34a;
}

.text-green-600:hover {
  color: #15803d;
}

.text-orange-600 {
  color: #ea580c;
}

.text-orange-600:hover {
  color: #c2410c;
}
</style>
