<!--
 - 客户详情抽屉组件
 - 
 - <AUTHOR>
-->
<template>
  <div>
    <a-drawer
      :visible="visible"
      @update:visible="handleVisibleChange"
      :title="title"
      width="50%"
      :footer="true"
      @cancel="handleClose"
    >
      <template #footer>
        <div class="flex justify-end space-x-2">
          <a-button @click="handleClose">取消</a-button>
          <a-button type="primary" @click="handleConfirm">确定</a-button>
        </div>
      </template>
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <a-spin size="large" />
      </div>

      <!-- 空状态 -->
      <div
        v-else-if="
          !loading && (!customer || Object.keys(customer).length === 0)
        "
        class="flex justify-center items-center h-64"
      >
        <div class="text-center text-gray-500">
          <div class="text-lg mb-2">暂无客户信息</div>
          <div class="text-sm">请检查客户ID是否正确</div>
        </div>
      </div>

      <template v-else-if="customer && Object.keys(customer).length > 0">
        <div class="p-4">
          <!-- 客户基本信息头部 -->
          <div class="customer-header mb-4 pb-4">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <a-avatar
                  :size="64"
                  :style="{ backgroundColor: '#165DFF' }"
                  class="mr-4"
                >
                  {{ customer.name ? customer.name.substring(0, 1) : "C" }}
                </a-avatar>
                <div class="flex flex-col ml-2">
                  <h3 class="text-lg font-medium">
                    {{ customer.name || "未命名客户" }}
                  </h3>
                  <div class="flex items-center text-gray-600 text-sm mt-1">
                    <a-tag
                      :color="getStatusColor(customer.status)"
                      size="small"
                      class="mr-2"
                    >
                      {{ getStatusText(customer.status) || "未知状态" }}
                    </a-tag>
                    <a-tag
                      :color="getSourceColor(customer.source)"
                      size="small"
                    >
                      {{ getSourceText(customer.source) || "未知来源" }}
                    </a-tag>
                  </div>
                </div>
              </div>
              <div></div>
            </div>
          </div>

          <!-- 标签页内容 -->
          <a-tabs default-active-key="info">
            <a-tab-pane key="info" title="基本信息">
              <div class="bg-gray-50 p-6 rounded-lg">
                <div class="space-y-4">
                  <!-- 第一行：客户编号 和 客户状态 -->
                  <div class="grid grid-cols-2 gap-8">
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户编号：</span>
                      <span class="text-gray-900">{{
                        customer.customerCode || ""
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户状态：</span>
                      <a-tag :color="getStatusColor(customer.status)">
                        {{ getStatusText(customer.status) || "未知状态" }}
                      </a-tag>
                    </div>
                  </div>

                  <!-- 第二行：客户名称 和 客户简称 -->
                  <div class="grid grid-cols-2 gap-8">
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户名称：</span>
                      <span class="text-gray-900">{{
                        customer.name || ""
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户简称：</span>
                      <span class="text-gray-900">{{
                        customer.shortName || ""
                      }}</span>
                    </div>
                  </div>

                  <!-- 第三行：所属集团 和 所属行业 -->
                  <div class="grid grid-cols-2 gap-8">
                    <div class="flex">
                      <span class="text-gray-600 w-20">所属集团：</span>
                      <span class="text-gray-900">{{
                        customer.groupName || ""
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">所属行业：</span>
                      <span class="text-gray-900">{{
                        customer.industryName || ""
                      }}</span>
                    </div>
                  </div>

                  <!-- 第四行：客户来源 和 客户规模 -->
                  <div class="grid grid-cols-2 gap-8">
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户来源：</span>
                      <a-tag :color="getSourceColor(customer.source)">
                        {{ getSourceText(customer.source) || "" }}
                      </a-tag>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">客户规模：</span>
                      <span class="text-gray-900">{{
                        customer.companyScale || "-"
                      }}</span>
                    </div>
                  </div>

                  <!-- 第五行：公司地址 -->
                  <div class="flex">
                    <span class="text-gray-600 w-20">公司地址：</span>
                    <span class="text-gray-900">{{
                      customer.fullAddress || ""
                    }}</span>
                  </div>

                  <!-- 第六行：客户备注 -->
                  <div class="flex">
                    <span class="text-gray-600 w-20">客户备注：</span>
                    <span class="text-gray-900 leading-relaxed">{{
                      customer.remark || ""
                    }}</span>
                  </div>

                  <!-- 第七行：客户附件 -->
                  <div class="flex">
                    <span class="text-gray-600 w-20">客户附件：</span>
                    <div class="space-y-2">
                      <template
                        v-if="
                          customer.attachments &&
                          customer.attachments.length > 0
                        "
                      >
                        <div
                          v-for="(attachment, index) in customer.attachments"
                          :key="attachment.id || index"
                          class="flex items-center space-x-3"
                        >
                          <span class="text-gray-900">{{
                            attachment.fileName || ``
                          }}</span>
                          <span
                            v-if="attachment.fileSize"
                            class="text-xs text-gray-500"
                          >
                            ({{ formatFileSize(attachment.fileSize) }})
                          </span>
                          <a-button
                            type="text"
                            size="small"
                            class="text-blue-500 hover:text-blue-700"
                            @click="previewAttachment(attachment)"
                          >
                            在线预览
                          </a-button>
                          <a-button
                            type="text"
                            size="small"
                            class="text-blue-500 hover:text-blue-700"
                            @click="downloadAttachment(attachment)"
                          >
                            下载
                          </a-button>
                        </div>
                      </template>
                      <div v-else class="text-gray-500 text-sm">暂无附件</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="contacts" title="联系人">
              <div class="space-y-4">
                <!-- 联系人卡片列表 -->
                <div
                  v-for="contact in contacts"
                  :key="contact.id"
                  class="border border-gray-200 rounded-lg"
                >
                  <!-- 卡片头部 - 始终显示 -->
                  <div
                    class="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    @click="toggleContactExpand(contact.id)"
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <!-- 第一行：姓名和标签 -->
                        <div class="flex items-center space-x-3 mb-2">
                          <span class="text-lg font-medium text-gray-900">{{
                            contact.contactName || contact.name
                          }}</span>
                          <span class="text-sm text-gray-600"
                            >({{
                              contact.position || contact.role || "联系人"
                            }})</span
                          >
                          <span
                            v-if="contact.isDefault"
                            class="text-sm text-red-500"
                            >关键联系人</span
                          >
                        </div>

                        <!-- 第二行：电话和微信 -->
                        <div
                          class="flex items-center space-x-6 text-sm text-gray-600"
                        >
                          <div class="flex items-center space-x-2">
                            📞
                            <span>{{
                              contact.contactPhone || contact.phone || "-"
                            }}</span>
                          </div>
                          <div class="flex items-center space-x-2">
                            💬
                            <span>{{
                              contact.wechatId || contact.wechat || "-"
                            }}</span>
                          </div>
                        </div>

                        <!-- 第三行：地址和备注预览 -->
                        <div class="mt-2 text-sm text-gray-500">
                          <div>
                            所在地区：{{
                              contact.fullRegionName ??
                              "" + contact.detailAddress ??
                              ""
                            }}
                          </div>
                          <div class="mt-1">
                            备注：{{
                              contact.remark || contact.remarkPreview || ""
                            }}
                          </div>
                        </div>
                      </div>

                      <!-- 展开/收起按钮 -->
                      <div class="ml-4">
                        <icon-up
                          v-if="contact.expanded"
                          class="w-5 h-5 text-gray-400"
                        />
                        <icon-down v-else class="w-5 h-5 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <!-- 卡片详细内容 - 展开时显示 -->
                  <div
                    v-if="contact.expanded"
                    class="bg-white border-t border-gray-200"
                  >
                    <div class="p-6 space-y-4">
                      <!-- 详细信息网格布局 -->
                      <div class="grid grid-cols-2 gap-x-8 gap-y-4">
                        <div class="flex">
                          <span class="text-gray-600 w-24">联系人姓名：</span>
                          <span class="text-gray-900">{{
                            contact.contactName || contact.name || "-"
                          }}</span>
                        </div>

                        <div class="flex">
                          <span class="text-gray-600 w-24">所属部门：</span>
                          <span class="text-gray-900">{{
                            contact.department || "-"
                          }}</span>
                        </div>

                        <div class="flex">
                          <span class="text-gray-600 w-24">公司职位：</span>
                          <span class="text-gray-900">{{
                            contact.position || "-"
                          }}</span>
                        </div>

                        <div class="flex">
                          <span class="text-gray-600 w-24">联系人电话：</span>
                          <span class="text-gray-900">{{
                            contact.contactPhone || contact.phone || "-"
                          }}</span>
                        </div>

                        <div class="flex">
                          <span class="text-gray-600 w-24">微信号：</span>
                          <span class="text-gray-900">{{
                            contact.wechatId || contact.wechat || "-"
                          }}</span>
                        </div>

                        <div class="flex">
                          <span class="text-gray-600 w-24">电子邮箱：</span>
                          <span class="text-gray-900">{{
                            contact.email || "-"
                          }}</span>
                        </div>
                      </div>

                      <!-- 地址信息 -->
                      <div class="flex">
                        <span class="text-gray-600 w-24">所在地区：</span>
                        <span class="text-gray-900">{{
                          contact.fullRegionName + contact.detailAddress
                        }}</span>
                      </div>

                      <!-- 生日信息 -->
                      <div class="flex">
                        <span class="text-gray-600 w-24">生日：</span>
                        <span
                          class="text-gray-900"
                          v-time="contact.birthday"
                          format="yyyy-MM-dd"
                          v-if="contact.birthday"
                        ></span>
                      </div>

                      <!-- 备注信息 -->
                      <div class="flex">
                        <span class="text-gray-600 w-24">备注：</span>
                        <span class="text-gray-900">{{ contact.remark }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="followRecords" title="财务信息">
              <div class="space-y-6">
                <!-- 基本财务信息 -->
                <div class="bg-white" v-if="customer.financeInfo">
                  <div class="grid grid-cols-2 gap-x-8 gap-y-4 text-sm">
                    <div class="flex">
                      <span class="text-gray-600 w-20">开户名称：</span>
                      <span class="text-gray-900">{{
                        customer.financeInfo.accountHolderName || "-"
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">开户行：</span>
                      <span class="text-gray-900">{{
                        customer.financeInfo.bankName || "-"
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">银行账户：</span>
                      <span class="text-gray-900">{{
                        customer.financeInfo.bankAccount || "-"
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">账期依据：</span>
                      <span class="text-gray-900">{{
                        getAccountPeriodBasisText(
                          customer.financeInfo.accountPeriodBasis
                        ) || "-"
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">结算方式：</span>
                      <span class="text-gray-900">{{
                        getSettlementMethodText(
                          customer.financeInfo.settlementMethod
                        ) || "-"
                      }}</span>
                    </div>
                    <div class="flex">
                      <span class="text-gray-600 w-20">结算日期：</span>
                      <span class="text-gray-900">{{
                        customer.financeInfo.settlementDate || "-"
                      }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  暂无财务信息
                </div>

                <!-- 发票抬头信息 -->
                <div
                  v-if="
                    customer.invoiceInfos && customer.invoiceInfos.length > 0
                  "
                >
                  <div
                    v-for="(invoice, index) in customer.invoiceInfos"
                    :key="invoice.id || index"
                    class="bg-white mb-4"
                  >
                    <div class="mb-4">
                      <h4 class="text-sm font-medium text-gray-700">
                        发票抬头{{ index + 1 }}
                      </h4>
                    </div>
                    <div class="grid grid-cols-2 gap-x-8 gap-y-4 text-sm">
                      <div class="flex">
                        <span class="text-gray-600 w-20">发票抬头：</span>
                        <span class="text-gray-900">{{
                          invoice.invoiceTitle || "-"
                        }}</span>
                      </div>
                      <div class="flex">
                        <span class="text-gray-600 w-20">税号：</span>
                        <span class="text-gray-900">{{
                          invoice.taxNumber || "-"
                        }}</span>
                      </div>
                      <div class="flex">
                        <span class="text-gray-600 w-20">开票地址：</span>
                        <span class="text-gray-900">{{
                          invoice.invoiceAddress || "-"
                        }}</span>
                      </div>
                      <div class="flex">
                        <span class="text-gray-600 w-20">开票电话：</span>
                        <span class="text-gray-900">{{
                          invoice.invoicePhone || "-"
                        }}</span>
                      </div>
                      <div class="flex">
                        <span class="text-gray-600 w-20">开户银行：</span>
                        <span class="text-gray-900">{{
                          invoice.bankName || "-"
                        }}</span>
                      </div>
                      <div class="flex">
                        <span class="text-gray-600 w-20">银行账户：</span>
                        <span class="text-gray-900">{{
                          invoice.bankAccount || "-"
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  暂无发票抬头信息
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="communications" title="沟通记录" v-if="false">
              <div class="mb-6">
                <!-- 时间轴样式的沟通记录列表 -->
                <div class="relative">
                  <!-- 时间轴线 -->
                  <div
                    class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"
                  ></div>

                  <!-- 沟通记录列表 -->
                  <div class="space-y-6">
                    <div
                      v-for="record in communications"
                      :key="record.id"
                      class="relative pl-12"
                    >
                      <!-- 时间轴圆点 -->
                      <div
                        class="absolute left-2.5 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow"
                      ></div>

                      <!-- 沟通记录卡片 -->
                      <div class="bg-gray-50 rounded-lg p-4 shadow-sm">
                        <!-- 时间 -->
                        <div class="text-sm text-gray-500 mb-3">
                          {{ record.time }}
                        </div>

                        <!-- 沟通详情 -->
                        <div class="space-y-2 text-sm">
                          <div class="flex">
                            <span class="text-gray-600 w-24">沟通事项：</span>
                            <span class="text-gray-900">{{
                              record.subject
                            }}</span>
                          </div>

                          <div class="flex">
                            <span class="text-gray-600 w-24">沟通联系人：</span>
                            <span class="text-gray-900">{{
                              record.contactPerson
                            }}</span>
                          </div>

                          <div class="flex">
                            <span class="text-gray-600 w-24">沟通人：</span>
                            <span class="text-gray-900">{{
                              record.communicator
                            }}</span>
                          </div>

                          <div class="flex">
                            <span class="text-gray-600 w-24">沟通结果：</span>
                            <span class="text-gray-900">{{
                              record.result
                            }}</span>
                          </div>

                          <div class="flex">
                            <span class="text-gray-600 w-24">附件：</span>
                            <div
                              v-if="
                                record.attachments &&
                                record.attachments.length > 0
                              "
                              class="space-y-2"
                            >
                              <div
                                v-for="(
                                  attachment, index
                                ) in record.attachments"
                                :key="index"
                                class="flex items-center space-x-3"
                              >
                                <span class="text-gray-900">{{
                                  attachment.name
                                }}</span>
                                <a-button
                                  type="text"
                                  size="small"
                                  class="text-blue-500 hover:text-blue-700"
                                  @click="previewAttachment(attachment)"
                                >
                                  在线预览
                                </a-button>
                                <a-button
                                  type="text"
                                  size="small"
                                  class="text-blue-500 hover:text-blue-700"
                                  @click="downloadAttachment(attachment)"
                                >
                                  下载
                                </a-button>
                              </div>
                            </div>
                            <span v-else class="text-gray-900">-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态 -->
                  <div
                    v-if="!communications || communications.length === 0"
                    class="text-center py-8 text-gray-500"
                  >
                    暂无沟通记录
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="opportunities" title="商机记录" v-if="false">
              <div class="space-y-4">
                <!-- 新增商机按钮 -->
                <div class="text-right mb-4">
                  <a-button
                    type="primary"
                    size="small"
                    @click="openOpportunityModal"
                  >
                    <template #icon><icon-plus /></template>
                    新增商机
                  </a-button>
                </div>

                <!-- 商机卡片列表 -->
                <div
                  v-for="opportunity in opportunities"
                  :key="opportunity.id"
                  class="border border-gray-200 rounded-lg"
                >
                  <!-- 卡片头部 - 始终显示 -->
                  <div
                    class="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    @click="toggleOpportunityExpand(opportunity.id)"
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <!-- 第一行：商机标签 + 商机名称 + 成功概率 -->
                        <div class="flex items-center justify-between mb-2">
                          <div class="flex items-center space-x-3">
                            <span
                              class="bg-red-500 text-white text-xs px-2 py-1 rounded"
                              >商机</span
                            >
                            <span class="text-lg font-medium text-gray-900">{{
                              opportunity.name
                            }}</span>
                          </div>
                          <span class="text-2xl font-bold text-gray-900">{{
                            opportunity.successRate
                          }}</span>
                        </div>

                        <!-- 第二行：预计预算 + 时间 -->
                        <div
                          class="flex items-center justify-between mb-2 text-sm text-gray-600"
                        >
                          <span>预计预算：{{ opportunity.budget }}</span>
                          <span>{{ opportunity.time }}</span>
                        </div>

                        <!-- 第三行：商机备注 -->
                        <div class="text-sm text-gray-500">
                          商机备注：{{ opportunity.remark }}
                        </div>
                      </div>

                      <!-- 展开/收起按钮 -->
                      <div class="ml-4">
                        <icon-up
                          v-if="opportunity.expanded"
                          class="w-5 h-5 text-gray-400"
                        />
                        <icon-down v-else class="w-5 h-5 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <!-- 卡片详细内容 - 展开时显示商品信息 -->
                  <div
                    v-if="opportunity.expanded"
                    class="bg-white border-t border-gray-200"
                  >
                    <div class="p-4">
                      <!-- 商品信息表格 -->
                      <a-table
                        :data="opportunity.products"
                        :pagination="false"
                        size="small"
                      >
                        <template #columns>
                          <a-table-column
                            title="商品名称"
                            data-index="name"
                            :width="300"
                          ></a-table-column>
                          <a-table-column
                            title="商品规格"
                            data-index="specification"
                            :width="150"
                          ></a-table-column>
                          <a-table-column
                            title="商品数量"
                            data-index="quantity"
                            :width="100"
                          ></a-table-column>
                          <a-table-column
                            title="状态"
                            data-index="status"
                            :width="120"
                          >
                            <template #cell="{ record }">
                              <a-tag
                                :color="getProductStatusColor(record.status)"
                              >
                                {{ record.status }}
                              </a-tag>
                            </template>
                          </a-table-column>
                        </template>
                      </a-table>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-if="!opportunities || opportunities.length === 0"
                  class="text-center py-8 text-gray-500"
                >
                  暂无商机记录
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="orders" title="订单记录" v-if="false">
              <div class="space-y-4">
                <!-- 订单卡片列表 -->
                <div
                  v-for="order in orders"
                  :key="order.id"
                  class="relative bg-gray-50 rounded-lg p-4 shadow-sm"
                >
                  <!-- 状态标签 -->
                  <div class="absolute top-0 left-0">
                    <div
                      class="bg-green-500 text-white text-xs px-3 py-1 rounded-tl-lg rounded-br-lg"
                    >
                      {{ order.status }}
                    </div>
                  </div>

                  <!-- 订单头部信息 -->
                  <div class="flex items-center justify-between mb-3 pt-2">
                    <div class="flex items-center space-x-3">
                      <span class="text-lg font-medium text-gray-900">{{
                        order.orderNo
                      }}</span>
                      <span class="text-gray-600">{{ order.platform }}</span>
                    </div>
                    <span class="text-xl font-bold text-gray-900">{{
                      order.amount
                    }}</span>
                  </div>

                  <!-- 订单详细信息 -->
                  <div class="space-y-2 text-sm">
                    <div class="flex">
                      <span class="text-gray-600 w-20">收货人：</span>
                      <span class="text-gray-900">{{ order.recipient }}</span>
                    </div>

                    <div class="flex justify-between items-center">
                      <div class="flex">
                        <span class="text-gray-600 w-20">收货地址：</span>
                        <span class="text-gray-900">{{ order.address }}</span>
                      </div>
                      <!-- 时间信息 -->
                      <span class="text-sm text-gray-500">{{
                        order.createTime
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-if="!orders || orders.length === 0"
                  class="text-center py-8 text-gray-500"
                >
                  暂无订单记录
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="logs" title="操作日志" v-if="false">
              <div class="flex justify-between items-center mb-2">
                <div class="text-sm text-gray-500">
                  共 {{ logs.length || 0 }} 条操作日志
                </div>
              </div>
              <a-table :data="logs" :pagination="false">
                <template #columns>
                  <a-table-column
                    title="操作时间"
                    data-index="time"
                  ></a-table-column>
                  <a-table-column
                    title="操作人员"
                    data-index="user"
                  ></a-table-column>
                  <a-table-column
                    title="操作类型"
                    data-index="type"
                  ></a-table-column>
                  <a-table-column
                    title="操作内容"
                    data-index="content"
                  ></a-table-column>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </a-drawer>

    <!-- 跟进记录弹窗 -->
    <FollowRecordForm
      :visible="followModal.visible"
      @update:visible="followModal.visible = $event"
      :title="followModal.title"
      :customer="customer"
      :status-options="statusOptions"
      @save="handleSaveFollowRecord"
      @close="followModal.visible = false"
    />

    <!-- 商机弹窗 -->
    <OpportunityForm
      :visible="opportunityModal.visible"
      @update:visible="opportunityModal.visible = $event"
      :title="opportunityModal.title"
      :customer="customer"
      :opportunity="currentOpportunity"
      @save="handleOpportunitySaved"
      @close="opportunityModal.visible = false"
    />

    <!-- 分配销售弹窗已移动到客户列表页面 -->

    <!-- 新增联系人弹窗 -->
    <ContactForm
      :visible="contactModal.visible"
      @update:visible="contactModal.visible = $event"
      :title="contactModal.title"
      :customer="customer"
      :contact="contactForm"
      @save="handleContactSaved"
      @close="contactModal.visible = false"
      @refresh="emit('refresh')"
    />

    <!-- 新增沟通记录弹窗 -->
    <CommunicationForm
      :visible="communicationModal.visible"
      @update:visible="communicationModal.visible = $event"
      :title="communicationModal.title"
      :customer="customer"
      :contacts="contacts"
      @save="handleCommunicationSaved"
      @close="communicationModal.visible = false"
      @add-contact="addContactFromCommunication"
      @refresh="emit('refresh')"
    />
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import dayjs from "dayjs";
import {
  customerApi,
  CustomerSourceDescriptions,
  CustomerSourceEnum,
  CustomerStatusDescriptions,
  CustomerStatusEnum,
} from "@/api/crm/customer.js";

// 导入各种表单组件
import ContactForm from "./ContactForm.vue";
import CommunicationForm from "./CommunicationForm.vue";
import OpportunityForm from "./OpportunityForm.vue";
import FollowRecordForm from "./FollowRecordForm.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "客户详情",
  },
  customerId: {
    type: [String, Number],
    default: null,
  },
});

const emit = defineEmits([
  "update:visible",
  "edit",
  "assign",
  "close",
  "confirm",
  "refresh",
  "action",
  "delete-contact",
]);

// 响应式数据存储客户详情
const customer = ref({});
const contacts = ref([]);
const followRecords = ref([]);
const communications = ref([]);
const opportunities = ref([]);
const orders = ref([]);
const logs = ref([]);
const loading = ref(false);

// 获取客户详情数据
const loadCustomerDetail = async (customerId) => {
  if (!customerId) return;

  loading.value = true;
  try {
    console.log("开始加载客户详情，ID:", customerId);
    const response = await customerApi.getById(customerId);
    console.log("API响应:", response);

    if (response && response.code === 200 && response.data) {
      const customerData = response.data;
      console.log("客户详情数据:", customerData);

      // 映射客户基本信息
      customer.value = {
        id: customerData.id,
        customerCode: customerData.customerCode || "SN20250624151051",
        name: customerData.customerName || "",
        shortName: customerData.customerAlias || "",
        groupName: customerData.parentGroup || "",
        industryName: getIndustryText(customerData.industry) || "",
        source: customerData.source,
        status: customerData.status,
        companyScale: customerData.companyScale,
        fullAddress: getFullAddress(customerData),
        remark: customerData.remark || "",
        attachments: customerData.attachments || [],
        // 销售人员信息
        salesperson1: customerData.salesperson1,
        salesperson2: customerData.salesperson2,
        salesperson3: customerData.salesperson3,
        salesperson1Id: customerData.salesperson1Id,
        salesperson2Id: customerData.salesperson2Id,
        salesperson3Id: customerData.salesperson3Id,
        // 财务信息
        financeInfo: customerData.financeInfo,
        // 开票信息
        invoiceInfos: customerData.invoiceInfos || [],
      };

      // 映射联系人信息，添加展开状态
      contacts.value = (customerData.contacts || []).map((contact) => ({
        ...contact,
        expanded: false, // 默认收起状态
      }));

      // 映射其他信息（暂时使用模拟数据，后续可根据实际API调整）
      followRecords.value = [];
      communications.value = [];
      opportunities.value = [];
      orders.value = [];
      logs.value = [];

      console.log("客户详情加载成功");
    } else {
      console.error("API响应错误:", response);
      Message.error(response?.message || "加载客户详情失败");
    }
  } catch (error) {
    console.error("加载客户详情失败:", error);
    Message.error(error?.message || "加载客户详情失败，请检查网络连接");
  } finally {
    loading.value = false;
  }
};

// 获取行业文本
const getIndustryText = (industry) => {
  const industryMap = {
    1: "互联网",
    2: "金融",
    3: "教育",
    4: "医疗",
    5: "制造业",
    6: "服务业",
  };
  return industryMap[industry] || "未知行业";
};

// 获取账期依据文本
const getAccountPeriodBasisText = (basis) => {
  const basisMap = {
    1: "订单已发货",
    2: "订单已收货",
    3: "订单已开票",
  };
  return basisMap[basis] || "未设置";
};

// 获取结算方式文本
const getSettlementMethodText = (method) => {
  const methodMap = {
    1: "月结",
    2: "固定账期",
    3: "现销现结",
  };
  return methodMap[method] || "未设置";
};

// 获取完整地址
const getFullAddress = (customerData) => {
  const parts = [];
  if (customerData.fullRegionName) {
    // 这里可以根据实际的地区代码转换为地区名称
    parts.push(customerData.fullRegionName);
  }
  if (customerData.detailAddress) {
    parts.push(customerData.detailAddress);
  }
  return parts.join(" ") || "地址信息未完善";
};

// 监听 customerId 变化，自动加载客户详情
// watch(
//   () => props.customerId,
//   (newCustomerId) => {
//     if (newCustomerId && props.visible) {
//       loadCustomerDetail(newCustomerId);
//     }
//   },
//   { immediate: true }
// );

// 监听弹窗显示状态，当弹窗打开时加载数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.customerId) {
      loadCustomerDetail(props.customerId);
    }
  }
);

// 定义弹窗状态
const followModal = reactive({
  visible: false,
  title: "添加跟进记录",
});

const opportunityModal = reactive({
  visible: false,
  title: "新增商机",
});

// 当前操作的商机数据
const currentOpportunity = reactive({
  id: undefined,
  customerId: undefined,
  name: "",
  amount: "",
  stage: "",
  owner: "",
  createTime: "",
  remark: "",
});

// 分配销售人员功能已移动到客户列表页面

const contactModal = reactive({
  visible: false,
  title: "新增联系人",
});

const communicationModal = reactive({
  visible: false,
  title: "新增沟通记录",
});

// 默认附件数据
const defaultAttachments = ref([
  {
    id: 1,
    name: "2025年供销合作协议.pdf",
    url: "/files/2025年供销合作协议.pdf",
    size: "2.5MB",
    uploadTime: "2025-05-01 10:00:00",
  },
  {
    id: 2,
    name: "2025年供销合作协议.pdf",
    url: "/files/2025年供销合作协议_v2.pdf",
    size: "2.8MB",
    uploadTime: "2025-05-15 14:30:00",
  },
]);

// 联系人表单数据
const contactForm = reactive({
  id: undefined,
  customerId: undefined,
  name: "",
  position: "",
  phone: "",
  wechat: "",
  email: "",
  importance: 3,
  remark: "",
  fromCommunication: false,
});

// 客户状态选项
const statusOptions = ref([
  { label: "了解产品", value: 1 },
  { label: "正在跟进", value: 2 },
  { label: "准备购买", value: 3 },
  { label: "复购", value: 4 },
]);

// 联系人列表数据
const contactsList = ref([
  {
    id: 1,
    name: "李强东",
    role: "默认联系人",
    tag: "供应链负责人（最近下单）",
    phone: "134****0987",
    wechat: "qwthohglan2019",
    location: "广东省广州市番禺区五洲城商务中心 C2 座",
    remarkPreview: "西南地区负责人，来购问题优先找此人处理",
    department: "供应链",
    position: "供应链负责人",
    email: "<EMAIL>",
    fullLocation: "广东省广州市番禺区五洲城商务中心 C2 座",
    birthday: "1986.10.01",
    remark: "来购问题优先找此人处理",
    expanded: false,
  },
]);

// 沟通记录列表数据
const communicationsList = ref([
  {
    id: 1,
    time: "2024.06.14 17:45:16",
    subject: "日常回访",
    contactPerson: "李强东",
    communicator: "李三",
    result: "没有下单欲望，目前的商机已经结束",
    attachments: [
      {
        name: "客户回访记录.xlsx",
        url: "/files/customer-visit-record.xlsx",
        type: "excel",
      },
    ],
  },
  {
    id: 2,
    time: "2024.06.13 17:45:16",
    subject: "沟通商机",
    contactPerson: "李强东",
    communicator: "李三",
    result: "没有下单欲望，目前的商机已经结束",
    attachments: [],
  },
]);

// 商机记录列表数据
const opportunitiesList = ref([
  {
    id: 1,
    name: '"星火"计划',
    successRate: "86.48%",
    budget: "￥8,000,000.00",
    time: "2025 年 6 月 3 日 18:01:12",
    remark: "此商机负责人与公司关系良好，大家可以多多接洽订单",
    expanded: false,
    products: [
      {
        id: 1,
        name: "大雅宏达400W作伴信号调制器 保密场所...",
        specification: "DYDS-NADP-9",
        quantity: 19,
        status: "已在其他平台采购",
      },
      {
        id: 2,
        name: "大雅宏达400W作伴信号调制器 保密场所...",
        specification: "DYDS-NADP-9",
        quantity: 19,
        status: "已转订单",
      },
    ],
  },
  {
    id: 2,
    name: '"星火"计划',
    successRate: "86.48%",
    budget: "￥8,000,000.00",
    time: "2025 年 6 月 3 日 18:01:12",
    remark: "此商机负责人与公司关系良好，大家可以多多接洽订单",
    expanded: false,
    products: [
      {
        id: 3,
        name: "大雅宏达400W作伴信号调制器 保密场所...",
        specification: "DYDS-NADP-9",
        quantity: 19,
        status: "已在其他平台采购",
      },
      {
        id: 4,
        name: "大雅宏达400W作伴信号调制器 保密场所...",
        specification: "DYDS-NADP-9",
        quantity: 19,
        status: "已转订单",
      },
    ],
  },
]);

// 订单记录列表数据
const ordersList = ref([
  {
    id: 1,
    orderNo: "**************",
    platform: "京东",
    amount: "￥ 81,947.75",
    status: "已完成",
    recipient: "李先生",
    address: "广东省广州市番禺区五洲城商务中心 C 座",
    createTime: "2025 年 6 月 3 日 18:01:12",
  },
  {
    id: 2,
    orderNo: "**************",
    platform: "京东",
    amount: "￥ 81,947.75",
    status: "已完成",
    recipient: "李先生",
    address: "广东省广州市番禺区五洲城商务中心 C 座",
    createTime: "2025 年 6 月 3 日 18:01:12",
  },
  {
    id: 3,
    orderNo: "**************",
    platform: "京东",
    amount: "￥ 81,947.75",
    status: "已完成",
    recipient: "李先生",
    address: "广东省广州市番禺区五洲城商务中心 C 座",
    createTime: "2025 年 6 月 3 日 18:01:12",
  },
]);

// 财务信息数据
const financeInfo = ref({
  accountName: "中国人民解放军国防科技大学学系统工程学院收款户",
  bankName: "工商银行长沙市银河支行",
  bankAccount: "1901011129200057134",
  creditBasis: "订单收货",
  settlementMethod1: "月结",
  settlementDate1: "每月 15 号",
  settlementMethod2: "固定账期",
  settlementDate2: "60 天",
  settlementMethod3: "现销现结",
});

// 发票抬头数据
const invoiceHeaders = ref([
  {
    id: 1,
    title: "国防科技大学",
    taxNumber: "12430000444882446W",
    address: "湖南省长沙市德雅路109号",
    phone: "",
    bankName: "工商银行长沙市银河支行",
    bankAccount: "1901011129200057134",
  },
  {
    id: 2,
    title: "国防科技大学",
    taxNumber: "12430000444882446W",
    address: "湖南省长沙市德雅路109号",
    phone: "",
    bankName: "工商银行长沙市银河支行",
    bankAccount: "1901011129200057134",
  },
]);

// 切换联系人卡片展开/收起状态
const toggleContactExpand = (contactId) => {
  const contact = contacts.value.find((c) => c.id === contactId);
  if (contact) {
    contact.expanded = !contact.expanded;
  }
};

// 切换商机卡片展开/收起状态
const toggleOpportunityExpand = (opportunityId) => {
  const opportunity = opportunities.value.find((o) => o.id === opportunityId);
  if (opportunity) {
    opportunity.expanded = !opportunity.expanded;
  }
};

// 获取商品状态颜色
const getProductStatusColor = (status) => {
  switch (status) {
    case "已转订单":
      return "green";
    case "已在其他平台采购":
      return "orange";
    default:
      return "gray";
  }
};

// 处理可见性变化
const handleVisibleChange = (val) => {
  emit("update:visible", val);
  if (!val) {
    // 当抖屉关闭时触发close事件
    emit("close");
  }
};

// 处理关闭按钮点击
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 处理确认按钮点击
const handleConfirm = () => {
  emit("confirm");
  emit("update:visible", false);
};

// 分配销售人员功能已移动到客户列表页面

// 打开联系人模态框
const openContactModal = (contact = null) => {
  // 重置当前联系人数据
  Object.keys(contactForm).forEach((key) => {
    contactForm[key] =
      contact && contact[key] !== undefined ? contact[key] : "";
  });

  if (contact) {
    contactForm.id = contact.id;
    contactModal.title = "编辑联系人";
  } else {
    contactForm.id = undefined;
    contactModal.title = "新增联系人";
  }

  contactModal.visible = true;
};

// 添加联系人 - 快捷方式
const addContact = () => {
  openContactModal();
};

// 编辑联系人 - 快捷方式
const editContact = (contact) => {
  openContactModal(contact);
};

// 处理联系人保存成功
const handleContactSaved = (contactData) => {
  if (!contactData) return;

  try {
    if (contactData.id) {
      // 编辑模式
      const index = contacts.value.findIndex(
        (item) => item.id === contactData.id
      );
      if (index !== -1) {
        // 更新现有联系人
        contacts.value[index] = { ...contactData };
      }
    } else {
      // 新增模式
      contacts.value.unshift(contactData);
    }

    // 刷新数据
    emit("refresh");
  } catch (error) {
    console.error("处理联系人数据失败", error);
  }
};

// 删除联系人
const deleteContact = (contact) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除联系人 ${contact.name} 吗？`,
    onOk: () => {
      // 删除联系人
      const index = contacts.value.findIndex((item) => item.id === contact.id);
      if (index !== -1) {
        contacts.value.splice(index, 1);
      }
      Message.success("删除联系人成功");
      emit("refresh");
      emit("action", "contactDeleted", {
        customerId: props.customerId,
        contactId: contact.id,
      });
    },
  });
};

// 添加跟进记录
const addFollowRecord = () => {
  followModal.title = "添加跟进记录";
  followModal.visible = true;
};

// 保存跟进记录
const handleSaveFollowRecord = (followData, done) => {
  if (!followData) {
    done(false);
    return;
  }

  try {
    // 模拟保存操作
    setTimeout(() => {
      // 更新跟进记录列表
      if (followData.id) {
        // 编辑模式
        const index = followRecords.findIndex(
          (item) => item.id === followData.id
        );
        if (index !== -1) {
          followRecords[index] = { ...followData };
        }
      } else {
        // 新增模式
        const newRecord = {
          id: Date.now(),
          customerId: props.customerId,
          ...followData,
          createdAt: new Date().toISOString(),
        };
        followRecords.unshift(newRecord);
      }

      // 更新客户状态
      if (followData.status && customer.value) {
        customer.value.status = followData.status;
        customer.value.statusName = getStatusText(followData.status);

        // 通知主页面更新状态
        emit("action", "updateStatus", {
          customerId: props.customerId,
          status: followData.status,
        });
      }

      followModal.visible = false;
      Message.success("跟进记录保存成功");
      done();
    }, 500);
  } catch (error) {
    console.error("保存跟进记录失败", error);
    Message.error("保存跟进记录失败");
    done(false);
  }
};

// 打开沟通记录弹窗
const openCommunicationModal = () => {
  communicationModal.title = "新增沟通记录";
  communicationModal.visible = true;
};

// 从沟通记录弹窗中新增联系人
const addContactFromCommunication = () => {
  // 先隐藏沟通记录弹窗
  communicationModal.visible = false;

  // 然后打开新增联系人弹窗
  contactForm.fromCommunication = true;
  addContact();
};

// 处理沟通记录保存成功
const handleCommunicationSaved = (communicationData) => {
  if (!communicationData) return;

  // 添加到沟通记录列表
  communications.value.unshift(communicationData);

  // 更新客户最后联系时间
  if (customer.value) {
    customer.value.lastContactTime = communicationData.time;
  }

  // 刷新数据
  emit("refresh");
};

// 打开商机模态框
const openOpportunityModal = (opportunity = null) => {
  // 重置当前商机数据
  Object.keys(currentOpportunity).forEach((key) => {
    currentOpportunity[key] =
      opportunity && opportunity[key] !== undefined ? opportunity[key] : "";
  });

  // 设置模态框标题
  opportunityModal.title =
    opportunity && opportunity.id ? "编辑商机" : "新增商机";
  opportunityModal.visible = true;
};

// 处理商机保存成功
const handleOpportunitySaved = (opportunityData, done) => {
  if (!opportunityData) {
    done(false);
    return;
  }

  try {
    // 更新商机列表
    if (opportunityData.id) {
      // 编辑模式
      const index = opportunities.value.findIndex(
        (item) => item.id === opportunityData.id
      );
      if (index !== -1) {
        opportunities.value[index] = { ...opportunityData };
      }
    } else {
      // 新增模式
      opportunities.value.unshift(opportunityData);
    }

    // 通知主页面更新数据
    emit("action", "opportunityAdded", {
      customerId: props.customerId,
    });

    // 关闭模态框
    opportunityModal.visible = false;

    // 刷新数据
    emit("refresh");

    done(true);
  } catch (error) {
    console.error("保存商机失败", error);
    done(false);
  }
};

// 客户来源文本
const getSourceText = (source) => {
  return CustomerSourceDescriptions[source] || "未知来源";
};

// 客户来源颜色
const getSourceColor = (source) => {
  switch (source) {
    case CustomerSourceEnum.ACTIVE_ACQUISITION:
      return "blue"; // 主动获取
    case CustomerSourceEnum.REFERRAL:
      return "orange"; // 推荐介绍
    default:
      return "gray";
  }
};

// 客户状态文本
const getStatusText = (status) => {
  return CustomerStatusDescriptions[status] || "未知状态";
};

// 客户状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case CustomerStatusEnum.ACTIVE:
      return "green"; // 在营
    case CustomerStatusEnum.SUSPENDED:
      return "orange"; // 暂停营业
    case CustomerStatusEnum.CLOSED:
      return "red"; // 关闭
    default:
      return "gray";
  }
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size || size === 0) return "";

  const units = ["B", "KB", "MB", "GB"];
  let index = 0;
  let fileSize = parseInt(size);

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(index === 0 ? 0 : 1)}${units[index]}`;
};

// 附件预览
const previewAttachment = (attachment) => {
  if (!attachment.fileUrl) {
    Message.warning("文件链接不存在");
    return;
  }

  // 在新窗口中打开文件进行预览
  window.open(attachment.fileUrl, "_blank");
};

// 附件下载
const downloadAttachment = (attachment) => {
  if (!attachment.fileUrl) {
    Message.warning("文件链接不存在");
    return;
  }

  try {
    // 创建下载链接
    const link = document.createElement("a");
    link.href = attachment.fileUrl;
    link.download = attachment.fileName || "附件";
    link.target = "_blank";

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Message.success(`开始下载：${attachment.fileName || "附件"}`);
  } catch (error) {
    console.error("下载失败:", error);
    Message.error("下载失败，请稍后重试");
  }
};
</script>

<style scoped>
.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 80px;
  flex-shrink: 0;
}

.customer-header {
  position: relative;
}
</style>
