<template>
  <div class="ma-content-block p-4">
    <!-- 状态标签页 -->
    <div class="mb-4">
      <a-tabs :active-key="activeTab" @change="handleTabChange">
        <a-tab-pane key title="全部"></a-tab-pane>
        <a-tab-pane key="2" title="待审核"></a-tab-pane>
        <a-tab-pane key="1" title="已审核"></a-tab-pane>
        <a-tab-pane key="3" title="已驳回"></a-tab-pane>
        <a-tab-pane key="0" title="已注册"></a-tab-pane>
      </a-tabs>
    </div>

    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :row-class="() => ''" :row-selection="{ type: 'none' }">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag
          :color="record.status === 1 ? 'green' : record.status === 2 ? 'orange' : record.status === 3 ? 'red' : record.status === 0 ? 'gray' : 'gray'">{{
            record.status === 1 ? '已审核' : record.status === 2 ? '待审核' : record.status === 3 ? '已驳回' : record.status === 0
              ? '已注册' : '禁用' }}</a-tag>
      </template>

      <template #createdAt="{ record }">{{ formatDate(record.createdAt) }}</template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="openDetails(record)">详情</a-button>
          <a-button type="text" size="small" @click="openEdit(record)">编辑</a-button>
        </div>
      </template>
    </ma-crud>

    <!-- 服务商详情抽屉 -->
    <a-drawer :width="'80%'" :visible="detailVisible" @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail" unmountOnClose :footer="true">
      <template #title>服务商详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <!-- 修改密码按钮 -->
          <a-button type="outline" @click="openChangePassword" class="mr-2">修改密码</a-button>
          <!-- 修改业务员按钮 -->
          <a-button type="outline" @click="handleEditSalesman(detailRecord)" class="mr-2"
            v-if="detailRecord.status === 1">修改业务员</a-button>
          <a-button @click="closeDetail" class="mr-2">取消</a-button>
          <!-- 编辑模式下显示保存按钮，查看模式下显示确定按钮 -->
          <a-button type="primary" @click="isEditing ? saveEdit() : closeDetail()"
            v-if="detailActiveTab !== '6' && detailActiveTab !== '5'" class="mr-2">{{ isEditing ? '保存' : '确定'
            }}</a-button>
          <a-button type="primary" @click="savePlatformRates()" v-if="isEditing && detailActiveTab === '6'"
            class="mr-2">保存平台费率</a-button>
          <!-- 审核中状态下显示审核按钮 -->
          <a-button v-if="detailRecord.status === 2" type="primary" status="success"
            @click="handleAudit(detailRecord)">审核</a-button>
        </div>
      </template>

      <div class="provider-detail-container">
        <!-- 服务商基本信息头部 -->
        <div class="provider-header mb-4 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <a-avatar :size="64" :image-url="detailRecord.logo || '/assets/images/default-company.png'"
                class="mr-4"></a-avatar>
              <div class="flex items-center">
                <h3 class="text-lg font-medium">{{ detailRecord.companyName || '未知服务商' }}</h3>
              </div>
            </div>
            <div>
              <a-tag
                :color="detailRecord.status === 1 ? 'green' : detailRecord.status === 2 ? 'orange' : detailRecord.status === 3 ? 'red' : 'gray'"
                size="large">{{ detailRecord.status === 1 ? '正常' : detailRecord.status === 2 ? '审核中' :
                  detailRecord.status
                    === 3 ? '驳回' : '禁用' }}</a-tag>
            </div>
          </div>

          <div class="provider-stats grid grid-cols-2 gap-4">
            <div class="stat-item flex items-center">
              <div class="text-gray-500 whitespace-nowrap mr-2">订单总量：</div>
              <div class="whitespace-nowrap">{{ detailRecord.orderCount || '0' }}</div>
            </div>
            <div class="stat-item flex items-center">
              <div class="text-gray-500 whitespace-nowrap mr-2">订单总金额：</div>
              <div class="whitespace-nowrap">{{ formatAmount(detailRecord.orderAmount) || '0.00' }}</div>
            </div>
            <div class="stat-item flex items-center">
              <div class="text-gray-500 whitespace-nowrap mr-2">入驻时间：</div>
              <div class="whitespace-nowrap">{{ detailRecord._formattedCreatedAt || formatDate(detailRecord.createdAt)
                ||
                '-' }}</div>
            </div>
            <!-- <div class="stat-item flex items-center">
              <div class="text-gray-500 whitespace-nowrap mr-2">服务商等级：</div>
              <div class="whitespace-nowrap">{{ detailRecord.level || '-' }}</div>
            </div>-->
          </div>
        </div>

        <!-- 选项卡导航 -->
        <a-tabs :active-key="detailActiveTab" @update:active-key="(key) => detailActiveTab = key"
          @change="detailHandleTabChange">
          <a-tab-pane key="1" title="基本信息">
            <div class="info-section">
              <div class="grid grid-cols-2 gap-4">
                <!-- 服务商编号（不可编辑） -->
                <div class="info-item">
                  <div class="text-gray-500">服务商编号：</div>
                  <div>{{ detailRecord.id || '-' }}</div>
                </div>

                <!-- 法人代表 -->
                <div class="info-item">
                  <div class="text-gray-500">法人代表：</div>
                  <div v-if="!isEditing">{{ detailRecord.legalPersonName || '-' }}</div>
                  <a-input v-else v-model="detailRecord.legalPersonName" placeholder="请输入法人代表" />
                </div>

                <!-- 服务商名称 -->
                <div class="info-item">
                  <div class="text-gray-500">服务商名称：</div>
                  <div v-if="!isEditing">{{ detailRecord.companyName || '-' }}</div>
                  <a-input v-else v-model="detailRecord.companyName" placeholder="请输入服务商名称" />
                </div>

                <!-- 统一社会信用代码 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px ">统一社会信用代码：</div>
                  <div v-if="!isEditing">{{ detailRecord.creditCode || '-' }}</div>
                  <a-input v-else v-model="detailRecord.creditCode" placeholder="请输入统一社会信用代码" />
                </div>

                <!-- 所在地区 -->
                <div class="info-item">
                  <div class="text-gray-500">所在地区：</div>
                  <div v-if="!isEditing">{{ formatRegionDisplay(detailRecord.area) || '-' }}</div>
                  <a-cascader path-mode v-else v-model="detailRecord.regionCodes" :options="regionOptions"
                    placeholder="请选择所在地区" allow-clear expand-trigger="click" check-strictly
                    @change="handleRegionChange" />
                </div>

                <!-- 详细地址 -->
                <div class="info-item">
                  <div class="text-gray-500">详细地址：</div>
                  <div v-if="!isEditing">{{ detailRecord.detailAddress || '-' }}</div>
                  <a-input v-else v-model="detailRecord.detailAddress" placeholder="请输入详细地址" />
                </div>

                <!-- 纳税等级 -->
                <div class="info-item">
                  <div class="text-gray-500">纳税等级：</div>
                  <div v-if="!isEditing">{{ detailRecord.taxLevel || '-' }}</div>
                  <a-radio-group v-else v-model="detailRecord.taxLevel">
                    <a-radio value="A">A</a-radio>
                    <a-radio value="B">B</a-radio>
                    <a-radio value="C">C</a-radio>
                    <a-radio value="D">D</a-radio>
                    <a-radio value="M">M</a-radio>
                  </a-radio-group>
                  <!-- <a-input v-else v-model="detailRecord.taxLevel" placeholder="请输入纳税等级" /> -->
                </div>

                <!-- 经营者姓名 -->
                <div class="info-item">
                  <div class="text-gray-500">经营者姓名：</div>
                  <div v-if="!isEditing">{{ detailRecord.legalRepresentative || '-' }}</div>
                  <a-input v-else v-model="detailRecord.legalRepresentative" placeholder="请输入经营者姓名" />
                </div>

                <!-- 身份证号码 -->
                <div class="info-item">
                  <div class="text-gray-500">身份证号码：</div>
                  <div v-if="!isEditing">{{ detailRecord.idCardNumber || '-' }}</div>
                  <a-input v-else v-model="detailRecord.idCardNumber" placeholder="请输入身份证号码" />
                </div>

                <!-- 经营范围 -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500">经营范围：</div>
                  <div v-if="!isEditing">{{ detailRecord.businessScope || '-' }}</div>
                  <a-textarea v-else v-model="detailRecord.businessScope" placeholder="请输入经营范围" :rows="4" />
                </div>
                <!-- 办公地址： -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500">办公地址：</div>
                  <div v-if="!isEditing">{{ detailRecord.officeAddress || '-' }}</div>
                  <a-textarea v-else v-model="detailRecord.officeAddress" placeholder="请输入办公地址" :rows="4" />
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="2" title="联系信息">
            <div class="info-section">
              <!-- 多个联系人循环渲染 -->
              <div v-if="contactsList && contactsList.length > 0">
                <div v-for="(contact, index) in contactsList" :key="index" class="contact-item">
                  <h4 class="contact-title">联系人{{ index + 1 }}</h4>
                  <div class="grid grid-cols-2 gap-4 mb-6">
                    <!-- 联系人姓名 -->
                    <div class="info-item">
                      <div class="text-gray-500">联系人姓名：</div>
                      <div v-if="!isEditing">{{ contact.name || '-' }}</div>
                      <a-input v-else v-model="contact.name" placeholder="请输入联系人姓名" />
                    </div>

                    <!-- 联系电话 -->
                    <div class="info-item">
                      <div class="text-gray-500">联系电话：</div>
                      <div v-if="!isEditing">{{ contact.phone || '-' }}</div>
                      <a-input v-else v-model="contact.phone" placeholder="请输入联系电话" />
                    </div>

                    <!-- 邮箱 -->
                    <div class="info-item">
                      <div class="text-gray-500">邮箱：</div>
                      <div v-if="!isEditing">{{ contact.email || '-' }}</div>
                      <a-input v-else v-model="contact.email" placeholder="请输入联系邮箱" />
                    </div>

                    <!-- 职务 -->
                    <div class="info-item">
                      <div class="text-gray-500">职务：</div>
                      <div v-if="!isEditing">{{ contact.position || '-' }}</div>
                      <a-input v-else v-model="contact.position" placeholder="请输入职务" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无联系人信息时的提示 -->
              <div v-else class="no-contact-info">
                <a-empty description="暂无联系人信息" />
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="3" title="财务信息">
            <div class="info-section">
              <div class="grid grid-cols-2 gap-4">
                <!-- 企业名称 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">企业名称：</div>
                  <div v-if="!isEditing">{{ detailRecord.companyName || '-' }}</div>
                  <a-input v-else v-model="detailRecord.companyName" placeholder="请输入企业名称" />
                </div>

                <!-- 纳税人识别号 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">纳税人识别号：</div>
                  <div v-if="!isEditing">{{ detailRecord.taxNumber || '-' }}</div>
                  <a-input v-else v-model="detailRecord.taxNumber" placeholder="请输入纳税人识别号" />
                </div>

                <!-- 地址 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">地址：</div>
                  <div v-if="!isEditing">{{ detailRecord.address || '-' }}</div>
                  <a-input v-else v-model="detailRecord.address" placeholder="请输入地址" />
                </div>

                <!-- 电话 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">电话：</div>
                  <div v-if="!isEditing">{{ detailRecord.phone || '-' }}</div>
                  <a-input v-else v-model="detailRecord.phone" placeholder="请输入电话" />
                </div>

                <!-- 开户行 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">开户行：</div>
                  <div v-if="!isEditing">{{ detailRecord.bank_name || '-' }}</div>
                  <a-input v-else v-model="detailRecord.bank_name" placeholder="请输入开户行" />
                </div>

                <!-- 开户支行 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">开户网点：</div>
                  <div v-if="!isEditing">{{ detailRecord.bank_branch || '-' }}</div>
                  <a-input v-else v-model="detailRecord.bank_branch" placeholder="请输入开户网点" />
                </div>

                <!-- 银行账号 -->
                <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">银行账号：</div>
                  <div v-if="!isEditing">{{ detailRecord.bank_account || '-' }}</div>
                  <a-input v-else v-model="detailRecord.bank_account" placeholder="请输入银行账号" />
                </div>

                <!-- 结算方式 -->
                <!-- <div class="info-item">
                  <div class="text-gray-500" style="width: 130px">结算方式：</div>
                  <div v-if="!isEditing">{{ detailRecord.settlement_type || '-' }}</div>
                  <a-input v-else v-model="detailRecord.settlement_type" placeholder="请输入结算方式" />
                </div> -->
              </div>

              <!-- 开户证明图片 -->
              <div class="info-item mt-4">
                <div class="text-gray-500 mb-2">开户证明：</div>
                <div class="image-preview relative rounded-lg overflow-hidden bg-gray-50" v-if="!isEditing">
                  <a-image v-if="detailRecord.license" :src="detailRecord.license" width="150px"
                    height="150px" fit="cover" />
                  <div v-else class="no-image">暂无图片</div>
                </div>
                <div v-else>
                  <div class="upload-image-container">
                    <div class="image-list w-full" v-if="bankCertificateFiles.length > 0">
                      <a-button class="delete-btn absolute right-2 top-2 z-10" @click="() => {
                        bankCertificateFiles = [];
                        detailRecord.license = '';
                      }">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                      <div class="image-container w-full h-[200px] relative rounded-lg overflow-hidden">
                        <a-image width="100%" height="200" :src="bankCertificateFiles[0] && bankCertificateFiles[0].url"
                          fit="cover" :preview="false" />
                      </div>
                    </div>

                    <a-upload v-if="bankCertificateFiles.length === 0" :show-file-list="false"
                      :file-list="bankCertificateFiles" @change="handleBankCertificateChange"
                      :accept="'.jpg,.jpeg,.png,.gif'" class="w-full">
                      <template #upload-button>
                        <div
                          class="upload-button-container flex flex-col items-center justify-center w-full h-[200px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gray-100 transition-colors cursor-pointer">
                          <icon-upload class="text-2xl text-gray-400 mb-2" />
                          <div class="text-gray-600">点击上传开户证明</div>
                        </div>
                      </template>
                    </a-upload>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="4" title="资质审核">
            <div class="qualification-section grid grid-cols-4 gap-6">
              <!-- 营业执照 -->
              <div class="qualification-item">
                <h4 class="text-base font-medium mb-2 text-center text-blue-500">营业执照</h4>
                <div class="image-preview relative rounded-lg overflow-hidden bg-gray-50" v-if="!isEditing">
                  <a-image v-if="detailRecord.businessLicense" :src="detailRecord.businessLicense" width="150px"
                    height="150px" fit="cover" />
                  <div v-else class="no-image">暂无图片</div>
                </div>
                <div v-else>
                  <div class="upload-image-container">
                    <div class="image-list w-full" v-if="businessLicenseFiles.length > 0">
                      <a-button class="delete-btn absolute right-2 top-2 z-10" @click="() => {
                        businessLicenseFiles = [];
                        detailRecord.businessLicense = '';
                      }">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                      <div class="image-container w-full h-[200px] relative rounded-lg overflow-hidden">
                        <a-image width="100%" height="200" :src="businessLicenseFiles[0] && businessLicenseFiles[0].url"
                          fit="cover" :preview="false" />
                      </div>
                    </div>

                    <a-upload v-if="businessLicenseFiles.length === 0" :show-file-list="false"
                      :file-list="businessLicenseFiles" @change="handleBusinessLicenseChange"
                      :accept="'.jpg,.jpeg,.png,.gif'" class="w-full">
                      <template #upload-button>
                        <div
                          class="upload-button-container flex flex-col items-center justify-center w-full h-[200px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gray-100 transition-colors cursor-pointer">
                          <icon-upload class="text-2xl text-gray-400 mb-2" />
                          <div class="text-gray-600">点击上传营业执照</div>
                        </div>
                      </template>
                    </a-upload>
                  </div>
                </div>
              </div>

              <!-- 资质证书 -->
              <div class="qualification-item">
                <h4 class="text-base font-medium mb-2 text-center text-blue-500">法人身份证（正面）</h4>
                <div class="image-preview relative rounded-lg overflow-hidden bg-gray-50" v-if="!isEditing">
                  <a-image v-if="detailRecord.identity_card_portrait || detailRecord.idCardFront"
                    :src="detailRecord.identity_card_portrait || detailRecord.idCardFront" width="150px" height="200"
                    fit="cover" />
                  <div v-else class="no-image flex items-center justify-center h-[200px] text-gray-400">
                    <icon-file-image class="text-4xl mb-2" />
                    <span class="ml-2">暂无图片</span>
                  </div>
                </div>
                <div v-else>
                  <div class="upload-image-container">
                    <div class="image-list w-full" v-if="idCardFrontFiles.length > 0">
                      <a-button class="delete-btn absolute right-2 top-2 z-10" @click="() => {
                        idCardFrontFiles = [];
                        detailRecord.identity_card_portrait = '';
                        detailRecord.idCardFront = '';
                      }">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                      <div class="image-container w-full h-[200px] relative rounded-lg overflow-hidden">
                        <a-image width="100%" height="200" :src="idCardFrontFiles[0] && idCardFrontFiles[0].url"
                          fit="cover" :preview="false" />
                      </div>
                    </div>

                    <a-upload v-if="idCardFrontFiles.length === 0" :show-file-list="false" :file-list="idCardFrontFiles"
                      @change="handleIdCardFrontChange" :accept="'.jpg,.jpeg,.png,.gif'" class="w-full">
                      <template #upload-button>
                        <div
                          class="upload-button-container flex flex-col items-center justify-center w-full h-[200px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gray-100 transition-colors cursor-pointer">
                          <icon-upload class="text-2xl text-gray-400 mb-2" />
                          <div class="text-gray-600">点击上传身份证正面</div>
                        </div>
                      </template>
                    </a-upload>
                  </div>
                </div>
              </div>

              <!-- 法人身份证（反面） -->
              <div class="qualification-item">
                <h4 class="text-base font-medium mb-2 text-center text-blue-500">法人身份证（反面）</h4>
                <div class="image-preview relative rounded-lg overflow-hidden bg-gray-50" v-if="!isEditing">
                  <a-image
                    v-if="detailRecord.identity_card_national_emblem || detailRecord.identity_card_back || detailRecord.idCardBack"
                    :src="detailRecord.identity_card_national_emblem || detailRecord.identity_card_back || detailRecord.idCardBack"
                    width="150px" height="200" fit="cover" />
                  <div v-else class="no-image flex items-center justify-center h-[200px] text-gray-400">
                    <icon-file-image class="text-4xl mb-2" />
                    <span class="ml-2">暂无图片</span>
                  </div>
                </div>
                <div v-else>
                  <div class="upload-image-container">
                    <div class="image-list w-full" v-if="idCardBackFiles.length > 0">
                      <a-button class="delete-btn absolute right-2 top-2 z-10" @click="() => {
                        idCardBackFiles = [];
                        detailRecord.identity_card_national_emblem = '';
                        detailRecord.identity_card_back = '';
                        detailRecord.idCardBack = '';
                      }">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                      <div class="image-container w-full h-[200px] relative rounded-lg overflow-hidden">
                        <a-image width="100%" height="200" :src="idCardBackFiles[0] && idCardBackFiles[0].url"
                          fit="cover" :preview="false" />
                      </div>
                    </div>

                    <a-upload v-if="idCardBackFiles.length === 0" :show-file-list="false" :file-list="idCardBackFiles"
                      @change="handleIdCardBackChange" :accept="'.jpg,.jpeg,.png,.gif'" class="w-full">
                      <template #upload-button>
                        <div
                          class="upload-button-container flex flex-col items-center justify-center w-full h-[200px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gray-100 transition-colors cursor-pointer">
                          <icon-upload class="text-2xl text-gray-400 mb-2" />
                          <div class="text-gray-600">点击上传身份证反面</div>
                        </div>
                      </template>
                    </a-upload>
                  </div>
                </div>
              </div>

              <!-- 评级证明 -->
              <div class="qualification-item">
                <h4 class="text-base font-medium mb-2 text-center text-blue-500">评级证明</h4>
                <div class="image-preview relative rounded-lg overflow-hidden bg-gray-50" v-if="!isEditing">
                  <a-image
                    v-if="detailRecord.rating || detailRecord.ratingCertificate || detailRecord.rating_certificate"
                    :src="detailRecord.rating || detailRecord.ratingCertificate || detailRecord.rating_certificate"
                    width="150px" height="200" fit="cover" />
                  <div v-else class="no-image flex items-center justify-center h-[200px] text-gray-400">
                    <icon-file-image class="text-4xl mb-2" />
                    <span class="ml-2">暂无图片</span>
                  </div>
                </div>
                <div v-else>
                  <div class="upload-image-container">
                    <div class="image-list w-full" v-if="ratingCertificateFiles.length > 0">
                      <a-button class="delete-btn absolute right-2 top-2 z-10" @click="() => {
                        ratingCertificateFiles = [];
                        detailRecord.rating = '';
                        detailRecord.ratingCertificate = '';
                      }">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                      <div class="image-container w-full h-[200px] relative rounded-lg overflow-hidden">
                        <a-image width="100%" height="200"
                          :src="ratingCertificateFiles[0] && ratingCertificateFiles[0].url" fit="cover"
                          :preview="false" />
                      </div>
                    </div>

                    <a-upload v-if="ratingCertificateFiles.length === 0" :show-file-list="false"
                      :file-list="ratingCertificateFiles" @change="handleRatingCertificateChange"
                      :accept="'.jpg,.jpeg,.png,.gif'" class="w-full">
                      <template #upload-button>
                        <div
                          class="upload-button-container flex flex-col items-center justify-center w-full h-[200px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-gray-100 transition-colors cursor-pointer">
                          <icon-upload class="text-2xl text-gray-400 mb-2" />
                          <div class="text-gray-600">点击上传评级证明</div>
                        </div>
                      </template>
                    </a-upload>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="5" title="框架协议">
            <div class="agreement-section">
              <!-- 查看模式下的协议表格 -->
              <a-table v-if="!isEditing" :columns="agreementColumns" :data="agreementData" :pagination="{
                total: agreementTotal,
                current: agreementPagination.page,
                pageSize: agreementPagination.pageSize,
                showTotal: true,
                showJumper: true,
                showPageSize: true,
                pageSizeOptions: [10, 20, 50, 100]
              }" @page-change="handleAgreementPageChange" @page-size-change="handleAgreementPageSizeChange"
                :bordered="true" size="small">
                <template #operations="{ record }">
                  <a-button type="text" size="small" @click="downloadAgreement(record)">
                    <template #icon>
                      <icon-download />
                    </template>
                    下载
                  </a-button>
                </template>
              </a-table>

              <!-- 编辑模式下的协议表格 -->
              <div v-else>
                <a-button type="primary" class="mb-4" @click="showUploadAgreementDialog">
                  <template #icon>
                    <icon-plus></icon-plus>
                  </template>
                  添加协议
                </a-button>

                <a-table :columns="agreementColumns" :data="agreementData" :pagination="{
                  total: agreementTotal,
                  current: agreementPagination.page,
                  pageSize: agreementPagination.pageSize,
                  showTotal: true,
                  showJumper: true,
                  showPageSize: true,
                  pageSizeOptions: [10, 20, 50, 100]
                }" @page-change="handleAgreementPageChange" @page-size-change="handleAgreementPageSizeChange"
                  :bordered="true" size="small">
                  <template #operations="{ record }">
                    <a-button type="text" size="small" @click="downloadAgreement(record)">
                      <template #icon>
                        <icon-download />
                      </template>
                      下载
                    </a-button>
                  </template>
                </a-table>
              </div>

              <!-- 上传框架协议对话框 -->
              <upload-agreement-dialog :visible="uploadAgreementDialogVisible" :providerId="detailRecord.id"
                @update:visible="val => uploadAgreementDialogVisible = val"
                @upload-success="handleAgreementUploadSuccess" />
            </div>
          </a-tab-pane>
          <!-- 平台费率设置 -->
          <a-tab-pane key="6" title="平台费率">
            <div class="platform-rates-section">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium">平台费率设置</h3>

              </div>

              <div class="grid grid-cols-1 gap-4">
                <a-card v-for="platform in platforms" :key="platform.id" class="platform-rate-card">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <icon-shop class="mr-2 text-blue-500" />
                      <span class="font-medium">{{ platform.name }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <a-input-number v-model="platform.rate" :min="0" :max="100" :precision="2" :step="0.1"
                        mode="button" size="large" class="w-34" style="width: 150px;">
                        <template #append>
                          <span class="px-2">%</span>
                        </template>
                      </a-input-number>
                    </div>
                  </div>
                </a-card>
              </div>

              <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center text-gray-500">
                  <icon-info-circle class="mr-2" />
                  <span>说明：费率范围为0-100%，精确到小数点后2位</span>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>

    <!-- 审核弹窗 -->
    <a-modal :visible="auditVisible" title="审核" @ok="submitAudit" @cancel="cancelAudit" :ok-loading="auditLoading"
      @update:visible="(val) => auditVisible = val">
      <a-form :model="auditForm" ref="auditFormRef">
        <a-form-item field="status" label="审核状态" :rules="[{ required: true, message: '请选择审核状态' }]">
          <a-radio-group v-model="auditForm.status" @change="handleAuditStatusChange">
            <a-radio :value="1">通过</a-radio>
            <a-radio :value="3">驳回</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="auditForm.status === 1" field="salesmanIds" label="所属业务员"
          :rules="[{ required: false, message: '请选择业务员' }]">
          <a-select v-model="auditForm.salesmanIds" placeholder="请选择业务员" allow-clear multiple>
            <a-option v-for="item in salesmanOptions" :key="item.id" :value="item.id"
              :label="item.realname || item.username">{{ item.realname || item.username }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="reason" :label="auditForm.status === 1 ? '审核意见' : '驳回原因'"
          :rules="[{ required: auditForm.status === 3, message: '请输入驳回原因' }]">
          <a-textarea v-model="auditForm.reason" :placeholder="auditForm.status === 1 ? '请输入审核意见（选填）' : '请输入驳回原因'"
            :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 修改密码模态框 -->
    <change-password-modal :visible="changePasswordVisible" @update:visible="val => changePasswordVisible = val"
      :provider-id="detailRecord.id" @success="handlePasswordChangeSuccess" />

    <!-- 修改业务员弹窗 -->
    <a-modal :visible="editSalesmanVisible" title="修改业务员" @ok="submitEditSalesman" @cancel="cancelEditSalesman"
      :ok-loading="editSalesmanLoading" @update:visible="(val) => editSalesmanVisible = val">
      <a-form :model="salesmanForm" ref="salesmanFormRef">
        <a-form-item field="salesmanIds" label="所属业务员" :rules="[{ required: false, message: '请选择业务员' }]">
          <a-select v-model="salesmanForm.salesmanIds" placeholder="请选择业务员" allow-clear multiple>
            <a-option v-for="item in salesmanOptions" :key="item.id" :value="item.id"
              :label="item.realname || item.username">{{ item.realname || item.username }}</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import UploadAgreementDialog from "@/components/master/cooperative/UploadAgreementDialog.vue";
import providerSystemApi from "@/api/provider/system.js";
import { Message, Modal } from "@arco-design/web-vue";
import provider from "@/api/master/provider";
import teamApi from "@/api/master/team";
import common from "@/api/common";
import ChangePasswordModal from "@/components/master/ChangePasswordModal.vue";

// 页面元数据
definePageMeta({
  name: "master-cooperative-providermanage-providerslist",
  path: "/master/cooperative/providermanage/providerslist",
  title: "服务商列表"
});

// CRUD组件引用
const crudRef = ref(null);

// 标签页状态
const activeTab = ref(""); // 默认显示全部

// 切换标签页
const handleTabChange = key => {
  // 更新当前标签页状态
  activeTab.value = key;

  // 构建查询参数
  const params = {};

  // 如果选择了状态标签，添加状态参数
  if (key !== "") {
    params.status = Number(key);
  }

  // 使用ma-crud组件的refresh方法刷新数据
  // 这会自动调用crud.api函数，即getProviderList
  if (crudRef.value) {
    crudRef.value.refresh(params);
  }
};

// 详情抽屉相关
const detailVisible = ref(false);
const detailRecord = ref({
  // 基本信息
  id: "*********",
  name: "深圳市科技创新有限公司",
  level: "A级",
  status: 1,
  order_count: 1280,
  order_amount: 1580000,
  created_at: "2024-01-15",
  logo: "",
  // 财务信息
  company_name: "深圳市科技创新有限公司",
  tax_number: "91440300MA5G9X7Y2R",
  address: "深圳市南山区科技园南区科技南十二路2号金蝶软件园B栋10层",
  phone: "0755-********",
  bank_name: "中国建设银行",
  bank_branch: "深圳科技园支行",
  bank_account: "4430 5566 7788 9900",
  settlement_type: "月结30天",
idCardNumber: "",
legalPersonName: "",
  // 省市区相关
  area: [], // 后端返回的省市区code数组
  regionCodes: [], // 级联选择器使用的字符串数组
  regionCodesNumber: [], // 数字类型的区域编码数组
  regionNames: [] // 区域名称数组
});

// 省市区级联选择器数据
const regionOptions = ref([]);
const regionTreeData = ref([]); // 存储完整的省市区树形数据

const detailActiveTab = ref("1");
const isEditing = ref(false);

// 平台费率相关
const platforms = ref([]);

// 框架协议相关
const agreementData = ref([]);
const agreementTotal = ref(0);
const agreementPagination = reactive({
  page: 1,
  pageSize: 10
});
const uploadAgreementDialogVisible = ref(false);

// 联系人列表计算属性
const contactsList = computed(() => {
  return detailRecord.value?.business_info?.contacts || [];
});

// 审核弹窗控制
const auditVisible = ref(false);
const auditLoading = ref(false);
const auditType = ref(1); // 1-通过，2-驳回
const auditForm = reactive({
  id: "",
  reason: "",
  status: 1,
  salesmanIds: []
});
const auditFormRef = ref(null);
const currentRecord = ref(null);

// 业务员相关变量
const salesmanOptions = ref([]);
const editSalesmanVisible = ref(false);
const editSalesmanLoading = ref(false);
const salesmanForm = reactive({
  id: "",
  salesmanIds: []
});
const salesmanFormRef = ref(null);

// 框架协议表格列定义
const agreementColumns = [
  {
    title: "文件名称",
    dataIndex: "fileName"
  },
  {
    title: "上传时间",
    dataIndex: "createdAt",
    render: ({ record }) => {
      if (!record.createdAt) return '-';
      // 时间戳是秒级，需要乘以1000转换为毫秒
      const timestamp = parseInt(record.createdAt) * 1000;
      return new Date(timestamp).toLocaleString("zh-CN");
    }
  },
  {
    title: "快递公司",
    dataIndex: "expressCompany"
  },
  {
    title: "快递单号",
    dataIndex: "expressNo"
  },
  {
    title: "操作",
    slotName: "operations"
  }
];

// 图片上传相关数据
const businessLicenseFiles = ref([]);
const idCardFrontFiles = ref([]);
const idCardBackFiles = ref([]);
const ratingCertificateFiles = ref([]);
const bankCertificateFiles = ref([]);

// 订单表格列定义
const orderColumns = [
  {
    title: "订单编号",
    dataIndex: "order_id"
  },
  {
    title: "订单金额",
    dataIndex: "amount"
  },
  {
    title: "订单状态",
    dataIndex: "status"
  },
  {
    title: "下单时间",
    dataIndex: "created_at"
  }
];

// 结算表格列定义
const settlementColumns = [
  {
    title: "结算单号",
    dataIndex: "settlement_id"
  },
  {
    title: "结算金额",
    dataIndex: "amount"
  },
  {
    title: "结算状态",
    dataIndex: "status"
  },
  {
    title: "结算时间",
    dataIndex: "settlement_time"
  }
];

// 打开详情抽屉
// 获取省市区数据
const fetchRegionTree = async () => {
  try {
    // 获取token
    const tool = await import("@/utils/tool");
    const token = tool.default.local.get("token");

    const params = {
      parentId: 0,
      excludeStreet: false
    };

    // 显示加载中提示
    Message.loading("正在加载省市区数据...");

    // 直接使用axios调用接口，手动设置请求头
    const axios = (await import("axios")).default;
    const { baseUrl } = await import("@/api/config");
    const result = await axios({
      url: `${baseUrl}/api/v1/master/region/tree`,
      method: "get",
      params,
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // 处理响应数据
    const response = result.data;

    if (response && response.code === 200 && response.data) {
      console.log("获取到省市区数据:", response.data);

      // 存储完整的树形数据，用于根据code查找名称
      regionTreeData.value = response.data;

      // 处理返回的数据，转换为级联选择器所需格式
      const formattedData = formatRegionData(response.data);
      regionOptions.value = formattedData;

      console.log("格式化后的省市区数据:", formattedData);
      Message.success("省市区数据加载成功");
    } else {
      console.error("获取省市区数据失败:", response);
      Message.error(response?.message || "获取省市区数据失败");
    }
  } catch (error) {
    console.error("获取省市区数据失败:", error);
    Message.error("获取省市区数据失败，请稍后再试");
  }
};

// 格式化省市区数据为级联选择器所需格式
const formatRegionData = data => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 检查是否有子级数据
    const hasChildren = item.children && Array.isArray(item.children) && item.children.length > 0;

    return {
      value: item.code,
      label: item.name,
      // 只有子级数据存在时才递归处理
      children: hasChildren ? formatRegionData(item.children) : []
    };
  });
};

// 根据省市区code数组查找对应的名称
const findRegionNamesByCodes = (codes) => {
  console.log("=== 查找省市区名称 ===");
  console.log("输入的codes:", codes);
  console.log("regionTreeData是否已加载:", regionTreeData.value.length > 0);
  console.log("regionTreeData内容:", regionTreeData.value);

  if (!codes || !Array.isArray(codes) || codes.length === 0) {
    console.log("codes为空或不是数组");
    return [];
  }

  if (!regionTreeData.value || regionTreeData.value.length === 0) {
    console.log("省市区数据还未加载完成");
    return [];
  }

  const names = [];
  let currentLevel = regionTreeData.value;

  for (let i = 0; i < codes.length; i++) {
    const code = codes[i];
    console.log(`查找第${i + 1}级，code: ${code}, 类型: ${typeof code}`);
    console.log(`当前级别数据:`, currentLevel);

    // 尝试不同的比较方式，确保类型匹配
    const found = currentLevel.find(item => {
      // 尝试字符串和数字两种比较方式
      return item.code == code || item.code === String(code) || item.code === Number(code);
    });

    if (found) {
      console.log(`找到匹配项:`, found);
      names.push(found.name);
      // 如果还有下一级，继续查找
      if (i < codes.length - 1 && found.children && found.children.length > 0) {
        currentLevel = found.children;
        console.log(`进入下一级，子级数据:`, currentLevel);
      }
    } else {
      console.warn(`未找到code为${code}的区域信息`);
      console.log(`当前级别所有可用的codes:`, currentLevel.map(item => `${item.code}(${typeof item.code})`));
      break;
    }
  }

  console.log("最终找到的names:", names);
  console.log("===================");
  return names;
};

// 格式化区域显示（用于详情显示）
const formatRegionDisplay = (areaCodes) => {
  console.log("=== formatRegionDisplay 被调用 ===");
  console.log("输入的areaCodes:", areaCodes);

  if (!areaCodes || !Array.isArray(areaCodes) || areaCodes.length === 0) {
    console.log("areaCodes为空，返回空字符串");
    return '';
  }

  const names = findRegionNamesByCodes(areaCodes);
  console.log("findRegionNamesByCodes返回的names:", names);

  const result = names.join('/');
  console.log("最终显示结果:", result);
  console.log("================================");

  return result;
};

// 处理区域选择变化（用于编辑时）
const handleRegionChange = (value, selectedOptions) => {
  console.log("=== 省市区选择变化 ===");
  console.log("原始值 value:", value);
  console.log("选中选项 selectedOptions:", selectedOptions);

  // 清空之前的数据
  detailRecord.value.regionNames = [];
  detailRecord.value.regionCodesNumber = [];

  // 确保 value 是数组
  if (Array.isArray(value) && value.length > 0) {
    // 保存选中的区域名称（省、市、区的名称）
    if (Array.isArray(selectedOptions) && selectedOptions.length > 0) {
      detailRecord.value.regionNames = selectedOptions.map(option => option.label);
      console.log("保存的区域名称:", detailRecord.value.regionNames);
    }

    // 保存所有层级的编码（省code、市code、区code）
    detailRecord.value.regionCodesNumber = value.map(code => parseInt(code));
    detailRecord.value.area = detailRecord.value.regionCodesNumber; // 同步到area字段

    console.log("保存的区域编码（所有层级）:", detailRecord.value.regionCodesNumber);
    console.log("选择层级数:", detailRecord.value.regionCodesNumber.length);

  } else {
    // 处理单个值的情况（非数组）
    if (value) {
      console.log("收到非数组值:", value);
      // 如果是单个值，转换为数组处理
      const codeValue = parseInt(value);
      if (!isNaN(codeValue)) {
        detailRecord.value.regionCodesNumber = [codeValue];
        detailRecord.value.area = detailRecord.value.regionCodesNumber;
        console.log("转换后的区域编码:", detailRecord.value.regionCodesNumber);
      }
    } else {
      // 清空值
      console.log("清空区域选择");
      detailRecord.value.area = [];
    }
  }

  console.log("最终保存的数据:");
  console.log("- regionNames:", detailRecord.value.regionNames);
  console.log("- regionCodesNumber:", detailRecord.value.regionCodesNumber);
  console.log("- area:", detailRecord.value.area);
  console.log("===================");
};

// 公共方法：获取服务商详情
const fetchProviderDetails = async (record) => {
  // 先将记录中的基本数据复制到detailRecord
  detailRecord.value = { ...record };

  try {
    // 调用服务商详情接口获取完整数据
    const res = await provider.provider.getBusinessInfo(record.id);
    if (res.code === 200 && res.data) {
      // 将返回的user_id赋值给detailRecord
      detailRecord.value.id = res.data.user_id;
      detailVisible.value = true;
      // 如果有business_info字段，将其展开到detailRecord中
      if (res.data.business_info) {
        // 将business_info对象中的所有字段合并到detailRecord
        Object.assign(detailRecord.value, res.data.business_info);
        detailRecord.value.id = res.data.user_id;
        // 处理特殊字段映射
        detailRecord.value.company_name = res.data.business_info.comname;

        detailRecord.value.address = res.data.business_info.comaddress;
        detailRecord.value.idCardFront =
          res.data.business_info.identity_card_portrait;
        detailRecord.value.idCardBack =
          res.data.business_info.identity_card_national_emblem;
        detailRecord.value.rating_certificate = res.data.business_info.rating;
        detailRecord.value.bank_name = res.data.business_info.bank;
        detailRecord.value.bank_branch = res.data.business_info.branch;
        detailRecord.value.bank_account = res.data.business_info.bankNumber;
        detailRecord.value.taxLevel = res.data.business_info.grade;
        detailRecord.value.businessScope = res.data.business_info.cardData.business;
        detailRecord.value.officeAddress = res.data.detail_address;
        detailRecord.value.detailAddress = res.data.business_info.cardData.address;
        detailRecord.value.creditCode = res.data.business_info.cardData.registerNumber;
        detailRecord.value.taxNumber = res.data.business_info.cardData.registerNumber;
        // 保存完整的联系人数据
        detailRecord.value.business_info = res.data.business_info;
        

        // 为了兼容性，保留第一个联系人的数据
        if (res.data.business_info.contacts && res.data.business_info.contacts.length > 0) {
          detailRecord.value.contactName = res.data.business_info.contacts[0].name;
          detailRecord.value.phone = res.data.business_info.contacts[0].phone;
          detailRecord.value.email = res.data.business_info.contacts[0].email;
          detailRecord.value.serviceArea = res.data.business_info.contacts[0].position;
        }
        detailRecord.value.legalPersonName = res.data.legal_person_name;

        // 处理经营者姓名和身份证号码
        if (res.data.business_info.legal_representative) {
          detailRecord.value.legalRepresentative = res.data.business_info.cardData.legalPerson ;
          console.log('保存经营者姓名:', res.data.business_info.legal_representative);
        }

        if (res.data.business_info.id_card_number) {
          detailRecord.value.idCardNumber = res.data.business_info.id_card_number;
          console.log('保存身份证号码:', res.data.business_info.id_card_number);
        }

        // 处理业务员信息
        if (res.data.salesman_id) {
          detailRecord.value.salesman_id = res.data.salesman_id;
          console.log('保存业务员ID字符串:', res.data.salesman_id);
        }

        if (res.data.salesman_info && Array.isArray(res.data.salesman_info)) {
          detailRecord.value.salesman_info = res.data.salesman_info;
          console.log('保存业务员信息数组:', res.data.salesman_info);
        }

        // 处理省市区数据
        if (res.data.business_info.area && Array.isArray(res.data.business_info.area) && res.data.business_info.area.length > 0) {
          detailRecord.value.area = res.data.business_info.area;
          // 将数字转为字符串，因为级联选择器需要字符串类型的值
          detailRecord.value.regionCodes = res.data.business_info.area.map(code => String(code));
          detailRecord.value.regionCodesNumber = res.data.business_info.area;
          console.log("设置区域编码（字符串）：", detailRecord.value.regionCodes);
          console.log("设置区域编码（数字）：", detailRecord.value.regionCodesNumber);
          console.log("设置area字段：", detailRecord.value.area);
        }

        // 处理文件上传相关数据
        // 营业执照
        if (res.data.business_info.businessLicense) {
          detailRecord.value.businessLicense = res.data.business_info.businessLicense;
          businessLicenseFiles.value = [{
            uid: '1',
            name: '营业执照',
            status: 'done',
            url: res.data.business_info.businessLicense
          }];
        } else {
          businessLicenseFiles.value = [];
        }

        // 身份证正面
        if (res.data.business_info.identity_card_portrait) {
          detailRecord.value.identity_card_portrait = res.data.business_info.identity_card_portrait;
          detailRecord.value.idCardFront = res.data.business_info.identity_card_portrait;
          idCardFrontFiles.value = [{
            uid: '2',
            name: '身份证正面',
            status: 'done',
            url: res.data.business_info.identity_card_portrait
          }];
        } else {
          idCardFrontFiles.value = [];
        }

        // 身份证反面
        if (res.data.business_info.identity_card_national_emblem || res.data.business_info.identity_card_back) {
          const backUrl = res.data.business_info.identity_card_national_emblem || res.data.business_info.identity_card_back;
          detailRecord.value.identity_card_national_emblem = backUrl;
          detailRecord.value.identity_card_back = backUrl;
          detailRecord.value.idCardBack = backUrl;
          idCardBackFiles.value = [{
            uid: '3',
            name: '身份证反面',
            status: 'done',
            url: backUrl
          }];
        } else {
          idCardBackFiles.value = [];
        }

        // 评级证明
        if (res.data.business_info.rating) {
          detailRecord.value.rating = res.data.business_info.rating;
          detailRecord.value.ratingCertificate = res.data.business_info.rating;
          ratingCertificateFiles.value = [{
            uid: '4',
            name: '评级证明',
            status: 'done',
            url: res.data.business_info.rating
          }];
        } else {
          ratingCertificateFiles.value = [];
        }

        // 开户证明
        if (res.data.business_info.license) {
          detailRecord.value.license = res.data.business_info.license;
          bankCertificateFiles.value = [{
            uid: '5',
            name: '开户证明',
            status: 'done',
            url: res.data.business_info.license
          }];
        } else {
          bankCertificateFiles.value = [];
        }
      }



      // 如果有平台费率数据，更新platforms数组
      if (res.data.platform_rate && Array.isArray(res.data.platform_rate)) {
        // 将接口返回的平台费率数据赋值给platforms
        platforms.value = res.data.platform_rate.map(item => ({
          id: item.id,
          name: item.name,
          rate: parseFloat(item.rate) * 100 // 转换为百分比显示
        }));
      } else {
        // 如果没有平台费率数据，初始化为空数组
        platforms.value = [];
      }

      // 如果有预格式化的入驻时间，使用它
      if (record._formattedCreatedAt) {
        detailRecord.value._formattedCreatedAt = record._formattedCreatedAt;
      }

      Message.success("获取服务商详情成功");
    } else {
      Message.error(res.message || "获取服务商详情失败");
    }
  } catch (error) {
    console.error("获取服务商详情失败:", error);
    Message.error("获取服务商详情失败，请重试");
  } finally {
    detailVisible.value = true;
    // 重置所有文件上传列表
    resetFileUploadLists();
  }
};

// 打开详情抽屉
const openDetails = async record => {
  // 设置为查看模式
  isEditing.value = false;

  // 确保省市区数据已加载
  if (!regionTreeData.value || regionTreeData.value.length === 0) {
    console.log("省市区数据未加载，先加载省市区数据");
    await fetchRegionTree();
  }

  // 调用公共方法获取详情
  await fetchProviderDetails(record);
};
const providerData = [];

// 打开编辑模式
const openEdit = async record => {
  // 设置为编辑模式
  isEditing.value = true;

  // 确保省市区数据已加载
  if (!regionTreeData.value || regionTreeData.value.length === 0) {
    console.log("省市区数据未加载，先加载省市区数据");
    await fetchRegionTree();
  }

  // 调用相同的详情接口获取数据
  await fetchProviderDetails(record);

  // 初始化文件上传列表
  initFileUploadLists();

  // 根据详情数据初始化文件列表
  if (detailRecord.value.businessLicense) {
    businessLicenseFiles.value = [
      {
        uid: "1",
        name: "营业执照",
        status: "done",
        url: detailRecord.value.businessLicense
      }
    ];
  }

  if (detailRecord.value.identity_card_portrait) {
    idCardFrontFiles.value = [
      {
        uid: "1",
        name: "身份证正面",
        status: "done",
        url: detailRecord.value.identity_card_portrait
      }
    ];
  }

  if (detailRecord.value.identity_card_back) {
    idCardBackFiles.value = [
      {
        uid: "1",
        name: "身份证背面",
        status: "done",
        url: detailRecord.value.identity_card_back
      }
    ];
  }

  if (detailRecord.value.ratingCertificate) {
    ratingCertificateFiles.value = [
      {
        uid: "1",
        name: "评级证书",
        status: "done",
        url: detailRecord.value.ratingCertificate
      }
    ];
  }

  if (detailRecord.value.license) {
    bankCertificateFiles.value = [
      {
        uid: "1",
        name: "开户证明",
        status: "done",
        url: detailRecord.value.license
      }
    ];
  }
};

const detailHandleTabChange = (key) => {
  detailActiveTab.value = key;
  if (key === "5") {
    fetchAgreementList();
  }
};

// 关闭详情抽屉
const closeDetail = () => {
  detailVisible.value = false;
  detailRecord.value = {};
  isEditing.value = false; // 重置编辑模式
  detailActiveTab.value = '1';
  // 重置所有文件上传列表
  resetFileUploadLists();
};

// 保存编辑
const saveEdit = async () => {
  try {
    // 验证平台费率是否合法
    const invalidRates = platforms.value.filter(
      p => p.rate < 0 || p.rate > 100
    );
    if (invalidRates.length > 0) {
      Message.error("费率必须在0-100之间");
      return;
    }
    detailRecord.value.cardData.registerNumber = detailRecord.value.creditCode;
    detailRecord.value.cardData.business = detailRecord.value.businessScope;
    detailRecord.value.cardData.address = detailRecord.value.officeAddress;
    // 构建请求数据
    const requestData = {
      user_id: detailRecord.value.id,
      business_info: {
        // 纳税评级证明等级
        grade: detailRecord.value.taxLevel,

        // 区域ID数组，包含所有选中层级的编码（省、市、区）
        area: detailRecord.value.regionCodesNumber && detailRecord.value.regionCodesNumber.length > 0
          ? detailRecord.value.regionCodesNumber
          : detailRecord.value.area || [],

        // 联系人信息
        contacts: [
          {
            position: detailRecord.value.serviceArea || "",
            name: detailRecord.value.contactName || "",
            email: detailRecord.value.email || "",
            phone: detailRecord.value.phone || ""
          }
        ],


        // 营业执照识别数据
        cardData: detailRecord.value.cardData || {},

        // 营业执照URL
        businessLicense: detailRecord.value.businessLicense || "",

        // 企业名称
        comname: detailRecord.value.companyName || "",

        // 身份证正面URL
        identity_card_portrait: detailRecord.value.identity_card_portrait || "",

        // 身份证反面URL
        identity_card_national_emblem: detailRecord.value.identity_card_back ||
          detailRecord.value.identity_card_national_emblem || "",

        // 办公地址
        comaddress: detailRecord.value.officeAddress || "",

        // 评级证明URL
        rating: detailRecord.value.ratingCertificate || detailRecord.value.rating || "",

        // 开户银行
        bank: detailRecord.value.bank_name || "",

        // 开户网点
        branch: detailRecord.value.bank_branch || "",

        // 对公银行账号
        bankNumber: detailRecord.value.bank_account || "",

        // 开户证明文件URL
        license: detailRecord.value.license || "",
        //法人姓名
        legal_representative:  detailRecord.value.legalPersonName || "",
        comaddress: detailRecord.value.detailAddress || "",

        // 身份证号码
        id_card_number: detailRecord.value.idCardNumber || "",

        // creditCode: detailRecord.value.creditCode || "",
        // registeredAddress: detailRecord.value.registeredAddress || "",
        // businessScope: detailRecord.value.businessScope || ""
      }
    };

    // 调用更新接口
    const res = await provider.provider.updateInfo(requestData);

    if (res.code === 200) {
      Message.success("保存成功");

      // 刷新列表
      crudRef.value.refresh();

      // 关闭抽屉
      closeDetail();
    } else {
      Message.error(res.message || "保存失败，请重试");
    }
  } catch (error) {
    console.error("保存服务商详情失败:", error);
    Message.error("保存失败，请重试");
  }
};

// 初始化文件上传列表
const initFileUploadLists = () => {
  // 初始化营业执照文件列表
  if (detailRecord.value.business_license) {
    businessLicenseFiles.value = [
      {
        uid: "1",
        name: "营业执照.jpg",
        url: detailRecord.value.business_license,
        status: "done"
      }
    ];
  } else {
    businessLicenseFiles.value = [];
  }

  // 初始化身份证正面文件列表
  if (detailRecord.value.id_card_front) {
    idCardFrontFiles.value = [
      {
        uid: "1",
        name: "身份证正面.jpg",
        url: detailRecord.value.id_card_front,
        status: "done"
      }
    ];
  } else {
    idCardFrontFiles.value = [];
  }

  // 初始化身份证反面文件列表
  if (detailRecord.value.id_card_back) {
    idCardBackFiles.value = [
      {
        uid: "1",
        name: "身份证反面.jpg",
        url: detailRecord.value.id_card_back,
        status: "done"
      }
    ];
  } else {
    idCardBackFiles.value = [];
  }

  // 初始化评级证明文件列表
  if (detailRecord.value.rating_certificate) {
    ratingCertificateFiles.value = [
      {
        uid: "1",
        name: "评级证明.jpg",
        url: detailRecord.value.rating_certificate,
        status: "done"
      }
    ];
  } else {
    ratingCertificateFiles.value = [];
  }

  // 初始化开户证明文件列表
  if (detailRecord.value.license) {
    bankCertificateFiles.value = [
      {
        uid: "1",
        name: "开户证明.jpg",
        url: detailRecord.value.license,
        status: "done"
      }
    ];
  } else {
    bankCertificateFiles.value = [];
  }

  // 初始化协议文件列表
  if (detailRecord.value.agreements) {
    detailRecord.value.agreements.forEach(agreement => {
      if (agreement.file_url) {
        agreement.fileList = [
          {
            uid: "1",
            name: "协议文件.pdf",
            url: agreement.file_url,
            status: "done"
          }
        ];
      } else {
        agreement.fileList = [];
      }
    });
  }
};

// 重置文件上传列表
const resetFileUploadLists = () => {
  businessLicenseFiles.value = [];
  idCardFrontFiles.value = [];
  idCardBackFiles.value = [];
  ratingCertificateFiles.value = [];
  bankCertificateFiles.value = [];
};

// 通用图片上传处理函数
const handleImageUpload = async (fileInfo, fileList, fieldName, listFieldName) => {
  try {
    // 防御性检查，确保参数正确
    if (!fileInfo || !fileList || !listFieldName) {
      console.error("上传图片参数错误:", {
        fileInfo,
        fileList,
        fieldName,
        listFieldName
      });
      Message.error("上传图片参数错误");
      return fileList;
    }

    // 获取文件对象
    const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);

    if (!file || !file.file) {
      console.error("未找到有效的文件对象");
      Message.error("未找到有效的文件对象");
      return fileList;
    }

    // 创建FormData对象
    const formData = new FormData();
    formData.append("file", file.file);

    // 显示上传中提示
    Message.loading("正在上传图片...");

    // 调用common.js中的uploadImage方法上传图片
    const response = await common.uploadImage(formData);

    if (response && response.code === 200 && response.data && response.data.fileUrl) {
      // 上传成功，获取文件URL
      const fileUrl = response.data.fileUrl;

      // 更新文件列表和URL
      const newFileList = [...fileList];
      if (newFileList.length > 0) {
        newFileList[newFileList.length - 1].status = "done";
        newFileList[newFileList.length - 1].url = fileUrl;
      }

      // 更新对应的字段值
      if (fieldName) {
        detailRecord.value[fieldName] = fileUrl;
      }

      Message.success("图片上传成功");
      return newFileList;
    } else {
      console.error("图片上传失败，响应:", response);
      Message.error("图片上传失败：" + (response?.message || "未知错误"));
      // 移除上传失败的文件
      return fileList.length > 0 ? fileList.slice(0, fileList.length - 1) : [];
    }
  } catch (error) {
    console.error("上传图片时发生错误:", error);
    Message.error("上传图片时发生错误：" + (error.message || "未知错误"));
    // 移除上传失败的文件
    return fileList.length > 0 ? fileList.slice(0, fileList.length - 1) : [];
  }
};

// 处理营业执照文件上传变化
const handleBusinessLicenseChange = async (fileInfo) => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleBusinessLicenseChange: fileInfo为空");
    return;
  }

  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  // 如果有文件且状态为上传中
  if ((fileList.length > 0 && file && file.status === "uploading") || file.status === "init") {
    // 上传图片
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "businessLicense",
      "businessLicenseList"
    );

    businessLicenseFiles.value = updatedFileList;

    // 如果上传成功，立即触发表单验证，清除必填提示
    if (updatedFileList.length > 0 && updatedFileList[0].url) {
      detailRecord.value.businessLicense = updatedFileList[0].url;

      // 立即触发表单验证，清除必填提示
      // 使用setTimeout确保在下一个微任务周期执行，避免触发全局验证
      setTimeout(() => {
        console.log("营业执照上传成功，已更新detailRecord.businessLicense");
      }, 0);
    }
  } else if (fileList.length === 0) {
    // 如果没有文件，清空表单数据
    detailRecord.value.businessLicense = "";
    businessLicenseFiles.value = [];
  }
};

// 处理身份证正面文件上传变化
const handleIdCardFrontChange = async (fileInfo) => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleIdCardFrontChange: fileInfo为空");
    return;
  }

  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  // 如果有文件且状态为上传中
  if ((fileList.length > 0 && file && file.status === "uploading") || file.status === "init") {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "identity_card_portrait",
      "idCardFrontList"
    );

    idCardFrontFiles.value = updatedFileList;

    // 如果上传成功，更新detailRecord
    if (updatedFileList.length > 0 && updatedFileList[0].url) {
      detailRecord.value.identity_card_portrait = updatedFileList[0].url;
      detailRecord.value.idCardFront = updatedFileList[0].url; // 兼容字段
    }
  } else if (fileList.length === 0) {
    idCardFrontFiles.value = [];
    detailRecord.value.identity_card_portrait = "";
    detailRecord.value.idCardFront = "";
  }
};

// 处理身份证反面文件上传变化
const handleIdCardBackChange = async (fileInfo) => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleIdCardBackChange: fileInfo为空");
    return;
  }

  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);
  console.log(fileInfo, 'fileInfo');

  // 如果有文件且状态为上传中
  if ((fileList.length > 0 && file && file.status === "uploading") ||
    file.status === "init") {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "identity_card_national_emblem",
      "idCardBackList"
    );

    idCardBackFiles.value = updatedFileList;

    // 如果上传成功，更新detailRecord
    if (updatedFileList.length > 0 && updatedFileList[0].url) {
      detailRecord.value.identity_card_national_emblem = updatedFileList[0].url;
      detailRecord.value.identity_card_back = updatedFileList[0].url; // 兼容字段
      detailRecord.value.idCardBack = updatedFileList[0].url; // 兼容字段
    }
  } else if (fileList.length === 0) {
    idCardBackFiles.value = [];
    detailRecord.value.identity_card_national_emblem = "";
    detailRecord.value.identity_card_back = "";
    detailRecord.value.idCardBack = "";
  }
};

// 处理评级证明文件上传变化
const handleRatingCertificateChange = async (fileInfo) => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleRatingCertificateChange: fileInfo为空");
    return;
  }

  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  // 如果有文件且状态为上传中
  if ((fileList.length > 0 && file && file.status === "uploading") || file.status === "init") {
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "rating",
      "ratingCertificateList"
    );

    ratingCertificateFiles.value = updatedFileList;

    // 如果上传成功，更新detailRecord
    if (updatedFileList.length > 0 && updatedFileList[0].url) {
      detailRecord.value.rating = updatedFileList[0].url;
      detailRecord.value.ratingCertificate = updatedFileList[0].url; // 兼容字段
    }
  } else if (fileList.length === 0) {
    ratingCertificateFiles.value = [];
    detailRecord.value.rating = "";
    detailRecord.value.ratingCertificate = "";
  }
};

// 处理开户证明文件上传变化
const handleBankCertificateChange = async (fileInfo) => {
  // 防御性检查，确保fileInfo存在
  if (!fileInfo) {
    console.warn("handleBankCertificateChange: fileInfo为空");
    return;
  }

  const fileList = Array.isArray(fileInfo) ? fileInfo : fileInfo.fileList || [];
  const file = fileInfo.file || (fileList.length > 0 ? fileList[fileList.length - 1] : null);

  // 上传中或初始化状态
  if ((fileList.length > 0 && file && file.status === "uploading") || file.status === "init") {
    // 上传图片
    const updatedFileList = await handleImageUpload(
      fileInfo,
      fileList,
      "license",
      "bankCertificateList"
    );

    bankCertificateFiles.value = updatedFileList;

    // 如果上传成功，更新detailRecord
    if (updatedFileList.length > 0 && updatedFileList[0].url) {
      detailRecord.value.license = updatedFileList[0].url;

      setTimeout(() => {
        console.log("开户证明上传成功，已更新detailRecord.license");
      }, 0);
    }
  } else if (fileList.length === 0) {
    bankCertificateFiles.value = [];
    detailRecord.value.license = "";
  }
};

// 处理审核状态变更
const handleAuditStatusChange = value => {
  auditType.value = value;
  // 如果切换到通过，清空驳回原因
  if (value === 1) {
    auditForm.reason = "";
  }
};

// 提交审核
const submitAudit = async () => {
  // 验证驳回原因
  if (auditForm.status === 3 && !auditForm.reason) {
    Message.error("请输入驳回原因");
    return;
  }

  auditLoading.value = true;

  try {
    // 构建请求数据
    const requestData = {
      id: auditForm.id,
      status: auditForm.status,
      remark: auditForm.reason || ""
    };

    // 如果是审核通过，添加业务员ID
    if (
      auditForm.status === 1 &&
      auditForm.salesmanIds &&
      auditForm.salesmanIds.length > 0
    ) {
      requestData.salesman_id = auditForm.salesmanIds.join(",");
    }

    // 调用审核接口
    const res = await provider.provider.audit(requestData);

    if (res.code === 200) {
      // 更新本地数据
      if (currentRecord.value) {
        currentRecord.value.status = auditForm.status;
      }

      Message.success(auditForm.status === 1 ? "审核通过成功" : "驳回成功");
      auditVisible.value = false;

      // 刷新列表
      crudRef.value.refresh();
      getProviderList();
      closeDetail();

      // 如果当前正在查看详情，也更新详情数据
      if (detailVisible.value && detailRecord.id === auditForm.id) {
        detailRecord.status = auditForm.status;
      }
    } else {
      Message.error(res.message || "审核操作失败");
    }
  } catch (error) {
    console.error("审核操作失败:", error);
    Message.error("操作失败，请重试");
  } finally {
    auditLoading.value = false;
  }
};

// 处理审核操作
const handleAudit = record => {
  currentRecord.value = record;
  auditForm.id = record.id;
  auditForm.status = 1; // 默认选中通过（正常状态）
  auditForm.reason = "";
  auditForm.salesmanIds = record.salesmanIds || [];
  auditVisible.value = true;
};

// 取消审核
const cancelAudit = () => {
  auditVisible.value = false;
  auditForm.reason = "";
  currentRecord.value = null;
};

// 处理启用/停用状态变更
const handleChangeStatus = record => {
  const newStatus = record.status === 0 ? 1 : 0; // 如果当前是禁用状态，则启用；如果是正常状态，则禁用

  Modal.confirm({
    title: `确认${newStatus === 0 ? "禁用" : "启用"}该服务商吗？`,
    content: `${newStatus === 0
      ? "禁用后该服务商将无法继续提供服务"
      : "启用后该服务商将恢复正常服务"
      }`,
    onOk: async () => {
      try {
        // 调用真实API进行状态变更
        const res = await provider.provider.changeStatus({
          id: record.id,
          status: newStatus
        });

        if (res.code === 200) {
          // 更新本地数据
          record.status = newStatus;

          Message.success(`${newStatus === 1 ? "启用" : "禁用"}成功`);

          // 刷新列表
          crudRef.value.refresh();
        } else {
          Message.error(res.message || "状态变更失败");
        }
      } catch (error) {
        console.error("状态变更失败:", error);
        Message.error("操作失败，请重试");
      }
    }
  });
};

// 下载协议
const downloadAgreement = (record) => {
  if (!record || !record.fileUrl) {
    Message.error("协议文件不存在");
    return;
  }

  window.open(record.fileUrl, "_blank");
};

// 显示上传框架协议对话框
const showUploadAgreementDialog = () => {
  uploadAgreementDialogVisible.value = true;
};

// 处理协议上传成功
const handleAgreementUploadSuccess = (data) => {
  // 刷新协议列表
  fetchAgreementList();
  Message.success("协议上传成功");
};

// 处理协议分页变化
const handleAgreementPageChange = (page) => {
  agreementPagination.page = page;
  fetchAgreementList();
};

// 处理协议每页条数变化
const handleAgreementPageSizeChange = (pageSize) => {
  agreementPagination.pageSize = pageSize;
  agreementPagination.page = 1;
  fetchAgreementList();
};

// 获取框架协议列表
async function fetchAgreementList() {
  console.log(detailRecord.value.id);

  try {
    if (!detailRecord.value.id) {
      console.warn("未找到服务商ID，无法获取框架协议列表");
      return;
    }

    // 构建请求参数，使用动态分页参数
    const params = {
      page: agreementPagination.page,
      pageSize: agreementPagination.pageSize,
      user_id: detailRecord.value.id
    };

    // 调用接口获取列表数据
    const result = await providerSystemApi.agreement.getList(params);

    if (result && result.code === 200) {
      // 更新列表数据
      agreementData.value = result.data?.items || [];
      // 更新总数
      agreementTotal.value = result.data?.pageInfo?.total || 0;
    } else {
      console.warn("获取框架协议列表失败:", result?.message);
    }
  } catch (error) {
    console.error("获取框架协议列表失败:", error);
  }
};



// 删除协议
const removeAgreement = index => {
  detailRecord.value.agreements.splice(index, 1);
};

// 处理协议文件上传变化
const handleAgreementFileChange = (fileList, index) => {
  if (!detailRecord.value.agreements[index]) return;

  detailRecord.value.agreements[index].fileList = fileList;
  if (fileList.length > 0 && fileList[0].status === "done") {
    // 模拟上传成功后的文件URL
    detailRecord.value.agreements[index].file_url =
      fileList[0].url || "/assets/documents/placeholder.pdf";
  }
};

// 格式化金额
const formatAmount = amount => {
  if (!amount) return "0.00";
  return Number(amount).toFixed(2);
};

// 格式化日期
const formatDate = date => {
  if (!date) return "-";

  // 处理数字类型的时间戳
  let timestamp = date;
  if (typeof date === "string") {
    // 尝试将字符串转换为数字
    const parsedNumber = Number(date);
    if (!isNaN(parsedNumber)) {
      timestamp = parsedNumber;
    }
  }

  // 判断是否为秒级时间戳（如果小于20亿，则为秒级时间戳）
  if (typeof timestamp === "number" && timestamp < 20000000000) {
    // 将秒级时间戳转换为毫秒级
    timestamp = timestamp * 1000;
  }

  // 创建日期对象
  const dateObj = new Date(timestamp);

  // 如果日期无效，返回原始字符串
  if (isNaN(dateObj.getTime())) return date;

  // 获取年月日
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");

  // 获取时分秒
  const hours = String(dateObj.getHours()).padStart(2, "0");
  const minutes = String(dateObj.getMinutes()).padStart(2, "0");
  const seconds = String(dateObj.getSeconds()).padStart(2, "0");

  // 返回格式化后的日期字符串：年-月-日 时:分:秒
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 银行账号脱敏显示
const maskBankAccount = account => {
  // 先将account转换为字符串类型
  const accountStr = String(account);
  if (!accountStr) return "-";
  if (accountStr.length <= 8) return accountStr;

  const prefix = accountStr.substring(0, 4);
  const suffix = accountStr.substring(accountStr.length - 4);
  const masked = "*".repeat(accountStr.length - 8);

  return `${prefix}${masked}${suffix}`;
};

// 调用真实API获取服务商列表
const getProviderList = async params => {
  console.log("调用getProviderList，参数：", params);

  // 构建请求参数
  const requestParams = {
    page: params?.page || 1,
    pageSize: params?.pageSize || params?.limit || 10
  };

  console.log("分页参数处理:", {
    原始page: params?.page,
    原始pageSize: params?.pageSize,
    原始limit: params?.limit,
    最终page: requestParams.page,
    最终pageSize: requestParams.pageSize
  });

  // 如果传入了status参数，直接使用
  if (params?.status !== undefined && params?.status !== "") {
    requestParams.status = Number(params.status);
    console.log("使用传入的status参数：", requestParams.status);
  }
  // 如果没有传入status参数，但有标签页状态，使用标签页状态
  else if (activeTab.value !== "") {
    requestParams.status = Number(activeTab.value);
    console.log("使用标签页状态：", requestParams.status);
  }

  // 处理搜索参数
  if (params) {
    // ID搜索
    if (params.id) {
      requestParams.id = params.id;
    }

    // 账号搜索
    if (params.username) {
      requestParams.username = params.username;
    }

    // 企业名称搜索
    if (params.companyName) {
      requestParams.companyName = params.companyName;
    }

    // 法人姓名搜索
    if (params.legalPersonName) {
      requestParams.legalPersonName = params.legalPersonName;
    }

    // 手机号搜索
    if (params.phone) {
      requestParams.phone = params.phone;
    }

    // 详细地址搜索
    if (params.detailAddress) {
      requestParams.detailAddress = params.detailAddress;
    }

    // 状态搜索（优先使用搜索框的状态值）
    if (params.status !== undefined && params.status !== "") {
      requestParams.status = Number(params.status);
    }
    // 如果没有使用搜索框的状态值，但是标签页有选择状态，则使用标签页的状态值
    else if (activeTab.value !== "") {
      requestParams.status = Number(activeTab.value);
    }

    // 处理时间范围筛选
    if (params.createdAt && params.createdAt.length === 2) {
      // 将日期转换为时间戳格式（秒级）
      const startDate = new Date(params.createdAt[0]);
      const endDate = new Date(params.createdAt[1]);
      // 设置开始时间为当天凌晨
      startDate.setHours(0, 0, 0, 0);
      // 设置结束时间为当天最后一秒
      endDate.setHours(23, 59, 59, 999);

      // 转换为秒级时间戳
      requestParams.startTime = Math.floor(startDate.getTime() / 1000);
      requestParams.endTime = Math.floor(endDate.getTime() / 1000);

      console.log('时间范围转换:', {
        原始开始时间: params.createdAt[0],
        原始结束时间: params.createdAt[1],
        开始时间戳: requestParams.startTime,
        结束时间戳: requestParams.endTime,
        开始时间验证: new Date(requestParams.startTime * 1000).toLocaleString(),
        结束时间验证: new Date(requestParams.endTime * 1000).toLocaleString()
      });
    }
  }

  try {
    // 调用真实API
    const res = await provider.provider.getList(requestParams);

    // 检查API响应
    if (res.code === 200 && res.data) {
      // 调试信息：查看返回的数据格式
      console.log("服务商列表原始数据：", res.data);
      if (res.data.items && res.data.items.length > 0) {
        console.log("第一条数据示例：", res.data.items[0]);
        console.log("入驻时间字段类型：", typeof res.data.items[0].createdAt);
        console.log("入驻时间字段值：", res.data.items[0].createdAt);
      }

      // 处理数据，确保入驻时间字段格式正确
      const processedItems = (res.data.items || []).map(item => {
        // 如果入驻时间字段存在，尝试转换为正确的时间格式
        if (item.createdAt) {
          // 在返回前先尝试格式化一次，确保列表显示正确
          item._formattedCreatedAt = formatDate(item.createdAt);
        }
        return item;
      });

      return {
        success: true,
        message: res.message || "获取服务商列表成功",
        code: res.code,
        data: {
          items: processedItems,
          pageInfo: {
            total: res.data.pageInfo?.total || 0,
            currentPage: res.data.pageInfo?.currentPage || 1,
            totalPage: res.data.pageInfo?.totalPage || 1
          }
        }
      };
    } else {
      return {
        success: false,
        message: res.message || "获取服务商列表失败",
        code: res.code || 500,
        data: {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: 1,
            totalPage: 1
          }
        }
      };
    }
  } catch (error) {
    console.error("获取服务商列表失败:", error);
    return {
      success: false,
      message: "获取服务商列表失败",
      code: 500,
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          totalPage: 1
        }
      }
    };
  }
};

// CRUD配置
const crud = reactive({
  // API配置
  api: getProviderList, // 使用模拟数据的API函数
  showIndex: false,
  // 移除 pageLayout: "fixed" 让分页组件显示在表格下方
  operationColumn: false, // 禁用自动添加的操作列，因为已在columns中手动定义了
  searchLabelWidth: "100px",
  // 禁用行选择
  rowSelection: false,
  // 显示搜索区域
  showSearch: true,
  // 显示搜索区域后的内容
  showSearchAfter: true,
  // 分页配置
  page: true,
  pageSize: 10,
  pageSizes: [10, 20, 30, 50, 100],
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  // 添加前处理参数
  beforeAdd: params => {
    return params;
  },
  // 编辑前处理参数
  beforeEdit: params => {
    return params;
  },

});

// 表格列配置
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    search: true,
    width: 120,
    searchSpan: 6 // 控制搜索框宽度
  },
  {
    title: "账号",
    dataIndex: "username",
    search: true,
    width: 180,
    searchSpan: 6
  },
  {
    title: "企业名称",
    dataIndex: "companyName",
    search: true,
    width: 180,
    searchSpan: 6
  },

  {
    title: "法人姓名",
    dataIndex: "legalPersonName",
    search: true,
    width: 120,
    searchSpan: 6
  },

  {
    title: "联系电话",
    dataIndex: "phone",
    search: true,
    width: 130
  },
  {
    title: "详细地址",
    dataIndex: "detailAddress",
    search: true,
    width: 180,
    searchSpan: 6
  },
  {
    title: "订单总量",
    dataIndex: "orderCount",
    width: 100,
    align: "center"
  },
  {
    title: "订单总金额",
    dataIndex: "orderAmount",
    width: 120,
    align: "center",
    render: ({ record }) => formatAmount(record.orderAmount)
  },
  {
    title: "业务员",
    dataIndex: "salesmanName",
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: "状态",
    dataIndex: "status",
    search: false,
    width: 100,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "禁用", value: 0 },
        { label: "已审核", value: 1 },
        { label: "待审核", value: 2 },
        { label: "已驳回", value: 3 }
      ]
    },
    render: ({ record }) => {
      return {
        props: {
          type: getStatusColor(record.status)
        },
        children: getStatusText(record.status)
      };
    }
  },
  {
    title: "入驻时间",
    dataIndex: "createdAt",
    search: true,
    width: 200,
    formType: "range",
    searchSpan: 8,
    render: ({ record }) => {
      // 优先使用预格式化的时间字段，如果没有再尝试实时格式化
      if (record._formattedCreatedAt) {
        return record._formattedCreatedAt;
      }
      return formatDate(record.createdAt);
    }
  },
  {
    title: "操作",
    dataIndex: "operationBeforeExtend",
    width: 200,
    align: "center",
    fixed: "right"
    // 添加插槽名称
    // slotName: 'column-operation'
  }
]);

// 获取业务员列表
const loadSalesmanList = async () => {
  try {
    const response = await teamApi.getUserList();
    if (response.code === 200) {
      salesmanOptions.value = response.data || [];
    } else {
      Message.error("获取业务员列表失败");
    }
  } catch (error) {
    console.error("获取业务员列表失败:", error);
    Message.error("获取业务员列表失败");
  }
};

// 处理修改业务员
const handleEditSalesman = record => {
  console.log('=== 修改业务员 ===');
  console.log('当前记录:', record);

  salesmanForm.id = record.id;

  // 从 salesman_id 字段中提取业务员ID（逗号分隔的字符串）
  if (record.salesman_id && typeof record.salesman_id === 'string') {
    salesmanForm.salesmanIds = record.salesman_id.split(',').filter(id => id.trim() !== '');
    console.log('已选业务员ID:', salesmanForm.salesmanIds);
    console.log('原始salesman_id:', record.salesman_id);
  } else {
    salesmanForm.salesmanIds = [];
    console.log('没有找到业务员信息，使用空数组');
  }

  editSalesmanVisible.value = true;
};

// 提交修改业务员
const submitEditSalesman = async () => {
  editSalesmanLoading.value = true;

  try {
    const requestData = {
      id: salesmanForm.id,
      salesman_id: salesmanForm.salesmanIds.join(",")
    };

    const res = await provider.provider.updateSalesman(requestData);

    if (res.code === 200) {
      Message.success("修改业务员成功");
      editSalesmanVisible.value = false;

      // 刷新列表
      crudRef.value.refresh();
      getProviderList();

      // 如果当前正在查看详情，也更新详情数据
      if (detailVisible.value && detailRecord.value.id === salesmanForm.id) {
        // 更新业务员ID字符串
        detailRecord.value.salesman_id = salesmanForm.salesmanIds.join(',');

        // 更新业务员信息数组
        const selectedSalesmen = salesmanOptions.value.filter(salesman =>
          salesmanForm.salesmanIds.includes(salesman.id)
        );

        detailRecord.value.salesman_info = selectedSalesmen.map(salesman => ({
          id: salesman.id,
          name: salesman.realname || salesman.username
        }));

        console.log('更新详情页面业务员信息:');
        console.log('- salesman_id:', detailRecord.value.salesman_id);
        console.log('- salesman_info:', detailRecord.value.salesman_info);
      }
    } else {
      Message.error(res.message || "修改业务员失败");
    }
  } catch (error) {
    console.error("修改业务员失败:", error);
    Message.error(error.message || "修改业务员失败");
  } finally {
    editSalesmanLoading.value = false;
  }
};

// 取消修改业务员
const cancelEditSalesman = () => {
  editSalesmanVisible.value = false;
  salesmanForm.id = "";
  salesmanForm.salesmanIds = [];
};

// 获取业务员名称
const getSalesmanNames = salesmanIds => {
  if (!salesmanIds || !salesmanIds.length) return "";

  return salesmanIds
    .map(id => {
      const salesman = salesmanOptions.value.find(item => item.id === id);
      return salesman ? salesman.realname || salesman.username : "";
    })
    .filter(Boolean)
    .join(", ");
};

// 页面初始化
onMounted(async () => {
  // 初始化数据，实际项目中可能需要调用API获取数据
  // 获取服务商列表
  getProviderList();
  // 获取业务员列表
  loadSalesmanList();
  // 加载省市区数据
  await fetchRegionTree();
});

// 修改密码相关状态
const changePasswordVisible = ref(false);

// 打开修改密码模态框
const openChangePassword = () => {
  changePasswordVisible.value = true;
};

// 处理密码修改成功
const handlePasswordChangeSuccess = () => {
  Message.success("密码修改成功");
};

// 保存平台费率
const savePlatformRates = async () => {
  try {
    if (!detailRecord.value.id) {
      Message.error("未找到服务商ID，无法保存平台费率");
      return;
    }

    // 准备请求参数
    const requestData = {
      provider_id: detailRecord.value.id,
      platform_rate: platforms.value.map(item => ({
        id: item.id,
        name: item.name,
        rate: (item.rate / 100).toFixed(4) // 将百分比转换为小数
      }))
    };

    // 调用接口保存平台费率
    const result = await provider.provider.updatePlatformRate(requestData);

    if (result && result.code === 200) {
      Message.success("平台费率保存成功");

      // 更新本地数据
      if (result.data && result.data.platform_rate) {
        platforms.value = result.data.platform_rate.map(item => ({
          id: item.id,
          name: item.name,
          rate: parseFloat(item.rate) * 100 // 转换为百分比显示
        }));
      }
      
    } else {
      Message.error(result?.message || "平台费率保存失败");
    }
  } catch (error) {
    console.error("保存平台费率失败:", error);
    Message.error("保存平台费率失败，请重试");
  }
};
</script>

<script>
export default { name: "master-cooperative-provider" };
</script>

<style scoped lang="less">
.provider-detail-container {
  padding: 0 16px;
}

.info-section,
.qualification-section,
.agreement-section,
.business-data-section {
  margin-bottom: 24px;
}

.qualification-section {
  display: flex;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 120px;
  flex-shrink: 0;
}

.image-preview {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 16px;
  display: inline-block;
}

.no-image {
  width: 300px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}

/* 覆盖表格行高亮样式 */
:deep(.arco-table-tr) {
  cursor: default !important;
}

/* 平台费率相关样式 */
.platform-rates-section {
  padding: 16px;
}

.platform-rate-card {
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

.platform-rate-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  border-color: var(--color-primary-light-3);
}

.platform-rate-card :deep(.arco-card-body) {
  padding: 16px;
}

.platform-rate-card :deep(.arco-input-number) {
  width: 120px;
}

.platform-rate-card :deep(.arco-input-number-mode-button) {
  border-radius: 4px;
}

/* 图片上传相关样式 */
.upload-image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.upload-skin {
  background-color: #f2f3f5;
  border: 1px dashed #c9cdd4;
  width: 130px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-skin:hover {
  border-color: #165dff;
  background-color: #f7f8fa;
}

.upload-skin .icon {
  font-size: 24px;
  color: #86909c;
  margin-bottom: 8px;
}

.upload-skin .title {
  font-size: 14px;
  color: #86909c;
}

.upload-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.image-list {
  position: relative;
  width: 130px;
  height: 130px;
  background-color: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 2px;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f53f3f;
  border: none;
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

:deep(.arco-table-tr:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr-selected td) {
  background-color: transparent !important;
}

/* 联系人相关样式 */
.contact-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #fafbfc;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  border-bottom: 2px solid #165dff;
  padding-bottom: 8px;
}

.no-contact-info {
  text-align: center;
  padding: 40px 0;
}

:deep(.arco-table-tr:focus-within) {
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

:deep(.arco-table-tr.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-td) {
  border-right: none !important;
}
</style>