<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 关联分类 -->
      <template #categoryId="{ record }">
        {{ record.categoryName }}
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
      <!-- 操作列 -->
      <template #operationAfterExtend="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleParams(record)">
            <template #icon><icon-list /></template>
            参数项目
          </a-button>
        </div>
      </template>
      <!-- 自定义关联分类选择器 -->
      <template #form-categoryId>
        <a-cascader
          v-model="selectedCategoryId"
          :options="options"
          placeholder="请选择分类"
          allow-clear
        />
      </template>
      <!-- 自定义关联分类搜索 -->
      <template #search-categoryId="{searchForm}">
        <a-cascader
          v-model="searchForm.categoryId"
          :options="options"
          placeholder="请选择分类"
          allow-clear
        />
      </template>
    </ma-crud>

    <!-- 参数项目组件 -->
    <param-item-list
      v-model:visible="paramsVisible"
      :record="currentRecord"
      @save="handleParamsSave"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from "~/components/base/ma-form/index.vue";
import ParamItemEdit from "./components/ParamItemEdit.vue";
import ParamItemList from "./components/ParamItemList.vue";
import goodsApi from "@/api/master/goods";
const paramTemplateApi = goodsApi.attrTemplate;
definePageMeta({
  name: "master-paramTemplate",
  path: "/master/goods/paramTemplate",
});


const crudRef = ref();
const paramsVisible = ref(false);
const currentRecord = ref({});
const selectedCategoryId = ref(null); // 选中的分类ID

const options = ref([]);
// 打开参数项目弹窗
const handleParams = (record) => {
  currentRecord.value = record;
  paramsVisible.value = true;
};

// 处理参数项目保存
const handleParamsSave = (data) => {
  crudRef.value.refresh();
};


const crud = reactive({
  showTools:false,
  api: paramTemplateApi.getList,
  showIndex: false,
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 250,
  add: { 
    show: true,
    api: paramTemplateApi.create,
  },
  edit: { 
    show: true,
    api: paramTemplateApi.update,
  },
  delete: {
    show: true,
    api: paramTemplateApi.delete,
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为毫秒级时间戳
    if(params.createdAt && params.createdAt.length > 0){
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime() ;
      
      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endTime = endDate.getTime() ;
      
      delete params.createdAt
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
  // 新增前的钩子，用于清空选中的菜单
  beforeOpenAdd: () => {
    selectedCategoryId.value = '';
    return true; // 必须返回true才会打开新增弹窗
  },
 
  // 编辑前的钩子，用于设置选中的菜单和部门
  beforeOpenEdit: (record) => {
    console.log('编辑前原始参数:', record);    
    selectedCategoryId.value = record.categoryId;
    return true; // 必须返回true才会打开编辑弹窗
  },
  // 添加前处理参数
  beforeAdd: (params) => {
   if(selectedCategoryId.value) params.categoryId = selectedCategoryId.value
    return params;
  }, 
   // 编辑前处理参数
   beforeEdit: (params) => {
    params.categoryId = selectedCategoryId.value
    return params;
  },  
});

const columns = reactive([
  { title: "ID", dataIndex: "id", width: 80 },
  {
    title: "模板名称",
    dataIndex: "name",
    search: true,
    width: 150,
    commonRules: [{ required: true, message: "模板名称必填" }],
  },
  {
    title: "关联分类",
    dataIndex: "categoryId",
    search: true,
    width: 120,
    formType: "cascader",
  },
  {
    title: "参数项数量",
    dataIndex: "paramCount",
    width: 120,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "排序",
    dataIndex: "sort",
    formType: 'inputNumber',
    sortable: true,
    min: 0,
    precision: 0,
    width: 100,
     commonRules: [
      { required: false, message: '排序必填' },
      {
        validator: (value, callback) => {
          if (value !== undefined && value !== null && value !== '') {
            if (!Number.isInteger(Number(value)) || Number(value) < 0) {
              callback('排序必须为大于等于0的整数');
            } else {
              callback();
            }
          } else {
            callback();
          }
        }
      }
    ],
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    formType: "range",
    search: true,
    width: 260,
    addDisplay: false,
    editDisplay: false,
  },
]);

// 参数项表格配置
const paramsCrud = reactive({
  showIndex: true,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: { show: true },
  edit: { show: false },
  delete: { show: false },
  pagination: false, // 禁用分页
});

// 参数项表格列定义
const paramsColumns = reactive([
  {
    title: "参数名称",
    dataIndex: "name",
    width: 120,
    search: true,
  },
  {
    title: "参数类型",
    dataIndex: "type",
    width: 100,
    dict: {
      data: [
        { label: "文本", value: "text" },
        { label: "数字", value: "number" },
        { label: "单选", value: "radio" },
        { label: "多选", value: "checkbox" },
        { label: "下拉选择", value: "select" },
      ],
    },
  },
  {
    title: "可选值/单位",
    dataIndex: "options",
    width: 150,
  },
  {
    title: "必填",
    dataIndex: "required",
    width: 80,
    slotName: "required",
  },
  {
    title: "可筛选",
    dataIndex: "filterable",
    width: 80,
    slotName: "filterable",
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 80,
    slotName: "status",
  },
]);


const getCategoryList = async () => {
  const transform = (item) => {
    const result = {
      value: item.id,
      label: item.name,
    };
    
    if (item.children && item.children.length > 0) {
      result.children = item.children.map(transform);
    }
    
    return result;
  };
  const res = await goodsApi.category.getList();
  options.value = res.data.map(transform);
  console.log(options.value, 'options');
};

onMounted(() => {
  getCategoryList();
});
</script>

<script>
export default { name: "master-paramTemplate" };
</script>

<style scoped>
.params-container {
  padding: 0 16px;
}
</style>
