<template>
  <a-modal
    :visible="visible"
    title="选择商品"
    @cancel="handleCancel"
    :footer="false"
    :mask-closable="false"
    :width="'80%'"
  >
    <div class="product-selector">
      <!-- 搜索和分类过滤区域 -->
      <div class="search-filter-container mb-4">
        <div class="grid grid-cols-4 gap-4">
          <div class="col-span-1">
            <!-- 分类树 -->
            <div class="category-tree-container border rounded p-2 h-96 overflow-auto">
              <div class="text-base font-medium mb-2">商品分类</div>
              <a-tree
                :data="categoryTree"
                :default-expanded-keys="['all']"
                @select="handleCategorySelect"
              />
            </div>
          </div>
          <div class="col-span-3">
            <!-- 搜索框 -->
            <div class="search-container mb-4 flex">
              <a-input-search
                v-model="searchKeyword"
                placeholder="请输入商品名称、编码或规格搜索"
                search-button
                @search="handleSearch"
                class="flex-1 mr-2"
              />
              <a-button @click="resetSearch">重置</a-button>
            </div>

            <!-- 商品列表 -->
            <div class="product-list-container">
              <a-spin :loading="loading">
                <a-empty v-if="productList.length === 0 && !loading" description="暂无商品数据" />
                <div v-else class="grid grid-cols-3 gap-4">
                  <div
                    v-for="product in productList"
                    :key="product.id"
                    class="product-item border rounded p-3 cursor-pointer hover:border-blue-500"
                    @click="handleProductClick(product)"
                  >
                    <div class="flex items-center">
                      <a-image
                        :src="product.mainImage || '/assets/images/placeholder.png'"
                        :width="60"
                        :height="60"
                        class="mr-3"
                        fit="cover"
                      />
                      <div class="flex-1">
                        <div class="product-name text-sm font-medium mb-1 line-clamp-2">{{ product.name }}</div>
                        <div class="product-category text-xs text-gray-500 mb-1">分类: {{ product.categoryName }}</div>
                        <div class="product-brand text-xs text-gray-500 mb-1" v-if="product.brandName">品牌: {{ product.brandName }}</div>
                        <div class="product-price text-sm text-red-500">{{ product.price }}</div>
                      </div>
                    </div>
                    <div v-if="product.skuType === 2" class="text-xs text-blue-500 mt-2 text-right">
                      多规格商品，点击选择
                    </div>
                  </div>
                </div>

                <!-- 分页 -->
                <div class="pagination-container mt-4 flex justify-end">
                  <a-pagination
                    v-model:current="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    show-total
                    show-jumper
                    @change="handlePageChange"
                  />
                </div>
              </a-spin>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- SKU选择弹窗 -->
    <a-modal
      v-model:visible="skuModalVisible"
      title="选择规格"
      @cancel="skuModalVisible = false"
      :footer="false"
      :mask-closable="false"
      :width="'1000px'"
    >
      <div class="sku-selector">
        <div class="product-info mb-4 flex items-center">
          <a-image
            :src="currentProduct?.mainImage || '/assets/images/placeholder.png'"
            :width="60"
            :height="60"
            class="mr-3"
            fit="cover"
          />
          <div>
            <div class="product-name font-medium">{{ currentProduct?.name }}</div>
            <div class="product-category text-sm text-gray-500">分类: {{ currentProduct?.categoryName }}</div>
          </div>
        </div>

        <div class="sku-list">
          <a-table :data="skuList" :bordered="true" :pagination="false">
            <template #columns>
              <a-table-column title="SKU图片" width="80">
                <template #cell="{ record }">
                  <a-image
                    :src="record.imageUrl || currentProduct?.mainImage || '/assets/images/placeholder.png'"
                    :width="40"
                    :height="40"
                    fit="cover"
                  />
                </template>
              </a-table-column>
              <a-table-column title="SKU名称" data-index="skuName" width="120" />
              <a-table-column title="规格" width="120">
                <template #cell="{ record }">
                  <div v-if="record.specs && record.specs.length > 0">
                    <div v-for="spec in record.specs" :key="spec.specNameId" class="text-xs">
                      {{ spec.specName }}: {{ spec.specValue }}
                    </div>
                  </div>
                  <div v-else class="text-gray-500 text-xs">标准规格</div>
                </template>
              </a-table-column>
              <a-table-column title="数量" width="100" align="center">
                <template #cell="{ record }">
                  <a-input-number
                    v-model="record.selectedQuantity"
                    :min="1"
                    :max="record.stock"
                    :precision="0"
                    placeholder="数量"
                    size="small"
                    style="width: 80px"
                  />
                </template>
              </a-table-column>
              <a-table-column title="单价" width="80" align="center">
                <template #cell="{ record }">
                  <div class="text-sm">¥{{ record.salesPrice }}</div>
                </template>
              </a-table-column>
              <a-table-column title="报备价" width="100" align="center">
                <template #cell="{ record }">
                  <a-input-number
                    v-model="record.selectedReportPrice"
                    :min="0"
                    :precision="2"
                    placeholder="报备价"
                    size="small"
                    style="width: 80px"
                  />
                </template>
              </a-table-column>
              <a-table-column title="小计" width="80" align="center">
                <template #cell="{ record }">
                  <div class="text-sm font-medium text-red-500">
                    ¥{{ formatAmount(calculateSkuSubtotal(record)) }}
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="库存" width="60" align="center">
                <template #cell="{ record }">
                  <div class="text-xs" :class="record.stock > 0 ? 'text-green-600' : 'text-red-500'">
                    {{ record.stock }}
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="操作" align="center" width="80">
                <template #cell="{ record }">
                  <a-button
                    type="primary"
                    size="small"
                    @click="selectSkuFromModal(record)"
                    :disabled="record.stock <= 0 || !record.selectedQuantity || !record.selectedReportPrice"
                  >
                    {{ record.stock <= 0 ? '缺货' : '选择' }}
                  </a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import  goodsApi  from '@/api/master/goods.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:visible', 'select-product', 'cancel']);

// 搜索和过滤状态
const searchKeyword = ref('');
const selectedCategory = ref('all');
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const loading = ref(false);

// 商品数据
const productList = ref([]);
const categoryTree = ref([
  {
    title: '全部商品',
    key: 'all',
    children: []
  }
]);

// SKU选择相关
const skuModalVisible = ref(false);
const currentProduct = ref(null);
const skuList = ref([]);

// 获取商品分类树
const fetchCategoryTree = async () => {
  try {
    loading.value = true;

    const result = await goodsApi.category.getList();
    if (result && result.code === 200 && result.data) {
      // 格式化分类树数据
      const formatCategoryTree = (categories) => {
        return categories.map(category => ({
          title: category.name,
          key: category.id.toString(),
          children: category.children && category.children.length > 0
            ? formatCategoryTree(category.children)
            : []
        }));
      };

      // 将格式化后的分类添加到全部商品下
      categoryTree.value[0].children = formatCategoryTree(result.data);
    } else {
      console.log('获取商品分类失败:', result?.message);
    }
  } catch (error) {
    console.error('获取商品分类失败:', error);
    Message.error('获取商品分类失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 搜索商品
const fetchProducts = async () => {
  try {
    loading.value = true;

    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: 1, // 只获取已上架的商品
    };

    // 添加搜索关键词
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value;
    }

    // 添加分类筛选
    if (selectedCategory.value && selectedCategory.value !== 'all') {
      params.categoryId = selectedCategory.value;
    }

    console.log('请求商品列表参数:', params);

    const response = await goodsApi.spu.getList(params);

    console.log('商品列表API响应:', response);

    if (response && response.code === 200) {
      productList.value = response.data.items || [];
      total.value = response.data.pageInfo?.total || 0;

      console.log('商品列表数据:', productList.value);
    } else {
      throw new Error(response?.message || '获取商品列表失败');
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
    Message.error(error.message || '获取商品列表失败，请稍后再试');
    productList.value = [];
  } finally {
    loading.value = false;
  }
};



// 处理分类选择
const handleCategorySelect = (selectedKeys) => {
  if (selectedKeys && selectedKeys.length > 0) {
    selectedCategory.value = selectedKeys[0];
  } else {
    selectedCategory.value = 'all';
  }
  currentPage.value = 1;
  fetchProducts();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchProducts();
};

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = '';
  selectedCategory.value = 'all';
  currentPage.value = 1;
  fetchProducts();
};

// 处理分页变化
const handlePageChange = () => {
  fetchProducts();
};

// 处理商品点击
const handleProductClick = async (product) => {
  if (product.skuType === 2 && product.skus && product.skus.length > 1) {
    // 如果是多规格商品，打开SKU选择弹窗
    currentProduct.value = product;
    // 为每个SKU添加默认的选择数量和报备价
    skuList.value = product.skus.map(sku => ({
      ...sku,
      selectedQuantity: 1,
      selectedReportPrice: parseFloat(sku.salesPrice) || 0
    }));
    skuModalVisible.value = true;
  } else {
    // 如果是单规格商品，直接选择商品
    const sku = product.skus && product.skus.length > 0 ? product.skus[0] : null;
    const price = sku ? parseFloat(sku.salesPrice) : 0;

    selectProduct({
      product_id: product.id,
      sku_id: sku?.id, // 添加skuId字段
      product_name: product.name,
      product_code: sku?.skuCode || product.id,
      product_image: product.mainImage,
      specification: sku?.skuName || '标准规格',
      unit_price: price,
      report_price: price,
      quantity: 1,
      subtotal: price
    });
  }
};

// 计算SKU小计
const calculateSkuSubtotal = (sku) => {
  const quantity = sku.selectedQuantity || 0;
  const price = sku.selectedReportPrice || 0;
  return quantity * price;
};

// 从弹窗选择SKU
const selectSkuFromModal = (sku) => {
  const specText = sku.specs && sku.specs.length > 0
    ? sku.specs.map(spec => `${spec.specName}: ${spec.specValue}`).join(', ')
    : sku.skuName || '标准规格';

  const productData = {
    product_id: currentProduct.value.id,
    sku_id: sku.id, // 添加skuId字段
    product_name: currentProduct.value.name,
    product_code: sku.skuCode,
    product_image: sku.imageUrl || currentProduct.value.mainImage,
    specification: specText,
    unit_price: parseFloat(sku.salesPrice) || 0,
    report_price: sku.selectedReportPrice,
    quantity: sku.selectedQuantity,
    subtotal: calculateSkuSubtotal(sku)
  };

  selectProduct(productData);
  skuModalVisible.value = false;
};

// 选择SKU (保留原有的简单选择功能)
const selectSku = (sku) => {
  const price = parseFloat(sku.salesPrice) || 0;
  const specText = sku.specs && sku.specs.length > 0
    ? sku.specs.map(spec => `${spec.specName}: ${spec.specValue}`).join(', ')
    : sku.skuName || '标准规格';

  selectProduct({
    product_id: currentProduct.value.id,
    sku_id: sku.id, // 添加skuId字段
    product_name: currentProduct.value.name,
    product_code: sku.skuCode,
    product_image: sku.imageUrl || currentProduct.value.mainImage,
    specification: specText,
    unit_price: price,
    report_price: price,
    quantity: 1,
    subtotal: price
  });
  skuModalVisible.value = false;
};

// 选择商品
const selectProduct = (product) => {
  emits('select-product', product);
  Message.success('商品已添加');
};

// 取消选择
const handleCancel = () => {
  emits('update:visible', false);
  emits('cancel');
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return "-";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 组件挂载时获取分类树和商品列表
onMounted(() => {
  fetchCategoryTree();
  fetchProducts();
});
</script>

<style scoped>
.product-selector {
  min-height: 500px;
}

.product-card {
  transition: all 0.3s;
  background: #fff;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-list-container {
  max-height: 600px;
  overflow-y: auto;
}

/* 自定义滚动条 */
.product-list-container::-webkit-scrollbar {
  width: 6px;
}

.product-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.product-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.product-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
