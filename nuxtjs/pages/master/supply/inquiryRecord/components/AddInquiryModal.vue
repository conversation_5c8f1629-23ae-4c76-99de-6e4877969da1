<template>
  <a-modal
    v-model:visible="visible"
    title="新增报价记录"
    width="800px"
    :mask-closable="false"
    :confirm-loading="submitLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="add-inquiry-form"
    >
      <a-form-item label="选择商品" field="selectedProduct">
        <!-- 商品选择按钮 -->
        <div v-if="!selectedProduct" class="product-selector-wrapper">
          <a-button @click="handleOpenProductSelector" type="dashed" block>
            选择商品
          </a-button>
        </div>

        <!-- 已选商品卡片 -->
        <div v-else class="selected-product-card">
          <a-card class="product-card" :bordered="true">
            <div class="product-card-content">
              <div class="product-image">
                <a-image
                  :src="selectedProduct.product_image || '/assets/images/placeholder.png'"
                  :width="80"
                  :height="80"
                  fit="cover"
                  :preview="false"
                />
              </div>
              <div class="product-info">
                <div class="product-name">{{ selectedProduct.product_name }}</div>
                <div class="product-details">
                  <div class="product-sku">SKU: {{ selectedProduct.sku_id }}</div>
                  <div class="product-spec" v-if="selectedProduct.specification">
                    规格: {{ selectedProduct.specification }}
                  </div>
                  <div class="product-price" v-if="selectedProduct.unit_price">
                    单价: ¥{{ formatPrice(selectedProduct.unit_price) }}
                  </div>
                </div>
              </div>
              <div class="product-actions">
                <a-button type="text" size="small" @click="handleChangeProduct">
                  更换
                </a-button>
                <a-button type="text" size="small" status="danger" @click="handleRemoveProduct">
                  移除
                </a-button>
              </div>
            </div>
          </a-card>
        </div>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="报价" field="currentPrice">
            <a-input-number
              v-model="formData.currentPrice"
              placeholder="请输入报价"
              :precision="2"
              :min="0"
              style="width: 100%"
            >
              <template #prefix>¥</template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否含税" field="includeTax">
            <a-select
              v-model="formData.includeTax"
              placeholder="请选择是否含税"
            >
              <a-option :value="true">含税</a-option>
              <a-option :value="false">不含税</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否含运" field="includeShipping">
            <a-select
              v-model="formData.includeShipping"
              placeholder="请选择是否含运"
            >
              <a-option :value="true">含运费</a-option>
              <a-option :value="false">不含运</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="询价员" field="inquirer">
            <a-input
              v-model="currentUserNickname"
              readonly
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="询价时间" field="inquiryTime">
            <a-date-picker
              v-model="formData.inquiryTime"
              style="width: 100%"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择询价时间"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="供应商信息" field="selectedSupplier">
        <!-- 供应商选择按钮 -->
        <div v-if="!selectedSupplier" class="supplier-selector-wrapper">
          <a-button @click="handleOpenSupplierSelector" type="dashed" block>
            选择供应商
          </a-button>
        </div>

        <!-- 已选供应商卡片 -->
        <div v-else class="selected-supplier-card">
          <a-card class="supplier-card" :bordered="true">
            <div class="supplier-card-content">
              <div class="supplier-info">
                <div class="supplier-name">{{ selectedSupplier.name }}</div>
                <div class="supplier-details">
                  <div class="supplier-code">编码: {{ selectedSupplier.code }}</div>
                  <div class="supplier-contact" v-if="selectedSupplier.contactPerson">
                    联系人: {{ selectedSupplier.contactPerson }}
                    <span v-if="selectedSupplier.position" class="supplier-position">
                      ({{ selectedSupplier.position }})
                    </span>
                  </div>
                  <div class="supplier-phone" v-if="selectedSupplier.contactPhone">
                    电话: {{ selectedSupplier.contactPhone }}
                  </div>
                  <div class="supplier-status" v-if="selectedSupplier.cooperation_status">
                    状态: <span class="status-tag">{{ selectedSupplier.cooperation_status }}</span>
                  </div>
                </div>
              </div>
              <div class="supplier-actions">
                <a-button type="text" size="small" @click="handleChangeSupplier">
                  更换
                </a-button>
                <a-button type="text" size="small" status="danger" @click="handleRemoveSupplier">
                  移除
                </a-button>
              </div>
            </div>
          </a-card>
        </div>
      </a-form-item>

      <a-form-item label="备注" field="specification">
        <a-textarea
          v-model="formData.specification"
          placeholder="请输入备注"
          :rows="3"
          allow-clear
        />
      </a-form-item>
    </a-form>

    <!-- 商品选择组件 -->
    <ProductSelector
      :visible="productSelectorVisible"
      @update:visible="productSelectorVisible = $event"
      @select-product="handleProductSelect"
      @cancel="productSelectorVisible = false"
    />

    <!-- 供应商选择组件 -->
    <SupplierSelectModal
      :visible="supplierSelectVisible"
      @update:visible="supplierSelectVisible = $event"
      @confirm="handleSupplierSelect"
      @cancel="supplierSelectVisible = false"
    />
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import dayjs from 'dayjs'
import ProductSelector from './ProductSelector.vue'
import SupplierSelectModal from '../../purchaseOrder/components/SupplierSelectModal/index.vue'
import { inquiryRecordApi } from '@/api/master/csm/inquiryRecord.js'

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(['update:visible', 'submit', 'success'])

// 表单引用
const formRef = ref()

// 商品选择器显示状态
const productSelectorVisible = ref(false)

// 选中的商品
const selectedProduct = ref(null)

// 供应商选择相关状态
const supplierSelectVisible = ref(false)
const selectedSupplier = ref(null)

// 当前用户信息
const currentUserNickname = ref('')

// 提交加载状态
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  selectedProduct: null,
  selectedSupplier: null,
  currentPrice: null,
  includeTax: null,
  includeShipping: null,
  inquirer: '',
  inquiryTime: '',
  specification: '',
  brand: ''
})

// 表单验证规则
const rules = {
  selectedProduct: [
    { required: true, message: '请选择商品' }
  ],
  selectedSupplier: [
    { required: true, message: '请选择供应商' }
  ],
  currentPrice: [
    { required: true, message: '请输入报价' }
  ],
  includeTax: [
    { required: true, message: '请选择是否含税' }
  ],
  includeShipping: [
    { required: true, message: '请选择是否含运' }
  ],
  inquirer: [
    { required: true, message: '请选择询价员' }
  ],
  inquiryTime: [
    { required: true, message: '请选择询价时间' }
  ]
}

// 计算属性：控制弹窗显示
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 打开商品选择器
const handleOpenProductSelector = () => {
  productSelectorVisible.value = true
}

// 处理商品选择
const handleProductSelect = (product) => {
  selectedProduct.value = product
  formData.selectedProduct = product

  // 自动填充相关字段
  formData.specification = product.specification || ''
  formData.brand = product.brand || ''

  // 如果有单价，可以作为默认报价
  if (product.unit_price) {
    formData.currentPrice = product.unit_price
  }

  productSelectorVisible.value = false
}

// 打开供应商选择器
const handleOpenSupplierSelector = () => {
  supplierSelectVisible.value = true
}

// 处理供应商选择
const handleSupplierSelect = (data) => {
  if (data.supplier) {
    selectedSupplier.value = {
      id: data.supplier.id,
      code: data.supplier.code,
      name: data.supplier.name,
      contactPerson: data.supplier.contactPerson || '',
      position: data.supplier.position || '',
      contactPhone: data.supplier.phone || data.supplier.contactPhone || '',
      status: data.supplier.status,
      cooperation_status: data.supplier.cooperation_status || ''
    }
    formData.selectedSupplier = selectedSupplier.value
  }
  supplierSelectVisible.value = false
}

// 更换供应商
const handleChangeSupplier = () => {
  supplierSelectVisible.value = true
}

// 移除供应商
const handleRemoveSupplier = () => {
  selectedSupplier.value = null
  formData.selectedSupplier = null
}

// 更换商品
const handleChangeProduct = () => {
  productSelectorVisible.value = true
}

// 移除商品
const handleRemoveProduct = () => {
  selectedProduct.value = null
  formData.selectedProduct = null
  formData.specification = ''
  formData.brand = ''
  formData.currentPrice = null
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0.00'
  return Number(price).toFixed(2)
}

// 获取当前用户信息
const getCurrentUser = () => {
  try {
    const userMaster = localStorage.getItem('user_master')
    if (userMaster) {
      const userData = JSON.parse(userMaster)
      console.log('userData:', userData)
      currentUserNickname.value = userData.nickname || userData.username || '当前用户'
      // 同时设置表单中的询价员字段
      console.log('currentUserNickname:', currentUserNickname.value)
      formData.inquirer = currentUserNickname.value
    } else {
      currentUserNickname.value = '当前用户'
      formData.inquirer = '当前用户'
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    currentUserNickname.value = '当前用户'
    formData.inquirer = '当前用户'
  }
}



// 重置表单
const resetForm = () => {
  selectedProduct.value = null
  selectedSupplier.value = null
  submitLoading.value = false
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'boolean') {
      formData[key] = null
    } else {
      formData[key] = (key === 'selectedProduct' || key === 'selectedSupplier') ? null : ''
    }
  })
  formRef.value?.resetFields()
}

// 自定义验证函数
const validateForm = () => {
  const errors = []

  if (!selectedProduct.value) {
    errors.push('请选择商品')
  }

  if (!selectedSupplier.value) {
    errors.push('请选择供应商')
  }

  if (!formData.currentPrice || formData.currentPrice <= 0) {
    errors.push('请输入有效的报价')
  }

  if (formData.includeTax === null || formData.includeTax === undefined) {
    errors.push('请选择是否含税')
  }

  if (formData.includeShipping === null || formData.includeShipping === undefined) {
    errors.push('请选择是否含运费')
  }

  if (!formData.inquirer) {
    errors.push('询价员不能为空')
  }

  if (!formData.inquiryTime) {
    errors.push('请选择询价时间')
  }

  return errors
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 使用自定义验证函数
    const validationErrors = validateForm()
    if (validationErrors.length > 0) {
      Message.error(validationErrors[0])
      return
    }



    // 设置加载状态
    submitLoading.value = true

    // 格式化提交数据
    const submitData = {
      // 商品信息
      productName: selectedProduct.value?.product_name || '',
      sku: selectedProduct.value?.sku_id || '',
      productImage: selectedProduct.value?.product_image || '',
      specification: formData.specification || '',
      brand: formData.brand || '',
      unit: selectedProduct.value?.unit || '',

      // 报价信息
      currentPrice: parseFloat(formData.currentPrice),
      includeTax: formData.includeTax,
      includeShipping: formData.includeShipping,

      // 询价信息
      inquirer: formData.inquirer,
      inquiryTime: formData.inquiryTime ?
        dayjs(formData.inquiryTime).format('YYYY-MM-DD HH:mm:ss') :
        dayjs().format('YYYY-MM-DD HH:mm:ss'),

      // 供应商信息
      supplierId: selectedSupplier.value?.id || null,
      supplier: selectedSupplier.value?.name || '',
      supplierCode: selectedSupplier.value?.code || '',
      contactPerson: selectedSupplier.value?.contactPerson || '',
      contactPosition: selectedSupplier.value?.position || '',
      contactPhone: selectedSupplier.value?.contactPhone || '',
      cooperationStatus: selectedSupplier.value?.cooperation_status || '',

      // 备注
      remark: formData.specification || ''
    }

    console.log('提交数据:', submitData)
    console.log('选中的商品:', selectedProduct.value)
    console.log('选中的供应商:', selectedSupplier.value)

    // 调用API创建询价记录
    const response = await inquiryRecordApi.createInquiryRecord(submitData)
    console.log('API响应:', response)

    if (response.code === 200) {
      Message.success('新增报价记录成功')

      // 通知父组件刷新数据
      emit('success', response.data)

      // 关闭弹窗并重置表单
      visible.value = false
      resetForm()
    } else {
      Message.error(response.message || '新增报价记录失败')
    }

  } catch (error) {
    console.error('提交失败:', error)

    // 处理不同类型的错误
    let errorMessage = '新增报价记录失败'

    if (error.response) {
      // 服务器响应错误
      errorMessage = error.response.data?.message || `服务器错误 (${error.response.status})`
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络连接'
    } else {
      // 其他错误
      errorMessage = error.message || errorMessage
    }

    Message.error(errorMessage)
  } finally {
    // 取消加载状态
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  if (submitLoading.value) {
    Message.warning('正在提交中，请稍候...')
    return
  }
  visible.value = false
  resetForm()
}

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置默认询价时间为当前时间（使用Date对象，兼容日期选择器）
    formData.inquiryTime = new Date()
    // 获取当前用户信息
    getCurrentUser()
  } else {
    resetForm()
  }
})

// 组件挂载时获取当前用户信息
onMounted(() => {
  getCurrentUser()
})
</script>

<style scoped>
.add-inquiry-form {
  padding: 16px 0;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #1d2129;
}

:deep(.arco-input-number-prefix) {
  color: #f53f3f;
  font-weight: 500;
}

.product-selector-wrapper {
  width: 100%;
}

.product-selector-wrapper :deep(.arco-input-group) {
  width: 100%;
}

.product-selector-wrapper :deep(.arco-input-group .arco-input) {
  flex: 1;
}

.product-selector-wrapper :deep(.arco-input-group .arco-btn) {
  flex-shrink: 0;
}

/* 商品卡片样式 */
.selected-product-card {
  width: 100%;
}

.product-card {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s;
}

.product-card:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.product-card-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 8px;
}

.product-image {
  flex-shrink: 0;
}

.product-image :deep(.arco-image) {
  border-radius: 4px;
  overflow: hidden;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 8px;
  line-height: 1.4;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-sku {
  font-size: 12px;
  color: #86909c;
  font-family: 'Courier New', monospace;
  background: #f2f3f5;
  padding: 2px 6px;
  border-radius: 2px;
  display: inline-block;
  width: fit-content;
}

.product-spec {
  font-size: 12px;
  color: #4e5969;
}

.product-price {
  font-size: 14px;
  color: #f53f3f;
  font-weight: 500;
}

.product-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 供应商卡片样式 */
.selected-supplier-card {
  width: 100%;
}

.supplier-card {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s;
}

.supplier-card:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.supplier-card-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  padding: 8px;
}

.supplier-info {
  flex: 1;
  min-width: 0;
}

.supplier-name {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 8px;
  line-height: 1.4;
}

.supplier-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplier-code {
  font-size: 12px;
  color: #86909c;
  font-family: 'Courier New', monospace;
  background: #f2f3f5;
  padding: 2px 6px;
  border-radius: 2px;
  display: inline-block;
  width: fit-content;
}

.supplier-contact {
  font-size: 12px;
  color: #4e5969;
}

.supplier-phone {
  font-size: 12px;
  color: #165dff;
  font-family: 'Courier New', monospace;
}

.supplier-position {
  color: #86909c;
  font-style: italic;
}

.supplier-status {
  font-size: 12px;
  color: #4e5969;
}

.status-tag {
  color: #00b42a;
  background: #f0f9f0;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 11px;
}

.supplier-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.supplier-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}
</style>
