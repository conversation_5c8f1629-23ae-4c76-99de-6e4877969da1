<template>
  <a-modal
    v-model:visible="visible"
    title="选择实际供应商"
    width="800px"
    :footer="true"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="supplier-select-modal">
      <!-- 搜索区域 -->
      <div class="search-section">
        <a-input-search
          v-model="searchKeyword"
          placeholder="请输入供应商名称或编码"
          search-button
          @search="handleSearch"
          @press-enter="handleSearch"
          class="search-input"
        />
      </div>

      <!-- 供应商列表 -->
      <div class="supplier-list-section">
        <a-spin :loading="loading" style="width: 100%;">
          <div v-if="supplierList.length === 0 && !loading" class="empty-state">
            <a-empty description="暂无数据" />
          </div>
          
          <div v-else class="supplier-table">
            <div class="table-header">
              <div class="header-cell checkbox-cell">选择</div>
              <div class="header-cell code-cell">编码</div>
              <div class="header-cell name-cell">供应商名称</div>
              <div class="header-cell price-cell" v-if="hasQuotations">报价信息</div>
            </div>

            <div class="table-body">
              <div
                v-for="supplier in supplierList"
                :key="supplier.id"
                class="table-row"
                :class="{ 'selected': selectedSupplier?.id === supplier.id }"
                @click="selectSupplier(supplier)"
              >
                <div class="body-cell checkbox-cell">
                  <a-radio
                    :model-value="selectedSupplier?.id === supplier.id"
                    @change="() => selectSupplier(supplier)"
                  />
                </div>
                <div class="body-cell code-cell">{{ supplier.code }}</div>
                <div class="body-cell name-cell">{{ supplier.name }}</div>
                <div class="body-cell price-cell" v-if="hasQuotations">
                  <div v-if="supplier.quotations && supplier.quotations.length > 0" class="quotation-info">
                    <div
                      v-for="quotation in supplier.quotations.slice(0, 2)"
                      :key="quotation.quotationId"
                      class="quotation-item"
                    >
                      {{ quotation.priceDescription }}
                    </div>
                    <div v-if="supplier.quotations.length > 2" class="more-quotations">
                      +{{ supplier.quotations.length - 2 }}个报价
                    </div>
                  </div>
                  <span v-else class="no-quotation">暂无报价</span>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="total > 0">
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :show-total="true"
          :show-jumper="true"
          :show-page-size="true"
          :page-size-options="['10', '20', '50', '100']"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :disabled="!selectedSupplier">
          确认
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { supplierApi } from '@/api/master/csm/supplier.js'
import { inquiryRecordApi } from '@/api/master/csm/inquiryRecord.js'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const visible = ref(props.visible)
const loading = ref(false)
const searchKeyword = ref('')
const selectedSupplier = ref(null)
const supplierList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 计算属性：是否有报价信息
const hasQuotations = computed(() => {
  return supplierList.value.some(supplier => supplier.quotations && supplier.quotations.length > 0)
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetModal()
    loadSupplierList()
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置弹窗状态
const resetModal = () => {
  searchKeyword.value = ''
  selectedSupplier.value = null
  currentPage.value = 1
  pageSize.value = 10
  supplierList.value = []
  total.value = 0
}

// 获取系统SKU
const getSystemSku = () => {
  // 从订单数据中获取系统SKU
  if (props.orderData?.items && props.orderData.items.length > 0) {
    // 取第一个商品的goodsSkuId作为系统SKU
    return props.orderData.items[0].goodsSkuId
  }

  // 兼容其他可能的SKU字段
  return props.orderData?.systemSku ||
         props.orderData?.goodsSkuId ||
         props.orderData?.product_code ||
         props.orderData?.skuId
}

// 加载供应商列表
const loadSupplierList = async () => {
  loading.value = true
  try {
    // 检查是否有系统SKU，优先使用询价记录接口
    const systemSku = getSystemSku()

    if (systemSku && !searchKeyword.value) {
      console.log('使用系统SKU获取供应商:', systemSku)
      // 使用询价记录接口获取报价供应商
      await loadSuppliersFromInquiryRecords(systemSku)
    } else {
      console.log('使用原供应商接口获取数据')
      // 使用原来的供应商接口
      await loadSuppliersFromSupplierApi()
    }
  } catch (error) {
    console.error('加载供应商列表失败:', error)
    Message.error('加载供应商列表失败，请重试')
    supplierList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 从询价记录接口获取供应商
const loadSuppliersFromInquiryRecords = async (systemSku) => {
  try {
    const response = await inquiryRecordApi.getSuppliersBySkuId(systemSku)

    if (response.code === 200 && response.data) {
      const quotationList = response.data.items || []

      // 转换数据格式，去重供应商
      const supplierMap = new Map()
      quotationList.forEach(quotation => {
        const supplier = quotation.supplierInfo
        if (supplier && supplier.id) {
          if (!supplierMap.has(supplier.id)) {
            supplierMap.set(supplier.id, {
              id: supplier.id,
              code: supplier.supplierCode,
              name: supplier.supplierName,
              // 添加报价信息用于显示
              quotations: []
            })
          }
          // 添加报价信息
          supplierMap.get(supplier.id).quotations.push({
            price: quotation.currentPrice,
            priceDescription: quotation.priceDescription,
            quotationId: quotation.quotationId
          })
        }
      })

      supplierList.value = Array.from(supplierMap.values())
      total.value = supplierList.value.length

      // 如果没有找到供应商，回退到原接口
      if (supplierList.value.length === 0) {
        await loadSuppliersFromSupplierApi()
      }
    } else {
      // 如果询价记录接口失败，回退到原接口
      await loadSuppliersFromSupplierApi()
    }
  } catch (error) {
    console.error('从询价记录获取供应商失败:', error)
    // 回退到原接口
    await loadSuppliersFromSupplierApi()
  }
}

// 从供应商接口获取供应商（原逻辑）
const loadSuppliersFromSupplierApi = async () => {
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      status: 1 // 只获取启用状态的供应商
    }

    const response = await supplierApi.getSelectList(params)

    if (response.code === 200 && response.data) {
      supplierList.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      console.error('获取供应商列表失败:', response.message)
      Message.error(response.message || '获取供应商列表失败')
      supplierList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('从供应商API获取数据失败:', error)
    throw error
  }
}



// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadSupplierList()
}

// 选择供应商
const selectSupplier = (supplier) => {
  selectedSupplier.value = supplier
}

// 分页变化处理
const handlePageChange = (page) => {
  currentPage.value = page
  loadSupplierList()
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadSupplierList()
}

// 确认选择
const handleConfirm = () => {
  if (!selectedSupplier.value) {
    Message.warning('请选择一个供应商')
    return
  }
  
  emit('confirm', {
    supplier: selectedSupplier.value,
    orderData: props.orderData
  })
  
  visible.value = false
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.supplier-select-modal {
  padding: 16px 0;
}

.search-section {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
}

.supplier-list-section {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.supplier-table {
  width: 100%;
}

.table-header {
  display: flex;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e6eb;
  font-weight: 500;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f7f8fa;
}

.table-row.selected {
  background: #e8f4ff;
}

.header-cell,
.body-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e5e6eb;
}

.header-cell:last-child,
.body-cell:last-child {
  border-right: none;
}

.checkbox-cell {
  width: 80px;
  justify-content: center;
}

.code-cell {
  width: 120px;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.price-cell {
  width: 200px;
  min-width: 200px;
}

.quotation-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quotation-item {
  font-size: 12px;
  color: #1d2129;
  background: #f2f3f5;
  padding: 2px 6px;
  border-radius: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-quotations {
  font-size: 12px;
  color: #86909c;
  font-style: italic;
}

.no-quotation {
  font-size: 12px;
  color: #c9cdd4;
}

.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
