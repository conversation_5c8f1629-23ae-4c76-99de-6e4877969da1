<template>
  <div class="basic-info-add">
    <div class="add-header">
      <h3>基础信息</h3>
    </div>
    <div class="add-content">
      <a-form 
        ref="formRef"
        :model="formData" 
        :rules="rules"
        layout="vertical"
        :label-col-props="{ span: 24 }"
        :wrapper-col-props="{ span: 24 }"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="供应商名称" field="supplierName">
              <a-input
                v-model="formData.supplierName"
                placeholder="请输入供应商名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商代码" field="supplierCode">
              <a-input-group>
                <a-input
                  v-model="formData.supplierCode"
                  placeholder="请输入供应商代码"
                  allow-clear
                />
                <a-button @click="formData.supplierCode = generateSupplierCode()">
                  重新生成
                </a-button>
              </a-input-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="公司性质" field="companyNature">
              <a-select
                v-model="formData.companyNature"
                placeholder="请选择公司性质"
                allow-clear
              >
                <a-option
                  v-for="option in enterpriseNatureOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="主营产品" field="mainProductIds">
              <a-select
                v-model="formData.mainProductIds"
                placeholder="请选择主营产品"
                multiple
                allow-clear
                allow-create
              >
                <a-option
                  v-for="option in mainProductOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合作方式" field="cooperationType">
              <a-select
                v-model="formData.cooperationType"
                placeholder="请选择合作方式"
                allow-clear
              >
                <a-option value="代加工">长期合作</a-option>
                <a-option value="代加工+授权">代加工+授权</a-option>
                <a-option value="授权">授权</a-option>
                <a-option value="独家代理">独家代理</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合作状态" field="cooperationStatus">
              <a-select
                v-model="formData.cooperationStatus"
                placeholder="请选择合作状态"
                allow-clear
              >
                <a-option value="启用">启用</a-option>
                <a-option value="待审核">待审核</a-option>
                <a-option value="黑名单">黑名单</a-option>
                <a-option value="不启用">不启用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="合作协议" field="cooperationAgreement">
              <a-select
                v-model="formData.cooperationAgreement"
                placeholder="请选择合作协议"
                allow-clear
              >
                <a-option
                  v-for="option in cooperationAgreementOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="注册资本" field="registeredCapital">
              <a-input-number
                v-model="formData.registeredCapital"
                placeholder="请输入注册资本"
                :min="0"
                :precision="2"
                hide-button
                mode="button"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="下单次数" field="orderCount">
              <a-input-number
                v-model="formData.orderCount"
                placeholder="请输入下单次数"
                :min="0"
                :precision="0"
                hide-button
                mode="button"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="供应商分组" field="supplierGroup">
              <a-select
                v-model="formData.supplierGroup"
                placeholder="请选择供应商分组"
                allow-clear
              >
                <a-option value="线上">线上</a-option>
                <a-option value="线下">线下</a-option>
                <a-option value="其他">其他</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="注册时间" field="registerTime">
              <a-date-picker
                v-model="formData.registerTime"
                placeholder="请选择注册时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="统一信用代码" field="creditCode">
              <a-input
                v-model="formData.creditCode"
                placeholder="请输入统一信用代码"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商关联" field="supplierRelation">
              <a-select
                v-model="formData.supplierRelation"
                placeholder="请选择供应商关联"
                allow-clear
                multiple
                :loading="supplierRelationLoading"
              >
                <a-option
                  v-for="option in supplierRelationOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="通讯地址" field="address">
              <a-cascader
                v-model="formData.address"
                :options="regionTreeOptions"
                placeholder="请选择通讯地址"
                allow-clear
                allow-search
                :field-names="{ value: 'name', label: 'name', children: 'children' }"
                path-mode
                expand-trigger="hover"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="详细地址" field="detailedAddress">
              <a-textarea
                v-model="formData.detailedAddress"
                placeholder="请输入详细地址"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生产地址" field="productionAddress">
              <a-textarea
                v-model="formData.productionAddress"
                placeholder="请输入生产地址"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存基础信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { mainProductApi } from '@/api/master/csm/mainProduct.js';
import { enterpriseNatureApi } from '@/api/master/csm/enterpriseNature.js';
import { cooperationAgreementApi } from '@/api/master/csm/cooperationAgreement.js';
import { supplierApi } from '@/api/master/csm/supplier.js';
import commonApi from '@/api/common.js';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const formRef = ref();
const saving = ref(false);
const supplierRelationLoading = ref(false);

// 下拉框数据
const mainProductOptions = ref([]);
const enterpriseNatureOptions = ref([]);
const cooperationAgreementOptions = ref([]);
const regionTreeOptions = ref([]);
const supplierRelationOptions = ref([]);

// 表单数据
const formData = reactive({
  supplierName: '',
  supplierCode: '',
  companyNature: '',
  mainProductIds: [],
  cooperationType: '',
  cooperationStatus: '',
  cooperationAgreement: '',
  registeredCapital: null,
  orderCount: null,
  supplierGroup: '',
  registerTime: '',
  creditCode: '',
  supplierRelation: [], // 确保供应商关联字段初始化为空数组
  address: '',
  detailedAddress: '',
  productionAddress: '',
  ...props.formData // 用props数据覆盖默认值
});

// 表单验证规则
const rules = {
  supplierName: [
    { required: true, message: '请输入供应商名称' },
    { minLength: 2, message: '供应商名称至少2个字符' }
  ],
  supplierCode: [
    { pattern: /^[A-Z0-9]{3,20}$/, message: '供应商代码格式不正确' }
  ],
  companyNature: [
    { required: true, message: '请选择公司性质' }
  ],
  mainProductIds: [
    { required: true, message: '请选择主营产品' }
  ],
  cooperationType: [
  ],
  cooperationStatus: [
    { required: true, message: '请选择合作状态' }
  ],
  address: [
    { required: true, message: '请输入通讯地址' }
  ],
  detailedAddress: [
    { required: true, message: '请输入详细地址' }
  ],
  creditCode: [
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '统一信用代码格式不正确' }
  ]
};

// 防止循环更新的标志
let isUpdatingFromProps = false;

// 监听表单数据变化
watch(formData, (newVal) => {
  // 如果正在从props更新，跳过emit
  if (isUpdatingFromProps) {
    return;
  }

  // 处理地址数据格式
  const processedData = { ...newVal };

  // 如果地址是数组（path-mode返回的格式），转换为字符串用于提交
  if (Array.isArray(processedData.address)) {
    // 用分隔符连接所有值：云南/丽江市/宁蒗彝族自治县
    processedData.address = processedData.address.join('/');
  }

  // 处理主营产品字段：确保是数组格式
  if (!processedData.mainProductIds) {
    processedData.mainProductIds = [];
  } else if (!Array.isArray(processedData.mainProductIds)) {
    processedData.mainProductIds = [processedData.mainProductIds];
  }
  // 过滤掉空值
  processedData.mainProductIds = processedData.mainProductIds.filter(id => id && id !== '');

  // 处理供应商关联字段：确保是数组格式，如果为空则设为空数组
  if (!processedData.supplierRelation) {
    processedData.supplierRelation = [];
  } else if (!Array.isArray(processedData.supplierRelation)) {
    processedData.supplierRelation = [processedData.supplierRelation];
  }
  // 过滤掉空值
  processedData.supplierRelation = processedData.supplierRelation.filter(id => id && id !== '');

  console.log('处理后的数据 - mainProductIds:', processedData.mainProductIds);
  console.log('处理后的数据 - supplierRelation:', processedData.supplierRelation);

  emit('update:formData', processedData);
}, { deep: true });

// 监听props变化（使用标志避免循环更新）
watch(() => props.formData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 设置标志，防止触发formData的watch
    isUpdatingFromProps = true;

    // 更新formData
    Object.assign(formData, newVal);

    // 确保mainProductIds是数组格式
    if (formData.mainProductIds && !Array.isArray(formData.mainProductIds)) {
      formData.mainProductIds = [];
    }

    // 确保supplierRelation是数组格式
    if (formData.supplierRelation && !Array.isArray(formData.supplierRelation)) {
      formData.supplierRelation = [];
    }

    // 重置标志
    setTimeout(() => {
      isUpdatingFromProps = false;
    }, 0);
  }
}, { immediate: true });

// 生成随机供应商代码
const generateSupplierCode = () => {
  const prefix = 'SUP';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${timestamp}${random}`;
};

// 获取主营产品列表
const getMainProductList = async () => {
  try {
    const result = await mainProductApi.getList();
    const response = result.data
    console.log(response, '主营产品列表');
    if (response.items && Array.isArray(response.items)) {
      mainProductOptions.value = response.items.map(item => ({
        label: item.product_name || item.name,
        value: item.id // 使用产品ID作为value
      }));
    }
  } catch (error) {
    console.error('获取主营产品列表失败:', error);
    // 使用默认数据作为备选
    mainProductOptions.value = [
      { label: '电子产品', value: 1 },
      { label: '机械设备', value: 2 },
      { label: '化工原料', value: 3 },
      { label: '建筑材料', value: 4 },
      { label: '纺织服装', value: 5 },
      { label: '食品饮料', value: 6 }
    ];
  }
};

// 获取企业性质列表
const getEnterpriseNatureList = async () => {
  try {
    const result = await enterpriseNatureApi.getList();
    const response = result.data
    console.log(response, '企业性质列表');
    if (response.items && Array.isArray(response.items)) {
      console.log(response.items, '企业性质列表');
      enterpriseNatureOptions.value = response.items.map(item => ({
        label: item.nature_name || item.name,
        value: item.nature_name || item.name || String(item.id)
      }));
    }
  } catch (error) {
    console.error('获取企业性质列表失败:', error);
    // 使用默认数据作为备选
    enterpriseNatureOptions.value = [
      { label: '有限责任公司', value: '有限责任公司' },
      { label: '股份有限公司', value: '股份有限公司' },
      { label: '外商独资企业', value: '外商独资企业' },
      { label: '国有企业', value: '国有企业' },
      { label: '合伙企业', value: '合伙企业' },
      { label: '个体工商户', value: '个体工商户' }
    ];
  }
};

// 获取合作协议列表
const getCooperationAgreementList = async () => {
  try {
    const result = await cooperationAgreementApi.getList();
    const response = result.data;
    console.log(response, '合作协议列表');
    if (response.items && Array.isArray(response.items)) {
      cooperationAgreementOptions.value = response.items.map(item => ({
        label: item.agreement_name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取合作协议列表失败:', error);
    // 使用默认数据作为备选
    cooperationAgreementOptions.value = [];
  }
};

// 递归过滤空的children数组
const filterEmptyChildren = (data) => {
  if (!Array.isArray(data)) return data;

  return data.map(item => {
    const newItem = { ...item };

    // 如果有children属性
    if (newItem.children) {
      // 如果children是空数组，删除children属性
      if (Array.isArray(newItem.children) && newItem.children.length === 0) {
        delete newItem.children;
      } else if (Array.isArray(newItem.children) && newItem.children.length > 0) {
        // 如果children不为空，递归处理
        newItem.children = filterEmptyChildren(newItem.children);
        // 处理后如果children变为空数组，删除children属性
        if (newItem.children.length === 0) {
          delete newItem.children;
        }
      }
    }

    return newItem;
  });
};

// 获取地区树数据
const getRegionTreeData = async () => {
  try {
    const result = await commonApi.getTreeRegion();
    console.log(result, '地区树数据');
    if (result && result.code === 200 && result.data) {
      // 过滤空的children数组
      regionTreeOptions.value = filterEmptyChildren(result.data);
      console.log(regionTreeOptions.value, '过滤后的地区树数据');
    }
  } catch (error) {
    console.error('获取地区树数据失败:', error);
    // 使用默认数据作为备选
    regionTreeOptions.value = [
      {
        code: "110000",
        name: "北京市",
        children: [
          {
            code: "110100",
            name: "北京市",
            children: [
              {
                code: "110101",
                name: "东城区"
              },
              {
                code: "110102",
                name: "西城区"
              }
            ]
          }
        ]
      }
    ];
  }
};

// 获取供应商关联数据
const getSupplierRelationList = async () => {
  supplierRelationLoading.value = true;
  try {
    const result = await supplierApi.getRelationSelectList({
      status: 1, // 只获取启用状态的供应商
      pageSize: 1000 // 获取足够多的数据
    });
    console.log(result, '供应商关联列表');
    if (result && result.code === 200 && result.data && result.data.list) {
      supplierRelationOptions.value = result.data.list.map(item => ({
        label: item.name, // 供应商名称
        value: String(item.id)    // 供应商ID，转换为字符串格式
      }));

    }
  } catch (error) {
    console.error('获取供应商关联列表失败:', error);
    // 使用默认数据作为备选
    supplierRelationOptions.value = [
      { label: '独立供应商', value: 'independent' },
      { label: '关联供应商', value: 'related' },
      { label: '子公司', value: 'subsidiary' },
      { label: '分公司', value: 'branch' }
    ];
  } finally {
    supplierRelationLoading.value = false;
  }
};

// 验证工具函数
const isEmptyValue = (value) => {
  if (value === undefined || value === null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  return false;
};

// 验证表单
const validate = async () => {
  try {
    console.log('开始验证基础信息:', formData);

    const result = await formRef.value.validate();
    console.log('表单验证结果:', result);

    // 额外的业务验证
    console.log('检查供应商名称:', formData.supplierName, typeof formData.supplierName);
    if (isEmptyValue(formData.supplierName)) {
      throw new Error('供应商名称不能为空');
    }

    console.log('检查供应商代码:', formData.supplierCode, typeof formData.supplierCode);
    if (isEmptyValue(formData.supplierCode)) {
      throw new Error('供应商代码不能为空');
    }

    console.log('检查公司性质:', formData.companyNature, typeof formData.companyNature);
    if (isEmptyValue(formData.companyNature)) {
      throw new Error('公司性质不能为空');
    }

    console.log('检查主营产品:', formData.mainProductIds, typeof formData.mainProductIds);
    console.log('主营产品是否为数组:', Array.isArray(formData.mainProductIds));
    console.log('主营产品长度:', formData.mainProductIds ? formData.mainProductIds.length : 'undefined');
    if (isEmptyValue(formData.mainProductIds)) {
      console.error('主营产品验证失败，当前值:', formData.mainProductIds);
      throw new Error('主营产品不能为空');
    }

    console.log('检查合作状态:', formData.cooperationStatus, typeof formData.cooperationStatus);
    if (isEmptyValue(formData.cooperationStatus)) {
      throw new Error('合作状态不能为空');
    }

    console.log('检查通讯地址:', formData.address, typeof formData.address);
    if (isEmptyValue(formData.address)) {
      throw new Error('通讯地址不能为空');
    }

    console.log('检查详细地址:', formData.detailedAddress, typeof formData.detailedAddress);
    if (isEmptyValue(formData.detailedAddress)) {
      throw new Error('详细地址不能为空');
    }

    console.log('基础信息验证通过');
    return true;
  } catch (error) {
    console.error('基础信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});

// 组件挂载时初始化
onMounted(() => {
  // 如果供应商代码为空，自动生成
  if (!formData.supplierCode) {
    formData.supplierCode = generateSupplierCode();
  }

  // 获取下拉框数据
  getMainProductList();
  getEnterpriseNatureList();
  getCooperationAgreementList();
  getRegionTreeData();
  getSupplierRelationList();
});
</script>

<style scoped>
.basic-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
