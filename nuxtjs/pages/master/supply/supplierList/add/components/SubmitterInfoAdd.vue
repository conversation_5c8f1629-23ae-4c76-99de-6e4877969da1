<template>
  <div class="submitter-info-add">
    <a-card title="提交人信息" :bordered="false">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        auto-label-width
      >
        <a-row :gutter="24">
          <!-- 部门 -->
          <a-col :span="8">
            <a-form-item label="部门" field="department">
              <a-input
                v-model="formData.department"
                placeholder="部门信息"
                disabled
                readonly
              />
            </a-form-item>
          </a-col>

          <!-- 提交人 -->
          <a-col :span="8">
            <a-form-item label="提交人" field="submitter" required>
              <a-input
                v-model="formData.submitter"
                placeholder="提交人信息"
                disabled
                readonly
              />
            </a-form-item>
          </a-col>

          <!-- 复核人 -->
          <a-col :span="8">
            <a-form-item label="复核人" field="reviewer">
              <a-select
                v-model="formData.reviewer"
                placeholder="请选择复核人"
                allow-clear
                :loading="userLoading"
                @search="handleUserSearch"
                filterable
              >
                <a-option
                  v-for="user in userOptions"
                  :key="user.value"
                  :value="user.value"
                  :label="user.label"
                >
                  {{ user.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import systemApi from '@/api/master/system.js';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData']);

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  department: '',
  submitter: '',
  submitTime: '',
  reviewer: ''
});

// 用户选项
const userOptions = ref([]);
const userLoading = ref(false);

// 表单验证规则
const rules = {
  submitter: [
    { required: true, message: '提交人不能为空' }
  ]
};

// 监听props变化
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(formData, newData);
  }
}, { immediate: true, deep: true });

// 监听formData变化，向上传递
watch(formData, (newData) => {
  emit('update:formData', { ...newData });
}, { deep: true });

// 获取当前用户信息
const getCurrentUserInfo = () => {
  try {
    const userMaster = localStorage.getItem('user_master');
    if (userMaster) {
      const userData = JSON.parse(userMaster);
      console.log('当前用户信息:', userData);
      
      // 设置部门信息
      formData.department = userData.dept_name || '未设置部门';
      
      // 设置提交人信息
      formData.submitter = userData.nickname || userData.username || '当前用户';
      
      return userData;
    } else {
      console.warn('未找到用户信息');
      formData.department = '未设置部门';
      formData.submitter = '当前用户';
      return null;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    formData.department = '未设置部门';
    formData.submitter = '当前用户';
    return null;
  }
};

// 搜索用户
const handleUserSearch = async (value) => {
  if (!value || value.trim() === '') {
    await loadUsers();
    return;
  }
  await loadUsers(value);
};

// 加载用户列表
const loadUsers = async (searchValue = '') => {
  userLoading.value = true;
  try {
    const params = {
      page: 1,
      pageSize: 100
    };

    if (searchValue) {
      params.username = searchValue;
    }

    const res = await systemApi.user.getList(params);
    if (res.code === 200) {
      userOptions.value = res.data.items.map((user) => {
        // 构建显示格式：昵称（账号）部门
        let label = '';
        if (user.nickname) {
          label += user.nickname;
        }
        if (user.username) {
          label += `（${user.username}）`;
        }
        if (user.dept_name) {
          label += user.dept_name;
        }

        return {
          label: label || user.username,
          value: user.nickname || user.username, // 使用nickname作为value
          userId: user.id // 保留用户ID用于API调用
        };
      });
    } else {
      console.error('获取用户列表失败', res);
      Message.error('获取用户列表失败');
    }
  } catch (err) {
    console.error('获取用户列表失败', err);
    Message.error('获取用户列表失败');
  } finally {
    userLoading.value = false;
  }
};

// 表单验证
const validate = async () => {
  try {
    const valid = await formRef.value?.validate();
    return !valid; // validate返回错误信息，没有错误时返回undefined
  } catch (error) {
    console.error('表单验证失败:', error);
    return false;
  }
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  getCurrentUserInfo(); // 重新获取当前用户信息
};

// 组件挂载时初始化
onMounted(() => {
  getCurrentUserInfo();
  loadUsers(); // 初始加载用户列表
});

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm,
  userOptions
});
</script>

<style scoped>
.submitter-info-add {
  padding: 0;
}

:deep(.arco-card-body) {
  padding: 20px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
}

:deep(.arco-input[disabled]) {
  background-color: #f7f8fa;
  color: #86909c;
}
</style>
