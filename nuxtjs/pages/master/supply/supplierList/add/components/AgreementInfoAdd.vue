<template>
  <div class="agreement-info-add">
    <div class="add-header">
      <h4>协议信息</h4>
    </div>
    <div class="add-content">
      <div class="agreement-list">
        <div v-for="(agreement, index) in formData" :key="index" class="agreement-item">
          <a-card>
            <a-form
              :ref="(el) => setFormRef(el, index)"
              :model="agreement"
              :rules="agreementRules"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-form-item
                    label="协议类型"
                    field="agreementType"
                    :validate-trigger="['change', 'blur']"
                  >
                    <a-select
                      v-model="agreement.agreementType"
                      placeholder="请选择协议类型"
                      allow-clear
                    >
                      <a-option value="委托代加工协议">委托代加工协议</a-option>
                      <a-option value="独家合作协议">独家合作协议</a-option>
                      <a-option value="返点协议">返点协议</a-option>
                      <a-option value="经销授权协议">经销授权协议</a-option>
                      <a-option value="合作框架协议">合作框架协议</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item
                    label="协议名称"
                    field="agreementName"
                    :validate-trigger="['change', 'blur']"
                  >
                    <a-input
                      v-model="agreement.agreementName"
                      placeholder="请输入协议名称"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
              <a-col :span="4">
                <a-form-item label="开始日期">
                  <a-date-picker
                    v-model="agreement.startDate"
                    placeholder="请选择开始日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="结束日期">
                  <a-date-picker
                    v-model="agreement.endDate"
                    placeholder="请选择结束日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label=" ">
                  <a-button 
                    type="text" 
                    status="danger" 
                    @click="removeAgreement(index)"
                    :disabled="formData.length <= 1"
                  >
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="协议状态">
                  <a-select 
                    v-model="agreement.status" 
                    placeholder="请选择协议状态"
                    allow-clear
                  >
                    <a-option value="active">生效中</a-option>
                    <a-option value="pending">待生效</a-option>
                    <a-option value="expired">已过期</a-option>
                    <a-option value="terminated">已终止</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="签署日期">
                  <a-date-picker
                    v-model="agreement.signDate"
                    placeholder="请选择签署日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="协议期限">
                  <a-input 
                    v-model="agreement.duration" 
                    placeholder="如：12个月"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="附件文件">
                  <a-upload
                    :file-list="agreement.fileList || []"
                    :show-file-list="true"
                    :auto-upload="false"
                    @change="handleFileChange(index, $event)"
                    @before-upload="(file) => handleBeforeUpload(file, index)"
                    accept="*/*"
                  >
                    <template #upload-button>
                      <a-button type="outline" size="small">
                        <template #icon><icon-upload /></template>
                        上传文件
                      </a-button>
                    </template>
                  </a-upload>
                </a-form-item>
              </a-col>
            </a-row>
            </a-form>
          </a-card>
        </div>
      </div>

      <div class="add-agreement-section">
        <a-button type="dashed" @click="addAgreement" style="width: 100%">
          <template #icon><icon-plus /></template>
          添加协议
        </a-button>
      </div>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存协议信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import commonApi from '@/api/common.js';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单引用数组
const formRefs = ref([]);

// 设置表单引用
const setFormRef = (el, index) => {
  if (el) {
    formRefs.value[index] = el;
  }
};

// 协议表单验证规则
const agreementRules = {
  agreementType: [
    { required: true, message: '请选择协议类型' }
  ],
  agreementName: [
    { required: true, message: '请输入协议名称' },
    { minLength: 2, message: '协议名称至少2个字符' }
  ]
};

// 表单数据
const formData = reactive([...props.formData]);

// 如果没有协议数据，添加一个默认项
if (formData.length === 0) {
  formData.push({
    id: Date.now(),
    agreementType: '',
    agreementName: '',
    startDate: '',
    endDate: '',
    status: 'pending',
    signDate: '',
    duration: '',
    fileUrl: '',
    fileList: []
  });
}

// 添加协议
const addAgreement = () => {
  formData.push({
    id: Date.now(),
    agreementType: '',
    agreementName: '',
    startDate: '',
    endDate: '',
    status: 'pending',
    signDate: '',
    duration: '',
    fileUrl: '',
    fileList: []
  });

  // 确保表单引用数组有足够的空间
  formRefs.value.length = formData.length;
};

// 删除协议
const removeAgreement = (index) => {
  if (formData.length > 1) {
    formData.splice(index, 1);
    // 同时删除对应的表单引用
    formRefs.value.splice(index, 1);
  }
};

// 上传前处理
const handleBeforeUpload = async (file, index) => {
  console.log('准备上传协议文件:', file);
  console.log('协议索引:', index);

  try {
    // 创建FormData
    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    uploadFormData.append('dir', 'supplier-agreements');
    uploadFormData.append('module', 'supplier');
    uploadFormData.append('bizType', 'agreement');
    uploadFormData.append('bizId', '');
    uploadFormData.append('isPublic', 'true');

    console.log('FormData内容:');
    for (let [key, value] of uploadFormData.entries()) {
      console.log(key, value);
    }

    // 调用上传API
    const response = await commonApi.uploadImage(uploadFormData);

    if (response.code === 200) {
      // 上传成功，更新文件信息
      const uploadedFile = {
        uid: response.data.id,
        name: response.data.fileName,
        url: response.data.fileUrl,
        size: parseInt(response.data.fileSize),
        type: file.type,
        status: 'done',
      };

      // 更新协议的文件URL和文件列表
      formData[index].fileUrl = response.data.fileUrl;
      formData[index].fileList = [uploadedFile];

      Message.success('文件上传成功');
    } else {
      throw new Error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    Message.error('文件上传失败: ' + (error.message || '未知错误'));
  }

  // 返回false阻止默认上传行为
  return false;
};

// 处理文件上传
const handleFileChange = (index, fileList) => {
  formData[index].fileList = fileList;
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
  if (formData.length === 0) {
    addAgreement();
  }
}, { deep: true });

// 验证工具函数
const isEmptyValue = (value) => {
  if (value === undefined || value === null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  return false;
};

// 验证表单
const validate = async () => {
  try {
    console.log('开始验证协议信息:', formData);

    // 验证每个协议表单
    const validationPromises = formRefs.value.map(async (formRef, index) => {
      if (formRef) {
        try {
          await formRef.validate();
          return true;
        } catch (error) {
          console.error(`协议${index + 1}验证失败:`, error);
          throw new Error(`协议${index + 1}验证失败`);
        }
      }
      return true;
    });

    await Promise.all(validationPromises);

    // 额外的业务验证
    for (let i = 0; i < formData.length; i++) {
      const agreement = formData[i];

      console.log(`检查协议${i + 1}类型:`, agreement.agreementType);
      if (isEmptyValue(agreement.agreementType)) {
        throw new Error(`协议${i + 1}的协议类型不能为空`);
      }

      console.log(`检查协议${i + 1}名称:`, agreement.agreementName);
      if (isEmptyValue(agreement.agreementName)) {
        throw new Error(`协议${i + 1}的协议名称不能为空`);
      }
    }

    console.log('协议信息验证通过');
    return true;
  } catch (error) {
    console.error('协议信息验证失败:', error);
    Message.error(error.message || '协议信息验证失败');
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.agreement-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

.agreement-list {
  margin-bottom: 16px;
}

.agreement-item {
  margin-bottom: 16px;
}

.add-agreement-section {
  margin-bottom: 24px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
