<template>
  <a-drawer
    v-model:visible="visible"
    title="新增供应商信息"
    :width="1200"
    :footer="false"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="true"
    @close="handleClose"
    placement="right"
  >
    <div class="supplier-add">
      <!-- 步骤指示器 -->
      <div class="steps-section">
        <a-steps :current="currentStep" size="small">
          <a-step title="基础信息" />
          <a-step title="账户信息" />
          <a-step title="品牌信息" />
          <a-step title="联系人信息" />
          <a-step title="协议信息" />
          <a-step title="附件信息" />
          <a-step title="提交人信息" />
        </a-steps>
      </div>

      <!-- 步骤内容区域 -->
      <div class="step-content-section">
        <!-- 基础信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <BasicInfoAdd
            ref="basicInfoRef"
            v-model:form-data="formData.basicInfo"
            :errors="errors.basicInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 账户信息 -->
        <div v-show="currentStep === 1" class="step-content">
          <AccountInfoAdd
            ref="accountInfoRef"
            v-model:form-data="formData.accountInfo"
            :errors="errors.accountInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 品牌信息 -->
        <div v-show="currentStep === 2" class="step-content">
          <BrandInfoAdd
            ref="brandInfoRef"
            v-model:form-data="formData.brandInfo"
            :errors="errors.brandInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 联系人信息 -->
        <div v-show="currentStep === 3" class="step-content">
          <ContactInfoAdd
            ref="contactInfoRef"
            v-model:form-data="formData.contactInfo"
            :errors="errors.contactInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 协议信息 -->
        <div v-show="currentStep === 4" class="step-content">
          <AgreementInfoAdd
            ref="agreementInfoRef"
            v-model:form-data="formData.agreementInfo"
            :errors="errors.agreementInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 附件信息 -->
        <div v-show="currentStep === 5" class="step-content">
          <AttachmentInfoAdd
            ref="attachmentInfoRef"
            v-model:form-data="formData.attachmentInfo"
            :errors="errors.attachmentInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 提交人信息 -->
        <div v-show="currentStep === 6" class="step-content">
          <SubmitterInfoAdd
            ref="submitterInfoRef"
            v-model:form-data="formData.submitterInfo"
            :errors="errors.submitterInfo"
            :show-save-button="false"
          />
        </div>
      </div>

      <!-- 步骤操作按钮 -->
      <div class="step-actions">
        <a-button
          v-if="currentStep > 0"
          @click="handlePrevStep"
          :disabled="saving"
        >
          上一步
        </a-button>
        <a-button
          v-if="currentStep < 6"
          type="primary"
          @click="handleNextStep"
          :disabled="saving"
        >
          下一步
        </a-button>
        <a-button
          v-if="currentStep === 6"
          type="primary"
          @click="handleSaveAll"
          :loading="saving"
        >
          <template #icon><icon-save /></template>
          保存新增
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { IconSave } from '@arco-design/web-vue/es/icon';
import { supplierApi } from '@/api/master/csm/supplier.js';
import BasicInfoAdd from './components/BasicInfoAdd.vue';
import AccountInfoAdd from './components/AccountInfoAdd.vue';
import BrandInfoAdd from './components/BrandInfoAdd.vue';
import ContactInfoAdd from './components/ContactInfoAdd.vue';
import AgreementInfoAdd from './components/AgreementInfoAdd.vue';
import AttachmentInfoAdd from './components/AttachmentInfoAdd.vue';
import SubmitterInfoAdd from './components/SubmitterInfoAdd.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'save-success']);

const visible = ref(props.visible);
const currentStep = ref(0); // 当前步骤，从0开始
const saving = ref(false);

// 表单引用
const basicInfoRef = ref();
const accountInfoRef = ref();
const brandInfoRef = ref();
const contactInfoRef = ref();
const agreementInfoRef = ref();
const attachmentInfoRef = ref();
const submitterInfoRef = ref();

// 表单数据
const formData = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: [],
  contactInfo: [],
  agreementInfo: [],
  attachmentInfo: {},
  submitterInfo: {}
});

// 错误信息
const errors = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: {},
  contactInfo: {},
  agreementInfo: {},
  attachmentInfo: {},
  submitterInfo: {}
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initFormData();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    currentStep.value = 0; // 重置到第一步
  }
});

// 初始化表单数据（新增模式，设置默认值）
const initFormData = () => {
  // 基础信息默认值
  formData.basicInfo = {
    supplierName: '',
    supplierCode: '',
    companyNature: '',
    mainProducts: [],
    cooperationType: '',
    cooperationStatus: '待审核',
    cooperationAgreement: '',
    supplierGroup: '',
    address: '',
    detailedAddress: '',
    productionAddress: '',
    registerTime: '',
    creditCode: '',
    supplierRelation: '',
    orderCount: 0,
    registeredCapital: 0
  };

  // 账户信息默认值
  formData.accountInfo = {
    paymentTerms: '',
    invoiceType: '',
    taxRate: '',
    accountName: '',
    settlementMethod: '',
    accountNumber: '',
    bankName: '',
    registeredCapital: ''
  };

  // 品牌信息默认值
  formData.brandInfo = [];

  // 联系人信息默认值
  formData.contactInfo = [];

  // 协议信息默认值
  formData.agreementInfo = [];

  // 附件信息默认值
  formData.attachmentInfo = {};

  // 提交人信息默认值
  formData.submitterInfo = {
    department: '',
    submitter: '',
    reviewer: ''
  };
};

// 步骤引用映射
const stepRefs = {
  0: () => basicInfoRef.value,
  1: () => accountInfoRef.value,
  2: () => brandInfoRef.value,
  3: () => contactInfoRef.value,
  4: () => agreementInfoRef.value,
  5: () => attachmentInfoRef.value,
  6: () => submitterInfoRef.value
};

// 下一步
const handleNextStep = async () => {
  // 验证当前步骤
  const currentRef = stepRefs[currentStep.value]();
  if (currentRef?.validate) {
    const isValid = await currentRef.validate();
    if (!isValid) {
      Message.error('请检查当前步骤信息填写是否正确');
      return;
    }
  }

  // 进入下一步
  if (currentStep.value < 6) {
    currentStep.value++;
  }
};

// 上一步
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 保存所有信息
const handleSaveAll = async () => {
  saving.value = true;
  try {
    // 验证最后一步
    const currentRef = stepRefs[currentStep.value]();
    if (currentRef?.validate) {
      const isValid = await currentRef.validate();
      if (!isValid) {
        Message.error('请检查提交人信息填写是否正确');
        return;
      }
    }

    // 组装完整的供应商数据
    const completeSupplierData = {
      // 基础信息
      ...formData.basicInfo,
      // 账户信息
      ...formData.accountInfo,
      // 品牌信息
      brands: formData.brandInfo,
      // 联系人信息
      contacts: formData.contactInfo,
      // 协议信息
      agreements: formData.agreementInfo,
      // 附件信息
      attachments: formData.attachmentInfo,
      // 提交人信息
      department: formData.submitterInfo.department || '采购部',
      submitter: formData.submitterInfo.submitter,
      reviewer: formData.submitterInfo.reviewer, // 保存nickname
      // 系统信息
      updater: null,
      updateDate: null
    };

    // 确保必填字段有值
    if (!completeSupplierData.cooperationType) {
      completeSupplierData.cooperationType = '长期合作'; // 默认值
    }

    // 数据清理和格式化
    if (completeSupplierData.registerTime && typeof completeSupplierData.registerTime === 'string') {
      // 确保日期格式正确
      try {
        completeSupplierData.registerTime = new Date(completeSupplierData.registerTime).toISOString();
      } catch (e) {
        completeSupplierData.registerTime = null;
      }
    }

    // 处理合作协议字段：如果是数字ID，需要转换为字符串或保持原样（根据后端要求）
    if (completeSupplierData.cooperationAgreement && typeof completeSupplierData.cooperationAgreement === 'number') {
      // 将数字ID转换为字符串
      completeSupplierData.cooperationAgreement = String(completeSupplierData.cooperationAgreement);
    }

    // 处理供应商关联字段：将数组转换为逗号分隔的字符串
    console.log('原始supplierRelation:', completeSupplierData.supplierRelation, typeof completeSupplierData.supplierRelation);
    if (Array.isArray(completeSupplierData.supplierRelation)) {
      // 过滤掉空值，然后转换为逗号分隔的字符串
      const validIds = completeSupplierData.supplierRelation.filter(id => id && id !== '');
      completeSupplierData.supplierRelation = validIds.join(',');
      console.log('转换后supplierRelation:', completeSupplierData.supplierRelation);
    } else if (!completeSupplierData.supplierRelation) {
      completeSupplierData.supplierRelation = '';
    }

    console.log('最终cooperationAgreement:', completeSupplierData.cooperationAgreement, typeof completeSupplierData.cooperationAgreement);
    console.log('最终supplierRelation:', completeSupplierData.supplierRelation, typeof completeSupplierData.supplierRelation);

    // 清理空字符串字段，但保留必填字段
    const requiredFields = ['supplierName', 'supplierCode', 'companyNature', 'address', 'cooperationType', 'cooperationStatus', 'paymentTerms', 'department', 'submitter', 'submitDate'];

    Object.keys(completeSupplierData).forEach(key => {
      if (completeSupplierData[key] === '' && !requiredFields.includes(key)) {
        completeSupplierData[key] = null;
      }
    });

    // 调用API创建供应商
    const response = await supplierApi.create(completeSupplierData);

    if (response.code === 201 || response.code === 200) {
      emit('save-success', response.data || completeSupplierData);

      // 重置表单和步骤
      currentStep.value = 0;
      initFormData();
    } else {
      throw new Error(response.message || '创建失败');
    }

  } catch (error) {
    console.error('供应商新增失败:', error);

    // 处理不同类型的错误
    if (error.response?.data?.errors) {
      // 参数验证错误
      const errorMessages = error.response.data.errors.join('; ');
      Message.error(`参数验证失败: ${errorMessages}`);
    } else if (error.response?.data?.message) {
      // 服务器返回的错误消息
      Message.error(error.response.data.message);
    } else if (error.message) {
      // 其他错误
      Message.error(error.message);
    } else {
      // 默认错误消息
      Message.error('供应商新增失败，请重试');
    }
  } finally {
    saving.value = false;
  }
};

// 关闭处理
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.supplier-add {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.steps-section {
  padding: 20px 20px 0 20px;
  background-color: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border-2);
}

.step-content-section {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.step-content {
  height: 100%;
}

.step-actions {
  padding: 16px 20px;
  background-color: var(--color-bg-1);
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-actions .arco-btn + .arco-btn {
  margin-left: 12px;
}

:deep(.arco-drawer-body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}
</style>
