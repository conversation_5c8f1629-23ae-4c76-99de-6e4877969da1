<template>
  <div class="supplier-list">
    <!-- 自定义添加按钮 -->
    <div class="custom-toolbar">
      <a-button type="primary" @click="handleAdd">
        <template #icon><icon-plus /></template>
        新增供应商
      </a-button>
    </div>

    <ma-crud
      ref="crudRef"
      :key="crudKey"
      :options="crudOptions"
      :columns="columns"
    >
      <!-- 自定义Tab标题 -->
      <template #tabTitle-全部>
        <span class="tab-title-with-badge">
          全部
          <a-badge :count="supplierStats.total" v-if="supplierStats.total > 0" class="ml-2" />
        </span>
      </template>

      <template #tabTitle-待处理>
        <span class="tab-title-with-badge">
          待处理
          <a-badge :count="supplierStats.pending" v-if="supplierStats.pending > 0" class="ml-2" />
        </span>
      </template>

      <!-- 供应商信息 -->
      <template #supplierInfo="{ record }">
        <div class="supplier-info" >
          <div style="display: flex; align-items: center;">
            <div><span>供应商名称：</span><span>{{ record.supplierName }}</span></div>
            <div style="margin-left: 10px;"><span>代码：</span>{{ record.supplierCode }}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <div><span>联系人：</span>{{ record.contactPerson }}</div>
            <div style="margin-left: 10px;"> {{ record.position }}</div>
            <div style="margin-left: 10px;">  {{ record.phone }}</div>
          </div>
          <div><span>公司性质：</span>{{ record.companyNature }}</div>
          <div><span>通信地址：</span>{{ record.address }}</div>
        </div>
      </template>
      <!-- 操作信息 -->
      <template #operationInfo="{ record }">
        <div class="operation-info">
          <div><span>部门：</span>{{ record.department }}</div>
          <div><span>提交人：</span>{{ record.submitter }}</div>
          <div><span>更新人：</span>{{ record.updater || '-' }}</div>
          <div><span>复审人：</span>{{ record.reviewer || '-' }}</div>
          <div><span>提交日期：</span>{{ formatDate(record.submitDate) }}</div>
          <div><span>更新时间：</span>{{ formatDate(record.updatedAt) || '-' }}</div>
        </div>
      </template>
      <!-- 主营产品 -->
      <template #mainProducts="{ record }">
        <div class="products">
          <a-tag v-for="product in record.mainProducts" :key="product.id" color="blue">
            {{ product.name }}
          </a-tag>
        </div>
      </template>

      <!-- 合作信息 -->
      <template #cooperationInfo="{ record }">
        <div class="cooperation-info">
          <div class="cooperation-item">
            <span class="cooperation-label">合作方式：</span>
            <a-tag :color="getCooperationTypeColor(record.cooperationType)">
              {{ record.cooperationType }}
            </a-tag>
          </div>
          <div class="cooperation-item">
            <span class="cooperation-label">合作状态：</span>
            <a-tag :color="getCooperationStatusColor(record.cooperationStatus)">
              {{ record.cooperationStatus }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- 品牌授权 -->
      <template #brandAuthorization="{ record }">
        <div class="brand-auth">
          <div v-for="brand in record.brands" :key="brand.id || brand.brandName" class="brand-item">
            <a-tag :color="brand.authorized ? 'green' : 'orange'">
              {{ brand.brandName || brand.name }}{{ brand.authorized ? '(已授权)' : '(未授权)' }}
            </a-tag>
          </div>
        </div>
      </template>


      <!-- 自定义操作按钮 -->
      <template #operationCell="{ record }">
        <a-space direction="vertical" fill>
          <a-button type="text" size="small" @click="handleView(record)">
            <template #icon><icon-eye /></template>
            详情
          </a-button>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="handleChangeReviewer(record)">
            <template #icon><icon-user /></template>
            更换复核人
          </a-button>
        </a-space>
      </template>

      <!-- 合作信息搜索自定义 -->
      <template #search-cooperation_info="{ searchForm, component }">
        <a-select
          v-model="searchForm.cooperation_info"
          placeholder="请选择合作方式或状态"
          allow-clear
        >
          <a-optgroup label="合作方式">
            <a-option value="长期合作">长期合作</a-option>
            <a-option value="项目合作">项目合作</a-option>
            <a-option value="临时采购">临时采购</a-option>
          </a-optgroup>
          <a-optgroup label="合作状态">
            <a-option value="启用">启用</a-option>
            <a-option value="待审核">待审核</a-option>
            <a-option value="黑名单">黑名单</a-option>
            <a-option value="不启用">不启用</a-option>
          </a-optgroup>
        </a-select>
      </template>
    </ma-crud>

    <!-- 供应商详情组件 -->
    <SupplierDetail
      v-model:visible="detailVisible"
      :supplier-data="currentSupplier"
      @edit-success="handleEditSuccess"
    />

    <!-- 供应商编辑组件 -->
    <SupplierEdit
      v-model:visible="editVisible"
      :supplier-data="currentSupplier"
      @save-success="handleEditSuccess"
    />

    <!-- 供应商添加组件 -->
    <SupplierAdd
      v-model:visible="addVisible"
      @save-success="handleAddSuccess"
    />

    <!-- 更换复核人弹框 -->
    <a-modal
      v-model:visible="changeReviewerVisible"
      title="更换复核人"
      width="400px"
      @ok="handleConfirmChangeReviewer"
      @cancel="handleCancelChangeReviewer"
      ok-text="确认"
      cancel-text="取消"
    >
      <a-form :model="reviewerForm" layout="vertical">
        <a-form-item label="选择复核人：" required>
          <a-select
            v-model="reviewerForm.reviewer"
            placeholder="请选择"
            allow-clear
            :loading="reviewerLoading"
            :options="reviewerOptions"
          />
        </a-form-item>
        <div class="reviewer-note">
          <span style="color: #ff4d4f;">注：一旦更换，原来的复核人将不再具有该供应商的复核权限。</span>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, h, onMounted, computed, watch, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import { supplierApi, mainProductApi, enterpriseNatureApi, paymentTermApi } from '@/api/master/csm';
import systemApi from '@/api/master/system.js';
import SupplierDetail from './detail/index.vue';
import SupplierEdit from './edit/index.vue';
import SupplierAdd from './add/index.vue';

// 定义页面路由元信息
definePageMeta({
  name: "supplierList",
  path: "/master/supply/supplierList",
});

const crudRef = ref();
const crudKey = ref(0);

// 详情相关
const detailVisible = ref(false);
const editVisible = ref(false);
const addVisible = ref(false);
const currentSupplier = ref({});

// 统计信息
const supplierStats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  disabled: 0,
  blacklist: 0
});

// 更换复核人相关
const changeReviewerVisible = ref(false);
const reviewerForm = reactive({
  reviewer: ''
});

// 复核人选项和加载状态
const reviewerOptions = ref([]);
const reviewerLoading = ref(false);

// 搜索项数据源
const mainProductOptions = ref([]);
const enterpriseNatureOptions = ref([]);
const paymentTermOptions = ref([]);

// 获取主营产品列表
const getMainProductList = async () => {
  try {
    const result = await mainProductApi.getList();
    console.log('主营产品列表:', result);
    if (result && result.code === 200 && result.data && Array.isArray(result.data.items)) {
      mainProductOptions.value = result.data.items.map(item => ({
        label: item.product_name,
        value: item.product_name
      }));
      console.log('主营产品选项:', mainProductOptions.value);
    }
  } catch (error) {
    console.error('获取主营产品列表失败:', error);
    // 使用默认数据作为备选
    mainProductOptions.value = [
      { label: "电子产品", value: "电子产品" },
      { label: "机械设备", value: "机械设备" },
      { label: "化工原料", value: "化工原料" },
      { label: "建筑材料", value: "建筑材料" },
      { label: "纺织服装", value: "纺织服装" },
      { label: "食品饮料", value: "食品饮料" }
    ];
  }
};

// 获取企业性质列表
const getEnterpriseNatureList = async () => {
  try {
    const result = await enterpriseNatureApi.getList();
    console.log('企业性质列表:', result);
    if (result && result.code === 200 && result.data && Array.isArray(result.data.items)) {
      console.log('企业性质原始数据:', result.data.items);
      enterpriseNatureOptions.value = result.data.items.map(item => {
        console.log('企业性质单项:', item);
        // 尝试多个可能的字段名
        const label = item.nature_name || item.name || item.enterprise_nature || `未知性质-${item.id}`;
        console.log('企业性质标签:', label);
        return {
          label: label,
          value: label
        };
      });
      console.log('企业性质选项:', enterpriseNatureOptions.value);
    }
  } catch (error) {
    console.error('获取企业性质列表失败:', error);
    // 使用默认数据作为备选
    enterpriseNatureOptions.value = [
      { label: "有限责任公司", value: "有限责任公司" },
      { label: "股份有限公司", value: "股份有限公司" },
      { label: "外商独资企业", value: "外商独资企业" },
      { label: "国有企业", value: "国有企业" },
      { label: "合伙企业", value: "合伙企业" },
      { label: "个体工商户", value: "个体工商户" }
    ];
  }
};

// 获取付款条件列表
const getPaymentTermList = async () => {
  try {
    const result = await paymentTermApi.getList();
    console.log('付款条件列表:', result);
    if (result && result.code === 200 && result.data && Array.isArray(result.data.items)) {
      console.log('付款条件原始数据:', result.data.items);
      paymentTermOptions.value = result.data.items.map(item => {
        console.log('处理付款条件项:', item);
        // 尝试多个可能的字段名
        const label = item.term_name || item.name || item.payment_term || item.title || `未知条件-${item.id}`;
        return {
          label: label,
          value: label
        };
      });
      console.log('付款条件选项:', paymentTermOptions.value);
    }
  } catch (error) {
    console.error('获取付款条件列表失败:', error);
    // 使用默认数据作为备选
    paymentTermOptions.value = [
      { label: "月结30天", value: "月结30天" },
      { label: "月结60天", value: "月结60天" },
      { label: "预付30%，发货前70%", value: "预付30%，发货前70%" },
      { label: "货到付款", value: "货到付款" },
      { label: "信用证付款", value: "信用证付款" },
      { label: "现金支付", value: "现金支付" }
    ];
  }
};

// 获取复核人列表
const getReviewerList = async () => {
  try {
    reviewerLoading.value = true;

    // 调用系统用户API获取复核人列表
    const response = await systemApi.user.getList({
      page: 1,
      pageSize: 100,
      status: 1 // 只获取启用状态的用户
    });

    if (response.code === 200 && response.data && response.data.items) {
      // 处理用户数据，只保留必要字段
      reviewerOptions.value = response.data.items.map(user => ({
        label: user.nickname || user.username,
        value: user.nickname || user.username
      }));
      console.log('复核人选项:', reviewerOptions.value);
    } else {
      console.error('获取复核人列表失败:', response.message);
      // 使用默认数据作为备选
      reviewerOptions.value = [
        { label: '供应链-胡晓梅', value: '供应链-胡晓梅' },
        { label: '陈伟锋', value: '陈伟锋' },
        { label: '供应链-陈小娇', value: '供应链-陈小娇' },
        { label: '供应链-吴诗君', value: '供应链-吴诗君' },
        { label: '供应链-匡晓西', value: '供应链-匡晓西' }
      ];
    }
  } catch (error) {
    console.error('获取复核人列表失败:', error);
    // 使用默认数据作为备选
    reviewerOptions.value = [
      { label: '供应链-胡晓梅', value: '供应链-胡晓梅' },
      { label: '陈伟锋', value: '陈伟锋' },
      { label: '供应链-陈小娇', value: '供应链-陈小娇' },
      { label: '供应链-吴诗君', value: '供应链-吴诗君' },
      { label: '供应链-匡晓西', value: '供应链-匡晓西' }
    ];
  } finally {
    reviewerLoading.value = false;
  }
};

// 获取供应商统计信息
const getSupplierStats = async () => {
  try {
    const response = await supplierApi.getStats();
    if (response && response.code === 200 && response.data) {
      supplierStats.value = response.data;
      console.log('供应商统计信息:', supplierStats.value);
    }
  } catch (error) {
    console.error('获取供应商统计信息失败:', error);
  }
};

// 真实API函数
const realApi = {
  // 获取列表
  getList: async (params = {}) => {
    try {
      const response = await supplierApi.getList(params);
      return response;
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      Message.error('获取供应商列表失败');
      throw error;
    }
  },

  // 新增
  create: async (data) => {
    try {
      const response = await supplierApi.create(data);
      Message.success('新增供应商成功');
      return response;
    } catch (error) {
      console.error('新增供应商失败:', error);
      Message.error(error.message || '新增供应商失败');
      throw error;
    }
  },

  // 更新
  update: async (id, data) => {
    try {
      const response = await supplierApi.update(id, data);
      Message.success('更新供应商成功');
      return response;
    } catch (error) {
      console.error('更新供应商失败:', error);
      Message.error(error.message || '更新供应商失败');
      throw error;
    }
  },

  // 删除
  delete: async (id) => {
    try {
      const response = await supplierApi.delete(id);
      Message.success('删除供应商成功');
      return response;
    } catch (error) {
      console.error('删除供应商失败:', error);
      Message.error(error.message || '删除供应商失败');
      throw error;
    }
  }
};


// ma-crud 配置选项
const crudOptions = reactive({
  api: realApi.getList,
  title: "供应商列表管理",
  searchLabelWidth: "100px",
  searchColNumber: 4,
  rowSelection: false,
  operationColumn: true,
  showIndex: false,
  indexLabel: "序号",
  pageLayout: "fixed",
  // 导出功能配置
  export: {
    show: true
  },
  // Tab配置
  tabs: {
    type: 'line',
    trigger: 'click',
    data: [
      { label: '全部', value: 'all' },
      { label: '待处理', value: 'pending' }
    ],
    defaultKey: 'all',
    searchKey: 'cooperation_status',
    onChange: (value) => {
      console.log('Tab切换:', value);
      if (value === 'pending') {
        crudRef.value.requestParams.cooperation_status = '待审核';
      } else {
        delete crudRef.value.requestParams.cooperation_status;
      }
      crudRef.value.refresh();
    }
  },

  // 表单配置
  formOption: {
    width: 900,
    viewType: 'modal'
  },

  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },

  // 新增配置 - 禁用内置添加功能
  add: {
    show: false,
    api: realApi.create,
    text: "新增供应商"
  },

  // 编辑配置
  edit: {
    show: false,
    api: realApi.update,
  },

  // 删除配置
  delete: {
    show: false,
    api: realApi.delete,
  },
});

// 表格列配置
const columns = ref([
  {
    title: "供应商信息",
    dataIndex: "supplierInfo",
    width: 300,
    search: false,
  },
  // 搜索项
  {
    title: "部门",
    dataIndex: "department",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入部门",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "提交人",
    dataIndex: "submitter",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入提交人",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "供应商名称",
    dataIndex: "supplierName",
    width: 150,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入供应商名称",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入品牌名称",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "供应商关联",
    dataIndex: "supplierRelation",
    width: 120,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入供应商关联",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "主营产品",
    dataIndex: "mainProducts",
    width: 80,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "3333", value: "3333" },
        { label: "测试123主营627", value: "测试123主营627" },
        { label: "编辑22", value: "编辑22" },
        { label: "电子产品", value: "电子产品" },
        { label: "机械设备", value: "机械设备" }
      ]
    },
    searchPlaceholder: "请选择主营产品",
    customRender: ({ record }) => {
      return h('div', { class: 'products' },
        record.mainProducts?.map(product =>
          h('a-tag', { color: 'blue', style: 'margin-bottom: 4px; display: block;' },
            product.name || product)
        ) || []
      );
    }
  },
  {
    title: "公司性质",
    dataIndex: "companyNature",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: []
    },
    searchPlaceholder: "请选择公司性质",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "复核人",
    dataIndex: "reviewer",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入复核人",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "合作方式",
    dataIndex: "cooperationTypeSearch",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "代加工", value: "代加工" },
        { label: "代加工+授权", value: "代加工+授权" },
        { label: "授权", value: "授权" },
        { label: "独家代理", value: "独家代理" }
      ]
    },
    searchPlaceholder: "请选择合作方式",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "合作状态",
    dataIndex: "cooperationStatusSearch",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "启用", value: "启用" },
        { label: "待审核", value: "待审核" },
        { label: "黑名单", value: "黑名单" },
        { label: "不启用", value: "不启用" }
      ]
    },
    searchPlaceholder: "请选择合作状态",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "授权情况",
    dataIndex: "authorizationStatus",
    width: 100,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "有", value: "有" },
        { label: "无", value: "无" }
      ]
    },
    searchPlaceholder: "请选择授权情况",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "付款条件",
    dataIndex: "paymentTerms",
    width: 150,
    search: true,
    formType: "select",
    dict: {
      data: []
    },
    searchPlaceholder: "请选择付款条件",
  },
  {
    title: "通讯地址",
    dataIndex: "address",
    width: 150,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入通讯地址",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "提交日期",
    dataIndex: "submitDateRange",
    width: 150,
    search: true,
    formType: "range",
    searchPlaceholder: "请选择提交日期",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "品牌-授权",
    dataIndex: "brandAuthorization",
    width: 100,
    search: false,
  },
  {
    title: "合作信息",
    dataIndex: "cooperationInfo",
    width: 100,
    search: false,
  },
  {
    title: "操作人信息",
    dataIndex: "operationInfo",
    width: 150,
    search: false,
  },
]);

// 更新字典数据的函数
const updateColumnDict = () => {
  console.log('开始更新字典数据...');

  // 找到主营产品列并更新其字典数据
  const mainProductColumn = columns.value.find(col => col.dataIndex === 'mainProducts');
  if (mainProductColumn && mainProductOptions.value.length > 0) {
    console.log('更新主营产品字典:', mainProductOptions.value);
    mainProductColumn.dict = {
      data: [...mainProductOptions.value]
    };
  }

  // 找到公司性质列并更新其字典数据
  const companyNatureColumn = columns.value.find(col => col.dataIndex === 'companyNature');
  console.log('公司性质列:', companyNatureColumn);
  console.log('企业性质选项数据:', enterpriseNatureOptions.value);
  if (companyNatureColumn && enterpriseNatureOptions.value.length > 0) {
    console.log('更新公司性质字典:', enterpriseNatureOptions.value);
    companyNatureColumn.dict = {
      data: [...enterpriseNatureOptions.value]
    };
  }

  // 找到付款条件列并更新其字典数据
  const paymentTermsColumn = columns.value.find(col => col.dataIndex === 'paymentTerms');
  console.log('付款条件列:', paymentTermsColumn);
  console.log('付款条件选项数据:', paymentTermOptions.value);
  if (paymentTermsColumn && paymentTermOptions.value.length > 0) {
    console.log('更新付款条件字典:', paymentTermOptions.value);
    paymentTermsColumn.dict = {
      data: [...paymentTermOptions.value]
    };
  }
};

// 监听数据变化并更新columns
watch([mainProductOptions, enterpriseNatureOptions, paymentTermOptions], () => {
  updateColumnDict();
  // 强制触发ma-crud重新渲染
  crudKey.value++;
  console.log('强制刷新ma-crud组件，新key:', crudKey.value);
}, { deep: true });

// 获取合作方式颜色
const getCooperationTypeColor = (type) => {
  const colorMap = {
    "代加工": "blue",
    "代加工+授权": "green",
    "授权": "orange",
    "独家代理": "yellow"
  };
  return colorMap[type] || "gray";
};

// 获取合作状态颜色
const getCooperationStatusColor = (status) => {
  const colorMap = {
    "启用": "green",
    "待审核": "blue",
    "黑名单": "red",
    "不启用": "gray"
  };
  return colorMap[status] || "gray";
};

// 事件处理方法
const handleView = async (record) => {
  try {
    // 获取完整的供应商详情信息
    const response = await supplierApi.getById(record.id);
    if (response && response.code === 200 && response.data) {
      currentSupplier.value = response.data;
      console.log('获取到的供应商详情:', response.data);
    } else {
      // 如果获取详情失败，使用列表数据
      currentSupplier.value = record;
      console.log('使用列表数据:', record);
    }
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    // 如果出错，使用列表数据
    currentSupplier.value = record;
  }
  detailVisible.value = true;
};

const handleEdit = async (record) => {
  try {
    // 获取完整的供应商详情信息
    const response = await supplierApi.getById(record.id);
    if (response && response.code === 200 && response.data) {
      currentSupplier.value = response.data;
      console.log('获取到的供应商编辑详情:', response.data);
    } else {
      // 如果获取详情失败，使用列表数据
      currentSupplier.value = record;
      console.log('使用列表数据进行编辑:', record);
    }
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    // 如果出错，使用列表数据
    currentSupplier.value = record;
  }
  editVisible.value = true;
};

const handleAdd = () => {
  addVisible.value = true;
};

// 更换复核人相关方法
const handleChangeReviewer = async (record) => {
  currentSupplier.value = record;
  reviewerForm.reviewer = record.reviewer || '';
  changeReviewerVisible.value = true;

  // 获取复核人列表
  await getReviewerList();
};

const handleConfirmChangeReviewer = async () => {
  if (!reviewerForm.reviewer) {
    Message.error('请选择复核人');
    return;
  }

  try {
    // 调用真实API
    await supplierApi.changeReviewer(currentSupplier.value.id, {
      reviewer: reviewerForm.reviewer,
      updater: '系统管理员' // 这里可以替换为当前登录用户
    });

    Message.success(`已成功更换复核人为：${reviewerForm.reviewer}`);
    changeReviewerVisible.value = false;
    crudRef.value.refresh();
    // 刷新统计信息
    getSupplierStats();
  } catch (error) {
    Message.error(error.message || '更换复核人失败，请重试');
  }
};

const handleCancelChangeReviewer = () => {
  changeReviewerVisible.value = false;
  reviewerForm.reviewer = '';
};

const handleEditSuccess = (updatedData) => {
  // 刷新列表数据
  crudRef.value.refresh();
  // 刷新统计信息
  getSupplierStats();
};

const handleAddSuccess = (newData) => {
  Message.success('供应商添加成功');
  // 刷新列表数据
  crudRef.value.refresh();
  // 刷新统计信息
  getSupplierStats();
  // 关闭添加弹窗
  addVisible.value = false;
};

// 日期格式化函数
const formatDate = (dateValue) => {
  if (!dateValue) return '-';

  let date;
  if (typeof dateValue === 'number') {
    // 时间戳
    date = new Date(dateValue);
  } else if (typeof dateValue === 'string') {
    // ISO字符串
    date = new Date(dateValue);
  } else {
    return '-';
  }

  if (isNaN(date.getTime())) return '-';

  return date.toLocaleDateString('zh-CN');
};

// 组件挂载时初始化数据
onMounted(() => {
  // 获取搜索项的数据源
  getMainProductList();
  getEnterpriseNatureList();
  getPaymentTermList();
  // 预先获取复核人列表
  getReviewerList();
  // 获取供应商统计信息
  getSupplierStats();
});
</script>

<style scoped>
.supplier-list {
  padding: 20px;
  background-color: var(--color-bg-1);
}

.custom-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.operation-info {
  line-height: 1.6;
}

.operation-info div {
  margin-bottom: 4px;
}

.supplier-info {
  line-height: 1.6;
}

.supplier-info div {
  margin-bottom: 4px;
}

.brand-auth {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.brand-item {
  margin-bottom: 4px;
}

.products {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.products .arco-tag {
  margin-bottom: 4px;
}

.cooperation-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.cooperation-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cooperation-label {
  font-size: 12px;
  color: var(--color-text-3);
  min-width: 32px;
}

.reviewer-note {
  margin-top: 12px;
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  font-size: 12px;
}

.tab-title-with-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-title-with-badge .arco-badge {
  margin-left: 4px;
}
</style>