<template>
  <a-drawer
    v-model:visible="visible"
    title="供应商详情"
    :width="1200"
    :footer="false"
    unmount-on-close
  >
    <template #footer>
      <div class="detail-footer">
        <a-space>
          <a-button @click="handleClose">关闭</a-button>
          <a-button type="primary" @click="handleEdit">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
        </a-space>
      </div>
    </template>
    <div class="supplier-detail">
      <!-- 基础信息区域 - 固定在顶部 -->
      <div class="basic-info-section">
        <BasicInfo :supplier-data="supplierData" />
      </div>

      <!-- Tab切换区域 -->
      <div class="tab-section">
        <a-tabs v-model:active-key="activeTab" type="line" size="large">
          <a-tab-pane key="submitter" title="提交人信息">
            <SubmitterInfo :supplier-data="supplierData" />
          </a-tab-pane>
          <a-tab-pane key="account" title="账户信息">
            <AccountInfo :supplier-data="supplierData" />
          </a-tab-pane>
          <a-tab-pane key="brand" title="品牌信息">
            <BrandInfo :supplier-data="supplierData" />
          </a-tab-pane>
          <a-tab-pane key="contact" title="联系人信息">
            <ContactInfo :supplier-data="supplierData" />
          </a-tab-pane>
          <a-tab-pane key="agreement" title="协议信息">
            <AgreementInfo :supplier-data="supplierData" />
          </a-tab-pane>
          <a-tab-pane key="attachment" title="附件信息">
            <AttachmentInfo :supplier-data="supplierData" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-drawer>

  <!-- 编辑组件 -->
  <SupplierEdit
    v-model:visible="editVisible"
    :supplier-data="supplierData"
    @save-success="handleEditSuccess"
  />
</template>

<script setup>
import { ref, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import BasicInfo from './components/BasicInfo.vue';
import SubmitterInfo from './components/SubmitterInfo.vue';
import AccountInfo from './components/AccountInfo.vue';
import BrandInfo from './components/BrandInfo.vue';
import ContactInfo from './components/ContactInfo.vue';
import AgreementInfo from './components/AgreementInfo.vue';
import AttachmentInfo from './components/AttachmentInfo.vue';
import SupplierEdit from '../edit/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'edit-success']);

const visible = ref(props.visible);
const activeTab = ref('submitter'); // 默认显示提交人信息
const editVisible = ref(false);

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    // 每次打开时重置到默认tab
    activeTab.value = 'submitter';
  }
});

// 事件处理
const handleClose = () => {
  visible.value = false;
};

const handleEdit = () => {
  editVisible.value = true;
};

const handleEditSuccess = (updatedData) => {
  Message.success('编辑成功');
  emit('edit-success', updatedData);
  // 可以在这里刷新详情数据
};
</script>

<style scoped>
.supplier-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.basic-info-section {
  flex-shrink: 0;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border-2);
}

.tab-section {
  flex: 1;
  overflow: hidden;
}

:deep(.arco-tabs-content) {
  height: calc(100vh - 300px);
  overflow-y: auto;
  padding: 20px 0;
}

:deep(.arco-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}
</style>
