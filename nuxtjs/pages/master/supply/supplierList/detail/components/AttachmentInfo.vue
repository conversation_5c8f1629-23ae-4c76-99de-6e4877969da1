<template>
  <div class="attachment-info">
    <div class="info-header">
      <h4>附件信息</h4>
    </div>
    <div class="attachment-content">
      <!-- 营业执照 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>营业执照</span>
          <span class="file-count">({{ businessLicenseCount }}/1)</span>
        </div>
        <div class="attachment-grid">
          <div v-if="attachments.businessLicense?.length > 0" class="image-list">
            <div
              v-for="(image, index) in attachments.businessLicense"
              :key="index"
              class="image-item"
              @click="previewImage(image)"
            >
              <a-image
                :src="image.url"
                :width="120"
                :height="90"
                fit="cover"
                show-loader
                :preview="false"
              />
              <div class="image-overlay">
                <icon-eye class="preview-icon" />
              </div>
            </div>
          </div>
          <div v-else class="empty-attachment">
            <icon-image class="empty-icon" />
            <span>暂无营业执照</span>
          </div>
        </div>
      </div>

      <!-- 开户证明 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>开户证明</span>
          <span class="file-count">({{ bankProofCount }}/1)</span>
        </div>
        <div class="attachment-grid">
          <div v-if="attachments.bankProof?.length > 0" class="image-list">
            <div
              v-for="(image, index) in attachments.bankProof"
              :key="index"
              class="image-item"
              @click="previewImage(image)"
            >
              <a-image
                :src="image.url"
                :width="120"
                :height="90"
                fit="cover"
                show-loader
                :preview="false"
              />
              <div class="image-overlay">
                <icon-eye class="preview-icon" />
              </div>
            </div>
          </div>
          <div v-else class="empty-attachment">
            <icon-image class="empty-icon" />
            <span>暂无开户证明</span>
          </div>
        </div>
      </div>

      <!-- 法人身份证 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>法人身份证</span>
          <span class="file-count">({{ idCardCount }}/2)</span>
        </div>
        <div class="attachment-grid">
          <div v-if="attachments.idCard?.length > 0" class="image-list">
            <div
              v-for="(image, index) in attachments.idCard"
              :key="index"
              class="image-item"
              @click="previewImage(image)"
            >
              <a-image
                :src="image.url"
                :width="120"
                :height="90"
                fit="cover"
                show-loader
                :preview="false"
              />
              <div class="image-overlay">
                <icon-eye class="preview-icon" />
              </div>
              <div class="image-label">{{ image.type === 'front' ? '正面' : '反面' }}</div>
            </div>
          </div>
          <div v-else class="empty-attachment">
            <icon-image class="empty-icon" />
            <span>暂无法人身份证</span>
          </div>
        </div>
      </div>

      <!-- 其他附件 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>其他附件</span>
          <span class="file-count">({{ otherAttachmentsCount }}/5)</span>
        </div>
        <div class="attachment-grid">
          <div v-if="attachments.others?.length > 0" class="image-list">
            <div 
              v-for="(image, index) in attachments.others" 
              :key="index"
              class="image-item"
              @click="previewImage(image)"
            >
              <a-image
                :src="image.url"
                :width="120"
                :height="90"
                fit="cover"
                show-loader
                :preview="false"
              />
              <div class="image-overlay">
                <icon-eye class="preview-icon" />
              </div>
              <div class="image-label" v-if="image.name">{{ image.name }}</div>
            </div>
          </div>
          <div v-else class="empty-attachment">
            <icon-image class="empty-icon" />
            <span>暂无其他附件</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>备注信息</span>
        </div>
        <div class="remark-content">
          <div v-if="supplierRemark" class="remark-text">
            {{ supplierRemark }}
          </div>
          <div v-else class="remark-placeholder">
            暂无备注信息
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <a-image-preview 
      v-model:visible="previewVisible" 
      :src="previewImageUrl"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 图片预览
const previewVisible = ref(false);
const previewImageUrl = ref('');

// 计算附件信息
const attachments = computed(() => {
  return props.supplierData.attachments || {};
});

// 计算主表备注信息
const supplierRemark = computed(() => {
  return props.supplierData.remark || '';
});

// 计算各类附件数量
const businessLicenseCount = computed(() => {
  return attachments.value.businessLicense?.length || 0;
});

const bankProofCount = computed(() => {
  return attachments.value.bankProof?.length || 0;
});

const idCardCount = computed(() => {
  return attachments.value.idCard?.length || 0;
});

const otherAttachmentsCount = computed(() => {
  return attachments.value.others?.length || 0;
});

// 预览图片
const previewImage = (image) => {
  previewImageUrl.value = image.url;
  previewVisible.value = true;
};
</script>

<style scoped>
.attachment-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.attachment-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 200px;
}

.attachment-section {
  background-color: var(--color-bg-1);
  border-radius: 6px;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-1);
}

.file-count {
  font-size: 12px;
  color: var(--color-text-3);
  font-weight: normal;
}

.attachment-grid {
  min-height: 100px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-item {
  position: relative;
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.preview-icon {
  color: white;
  font-size: 24px;
}

.image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  text-align: center;
  padding: 4px;
}

.empty-attachment {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: var(--color-text-3);
  gap: 8px;
}

.empty-icon {
  font-size: 32px;
}

.remark-content {
  margin-top: 8px;
}

:deep(.arco-textarea) {
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border-2);
}

.remark-text {
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  padding: 12px;
  min-height: 80px;
  line-height: 1.5;
  color: var(--color-text-1);
  white-space: pre-wrap;
  word-break: break-word;
}

.remark-placeholder {
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  padding: 12px;
  min-height: 80px;
  line-height: 1.5;
  color: var(--color-text-3);
  font-style: italic;
  display: flex;
  align-items: center;
}
</style>
