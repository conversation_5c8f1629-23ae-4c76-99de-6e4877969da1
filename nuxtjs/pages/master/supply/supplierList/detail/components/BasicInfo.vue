<template>
  <div class="basic-info">
    <div class="info-header">
      <h3>基础信息</h3>
    </div>
    <div class="info-content">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商名称：</span>
            <span class="value">{{ supplierData.supplierName || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">公司性质：</span>
            <span class="value">{{ supplierData.companyNature || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">主营产品：</span>
            <span class="value">
              <a-tag
                v-for="product in supplierData.mainProducts"
                :key="product"
                color="blue"
                style="margin-right: 4px;"
              >
                {{ product.name }}
              </a-tag>
              <span v-if="!supplierData.mainProducts?.length">-</span>
            </span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作方式：</span>
            <span class="value">
              <a-tag :color="getCooperationTypeColor(supplierData.cooperationType)">
                {{ supplierData.cooperationType || '-' }}
              </a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商代码：</span>
            <span class="value">{{ supplierData.supplierCode || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">下单次数：</span>
            <span class="value">{{ supplierData.orderCount || 0 }}次</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作状态：</span>
            <span class="value">
              <a-tag :color="getCooperationStatusColor(supplierData.cooperationStatus)">
                {{ supplierData.cooperationStatus || '-' }}
              </a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作协议：</span>
            <span class="value">{{ supplierData.cooperationAgreement || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商分组：</span>
            <span class="value">{{ supplierData.supplierGroup || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">通讯地址：</span>
            <span class="value">{{ supplierData.address || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">注册时间：</span>
            <span class="value">{{ supplierData.registerTime || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">统一信用代码：</span>
            <span class="value">{{ supplierData.creditCode || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商关联：</span>
            <span class="value">{{ supplierData.supplierRelation || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">详细地址：</span>
            <span class="value">{{ supplierData.detailedAddress || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">生产地址：</span>
            <span class="value">{{ supplierData.productionAddress || '-' }}</span>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 获取合作方式颜色
const getCooperationTypeColor = (type) => {
  const colorMap = {
    "长期合作": "blue",
    "项目合作": "green",
    "临时采购": "orange"
  };
  return colorMap[type] || "gray";
};

// 获取合作状态颜色
const getCooperationStatusColor = (status) => {
  const colorMap = {
    "启用": "green",
    "待审核": "blue",
    "黑名单": "red",
    "不启用": "gray"
  };
  return colorMap[status] || "gray";
};
</script>

<style scoped>
.basic-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-1);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.label {
  font-weight: 500;
  color: var(--color-text-2);
  min-width: 120px;
  flex-shrink: 0;
}

.value {
  color: var(--color-text-1);
  flex: 1;
  word-break: break-all;
}
</style>
