<template>
  <div class="submitter-info-edit">
    <a-card title="提交人信息" :bordered="false">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        auto-label-width
      >
        <a-row :gutter="24">
          <!-- 部门 -->
          <a-col :span="6">
            <a-form-item label="部门" field="department">
              <a-input
                v-model="formData.department"
                placeholder="部门信息"
                disabled
                readonly
              />
            </a-form-item>
          </a-col>

          <!-- 提交人 -->
          <a-col :span="6">
            <a-form-item label="提交人" field="submitter" required>
              <a-input
                v-model="formData.submitter"
                placeholder="提交人信息"
                disabled
                readonly
              />
            </a-form-item>
          </a-col>

          <!-- 提交时间 -->
          <a-col :span="6">
            <a-form-item label="提交时间" field="submitTime">
              <a-input
                v-model="formData.submitTime"
                placeholder="提交时间"
                disabled
                readonly
              />
            </a-form-item>
          </a-col>

          <!-- 复核人 -->
          <a-col :span="6">
            <a-form-item label="复核人" field="reviewer">
              <a-select
                v-model="formData.reviewer"
                placeholder="请选择复核人"
                allow-clear
                :loading="userLoading"
                @search="handleUserSearch"
                filterable
              >
                <a-option
                  v-for="user in userOptions"
                  :key="user.value"
                  :value="user.value"
                  :label="user.label"
                >
                  {{ user.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 保存按钮 -->
        <div v-if="showSaveButton" class="form-actions">
          <a-space>
            <a-button type="primary" :loading="saving" @click="handleSave">
              <template #icon><icon-save /></template>
              保存提交人信息
            </a-button>
          </a-space>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconSave } from '@arco-design/web-vue/es/icon';
import systemApi from '@/api/master/system.js';
import { supplierApi } from '@/api/master/csm/supplier.js';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  },
  supplierId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:formData', 'save']);

// 表单引用
const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive({
  department: '',
  submitter: '',
  submitTime: '',
  reviewer: ''
});

// 用户选项
const userOptions = ref([]);
const userLoading = ref(false);

// 表单验证规则
const rules = {
  submitter: [
    { required: true, message: '提交人不能为空' }
  ]
};

// 监听props变化
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(formData, newData);
  }
}, { immediate: true, deep: true });

// 监听formData变化，向上传递
watch(formData, (newData) => {
  emit('update:formData', { ...newData });
}, { deep: true });

// 格式化时间显示
const formatDateTime = (dateTime) => {
  if (!dateTime) return '';

  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) return '';

    // 格式化为 YYYY-MM-dd HH:mm:ss
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('时间格式化失败:', error);
    return '';
  }
};

// 获取当前用户信息
const getCurrentUserInfo = () => {
  try {
    const userMaster = localStorage.getItem('user_master');
    if (userMaster) {
      const userData = JSON.parse(userMaster);
      console.log('当前用户信息:', userData);

      // 如果表单数据为空，则设置当前用户信息
      if (!formData.department) {
        formData.department = userData.dept_name || '未设置部门';
      }
      if (!formData.submitter) {
        formData.submitter = userData.nickname || userData.username || '当前用户';
      }
      // 如果没有提交时间，设置当前时间
      if (!formData.submitTime) {
        formData.submitTime = formatDateTime(new Date());
      }

      return userData;
    } else {
      console.warn('未找到用户信息');
      if (!formData.department) {
        formData.department = '未设置部门';
      }
      if (!formData.submitter) {
        formData.submitter = '当前用户';
      }
      if (!formData.submitTime) {
        formData.submitTime = formatDateTime(new Date());
      }
      return null;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    if (!formData.department) {
      formData.department = '未设置部门';
    }
    if (!formData.submitter) {
      formData.submitter = '当前用户';
    }
    if (!formData.submitTime) {
      formData.submitTime = formatDateTime(new Date());
    }
    return null;
  }
};

// 搜索用户
const handleUserSearch = async (value) => {
  if (!value || value.trim() === '') {
    await loadUsers();
    return;
  }
  await loadUsers(value);
};

// 加载用户列表
const loadUsers = async (searchValue = '') => {
  userLoading.value = true;
  try {
    const params = {
      page: 1,
      pageSize: 100
    };

    if (searchValue) {
      params.username = searchValue;
    }

    const res = await systemApi.user.getList(params);
    if (res.code === 200) {
      userOptions.value = res.data.items.map((user) => {
        // 构建显示格式：昵称（账号）部门
        let label = '';
        if (user.nickname) {
          label += user.nickname;
        }
        if (user.username) {
          label += `（${user.username}）`;
        }
        if (user.dept_name) {
          label += user.dept_name;
        }

        return {
          label: label || user.username,
          value: user.nickname || user.username, // 使用nickname作为value
          userId: user.id // 保留用户ID用于API调用
        };
      });
    } else {
      console.error('获取用户列表失败', res);
      Message.error('获取用户列表失败');
    }
  } catch (err) {
    console.error('获取用户列表失败', err);
    Message.error('获取用户列表失败');
  } finally {
    userLoading.value = false;
  }
};

// 保存提交人信息
const handleSave = async () => {
  try {
    // 验证表单
    const valid = await validate();
    if (!valid) {
      return;
    }

    saving.value = true;

    // 调用API保存提交人信息
    const saveData = {
      department: formData.department,
      submitter: formData.submitter,
      submitTime: formData.submitTime,
      reviewer: formData.reviewer // 保存nickname
    };

    const result = await supplierApi.updateSubmitterInfo(props.supplierId, saveData);

    if (result.code === 200) {
      Message.success('提交人信息保存成功');
      emit('save', result.data);
    } else {
      Message.error(result.message || '保存失败');
    }
  } catch (error) {
    console.error('保存提交人信息失败:', error);
    Message.error('保存失败，请稍后重试');
  } finally {
    saving.value = false;
  }
};

// 表单验证
const validate = async () => {
  try {
    const valid = await formRef.value?.validate();
    return !valid; // validate返回错误信息，没有错误时返回undefined
  } catch (error) {
    console.error('表单验证失败:', error);
    return false;
  }
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  getCurrentUserInfo(); // 重新获取当前用户信息
};

// 组件挂载时初始化
onMounted(() => {
  getCurrentUserInfo();
  loadUsers(); // 初始加载用户列表
});

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm,
  handleSave
});
</script>

<style scoped>
.submitter-info-edit {
  padding: 0;
}

:deep(.arco-card-body) {
  padding: 20px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
}

:deep(.arco-input[disabled]) {
  background-color: #f7f8fa;
  color: #86909c;
}

.form-actions {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e6eb;
}
</style>
