<template>
  <div class="account-info-edit">
    <div class="edit-header">
      <h4>账户信息</h4>
    </div>
    <div class="edit-content">
      <a-form 
        ref="formRef"
        :model="accountInfoFormData"
        :rules="rules"
        layout="vertical"
        :label-col-props="{ span: 24 }"
        :wrapper-col-props="{ span: 24 }"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="付款条件" field="paymentTerms">
              <a-select
                v-model="accountInfoFormData.paymentTerms"
                placeholder="请选择付款条件"
                allow-clear
                allow-create
                :loading="paymentTermsLoading"
              >
                <a-option
                  v-for="item in paymentTermsList"
                  :key="item.id"
                  :value="item.termName"
                >
                  {{ item.termName }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票类型" field="invoiceType">
              <a-select
                v-model="accountInfoFormData.invoiceType"
                placeholder="请选择发票类型"
                allow-clear
              >
                <a-option value="无票">无票</a-option>
                <a-option value="增值税专用发票">增值税专用发票</a-option>
                <a-option value="增值税普通发票">增值税普通发票</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="税率(%)" field="taxRate">
              <a-input-number
                v-model="accountInfoFormData.taxRate"
                placeholder="请输入税率"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开户名" field="accountName">
              <a-input
                v-model="accountInfoFormData.accountName"
                placeholder="请输入开户名"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="结算方式" field="settlementMethod">
              <a-select
                v-model="accountInfoFormData.settlementMethod"
                placeholder="请选择结算方式"
                allow-clear
              >
                <a-option value="银行转账">银行转账</a-option>
                <a-option value="现金支付">现金支付</a-option>
                <a-option value="支票支付">支票支付</a-option>
                <a-option value="信用证">信用证</a-option>
                <a-option value="承兑汇票">承兑汇票</a-option>
                <a-option value="网银支付">网银支付</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开户账号" field="accountNumber">
              <a-input
                v-model="accountInfoFormData.accountNumber"
                placeholder="请输入开户账号"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="开户行" field="bankName">
              <a-input
                v-model="accountInfoFormData.bankName"
                placeholder="请输入开户行"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="注册资金(万元)" field="registeredCapital">
              <a-input-number
                v-model="accountInfoFormData.registeredCapital"
                placeholder="请输入注册资金"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存账户信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { supplierApi } from '@/api/master/csm/supplier.js';
import { paymentTermApi } from '@/api/master/csm/paymentTerm.js';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  supplierId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const formRef = ref();
const saving = ref(false);

// 付款条件相关
const paymentTermsList = ref([]);
const paymentTermsLoading = ref(false);

// 表单数据
const  accountInfoFormData =  reactive({ ...props.formData });

// 表单验证规则
const rules = {
  paymentTerms: [
    { required: true, message: '请选择付款条件' }
  ],
  invoiceType: [
    { required: true, message: '请选择发票类型' }
  ],
  taxRate: [
    { required: true, message: '请输入税率' },
    { type: 'number', min: 0, max: 100, message: '税率必须在0-100之间' }
  ]
};

// 监听表单数据变化
watch(accountInfoFormData, (newVal) => {
  console.log('表单数据变化:', newVal);
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  Object.assign(accountInfoFormData, newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    if(accountInfoFormData.paymentTerms === undefined || accountInfoFormData.paymentTerms === null || accountInfoFormData.paymentTerms === '') {
      throw new Error('付款条件不能为空');
    }
    if(accountInfoFormData.invoiceType === undefined || accountInfoFormData.invoiceType === null || accountInfoFormData.invoiceType === '') {
      throw new Error('发票类型不能为空');
    }
    if(accountInfoFormData.taxRate === undefined || accountInfoFormData.taxRate === null || accountInfoFormData.taxRate === '') {
      throw new Error('税率不能为空');
    }
    return true;
  } catch (error) {
    console.error('账户信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    // 先验证表单
    const valid = await validate();
    if (!valid) {
      return;
    }

    // 调用API更新账户信息
    const response = await supplierApi.updateAccountInfo(props.supplierId, accountInfoFormData);

    if (response.code === 200) {
      Message.success('账户信息保存成功');
      emit('save', response.data);
    } else {
      Message.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存账户信息失败:', error);
    Message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    saving.value = false;
  }
};

// 获取付款条件列表
const getPaymentTermsList = async () => {
  try {
    paymentTermsLoading.value = true;
    const response = await paymentTermApi.getList();
    if (response.code === 200) {
      paymentTermsList.value = response.data.items.map(item => ({
        id: item.id,
        termName: item.term_name
      }));
    }
  } catch (error) {
    console.error('获取付款条件列表失败:', error);
  } finally {
    paymentTermsLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getPaymentTermsList();
});

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.account-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  margin-top: 16px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
