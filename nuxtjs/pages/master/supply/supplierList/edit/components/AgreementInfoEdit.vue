<template>
  <div class="agreement-info-edit">
    <div class="edit-header">
      <h4>协议信息</h4>
      <a-button type="primary" @click="addAgreement">
        <template #icon><icon-plus /></template>
        添加协议
      </a-button>
    </div>
    <div class="edit-content">
      <div v-if="formData.length === 0" class="empty-state">
        <a-empty description="暂无协议信息，点击上方按钮添加" />
      </div>
      <div v-else class="agreement-list">
        <div 
          v-for="(agreement, index) in formData" 
          :key="agreement.id || index"
          class="agreement-item"
        >
          <a-card class="agreement-card" :bordered="true">
            <template #title>
              <div class="agreement-title">
                <span>协议 {{ agreement.agreementName || index + 1 }}</span>
                <a-button 
                  type="text" 
                  status="danger" 
                  size="small"
                  @click="removeAgreement(index)"
                >
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
              </div>
            </template>
            
            <a-form 
              :model="agreement" 
              layout="vertical"
              :label-col-props="{ span: 24 }"
              :wrapper-col-props="{ span: 24 }"
            >
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="协议类型" :field="`${index}.agreementType`" :rules="agreementRules.agreementType">
                    <a-select
                      v-model="agreement.agreementType"
                      placeholder="请选择协议类型"
                      allow-clear
                    >
                      <a-option value="委托代加工协议">委托代加工协议</a-option>
                      <a-option value="独家合作协议">独家合作协议</a-option>
                      <a-option value="返点协议">返点协议</a-option>
                      <a-option value="经销授权协议">经销授权协议</a-option>
                      <a-option value="合作框架协议">合作框架协议</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="协议名称" :field="`${index}.agreementName`" :rules="agreementRules.agreementName">
                    <a-input
                      v-model="agreement.agreementName"
                      placeholder="请输入协议名称"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="协议状态" :field="`${index}.status`" :rules="agreementRules.status">
                    <a-select 
                      v-model="agreement.status" 
                      placeholder="请选择协议状态"
                      allow-clear
                    >
                      <a-option value="draft">草稿</a-option>
                      <a-option value="pending">待生效</a-option>
                      <a-option value="active">生效中</a-option>
                      <a-option value="expired">已过期</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="开始日期" :field="`${index}.startDate`" :rules="agreementRules.startDate">
                    <a-date-picker
                      v-model="agreement.startDate"
                      placeholder="请选择开始日期"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="结束日期" :field="`${index}.endDate`" :rules="agreementRules.endDate">
                    <a-date-picker
                      v-model="agreement.endDate"
                      placeholder="请选择结束日期"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="签署日期" :field="`${index}.signDate`">
                    <a-date-picker
                      v-model="agreement.signDate"
                      placeholder="请选择签署日期"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="协议文件" :field="`${index}.fileUrl`">
                    <a-upload
                      :file-list="agreement.fileList || []"
                      :limit="1"
                      :auto-upload="false"
                      accept=".pdf,.doc,.docx,image/*"
                      @change="handleFileChange(index, $event)"
                      @before-upload="(file) => handleBeforeUpload(file, index)"
                    >
                      <template #upload-button>
                        <a-button>
                          <template #icon><icon-upload /></template>
                          上传协议文件
                        </a-button>
                      </template>
                    </a-upload>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="协议期限" :field="`${index}.duration`">
                    <a-input 
                      v-model="agreement.duration" 
                      placeholder="如：12个月、3年等"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="协议描述" :field="`${index}.remark`">
                    <a-textarea 
                      v-model="agreement.remark"
                      placeholder="请输入协议描述"
                      :auto-size="{ minRows: 3, maxRows: 6 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存协议信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import commonApi from '@/api/common.js';
import { supplierApi } from '@/api/master/csm/supplier.js';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  supplierId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 协议验证规则
const agreementRules = {
  agreementType: [
    { required: true, message: '请选择协议类型' }
  ],
  agreementName: [
    { required: true, message: '请输入协议名称' },
    { minLength: 2, message: '协议名称至少2个字符' }
  ]
};

// 添加协议
const addAgreement = () => {
  const newAgreement = {
    id: null, // 临时ID
    agreementType: '',
    agreementName: '',
    startDate: '',
    endDate: '',
    signDate: '',
    status: 'draft',
    fileUrl: '',
    fileList: [],
    duration: '',
    description: ''
  };
  formData.push(newAgreement);
};

// 删除协议
const removeAgreement = (index) => {
  formData.splice(index, 1);
};

// 上传前处理
const handleBeforeUpload = async (file, index) => {
  console.log('准备上传协议文件:', file);
  console.log('协议索引:', index);

  try {
    // 创建FormData
    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    uploadFormData.append('dir', 'supplier-agreements');
    uploadFormData.append('module', 'supplier');
    uploadFormData.append('bizType', 'agreement');
    uploadFormData.append('bizId', '');
    uploadFormData.append('isPublic', 'true');

    console.log('FormData内容:');
    for (let [key, value] of uploadFormData.entries()) {
      console.log(key, value);
    }

    // 调用上传API
    const response = await commonApi.uploadImage(uploadFormData);

    if (response.code === 200) {
      // 上传成功，更新文件信息
      const uploadedFile = {
        uid: response.data.id,
        name: response.data.fileName,
        url: response.data.fileUrl,
        size: parseInt(response.data.fileSize),
        type: file.type,
        status: 'done',
      };

      // 更新协议的文件URL和文件列表
      formData[index].fileUrl = response.data.fileUrl;
      formData[index].fileList = [uploadedFile];

      Message.success('文件上传成功');
    } else {
      throw new Error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    Message.error('文件上传失败: ' + (error.message || '未知错误'));
  }

  // 返回false阻止默认上传行为
  return false;
};

// 处理文件上传
const handleFileChange = (index, fileList) => {
  formData[index].fileList = fileList;
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 验证每个协议的必填字段
    for (let i = 0; i < formData.length; i++) {
      const agreement = formData[i];
      if (!agreement.agreementType) {
        Message.error(`第${i + 1}个协议的类型不能为空`);
        return false;
      }
      if (!agreement.agreementName) {
        Message.error(`第${i + 1}个协议的名称不能为空`);
        return false;
      }

      // 验证日期逻辑
      if (agreement.startDate && agreement.endDate) {
        const startDate = new Date(agreement.startDate);
        const endDate = new Date(agreement.endDate);
        if (startDate >= endDate) {
          Message.error(`第${i + 1}个协议的开始日期不能晚于或等于结束日期`);
          return false;
        }
      }
    }
    return true;
  } catch (error) {
    console.error('协议信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    // 先验证表单
    const valid = await validate();
    if (!valid) {
      return;
    }

    // 调用API更新协议信息
    const response = await supplierApi.updateAgreementInfo(props.supplierId, {
      agreements: formData
    });

    if (response.code === 200) {
      Message.success('协议信息保存成功');
      emit('save', response.data);
    } else {
      Message.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存协议信息失败:', error);
    Message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.agreement-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  flex: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.agreement-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.agreement-item {
  width: 100%;
}

.agreement-card {
  background-color: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
}

.agreement-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 12px;
}

:deep(.arco-card-header) {
  padding: 12px 16px;
  background-color: var(--color-fill-1);
}

:deep(.arco-card-body) {
  padding: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
