<template>
  <a-drawer
    v-model:visible="visible"
    title="编辑供应商信息"
    :width="1200"
    :footer="false"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="true"
    @close="handleClose"
    placement="right"
  >
    <div class="supplier-edit">
      <!-- Tab切换编辑区域 -->
      <div class="tab-edit-section">
        <a-tabs v-model:active-key="activeTab" type="line" size="large">
          <a-tab-pane key="basic" title="基础信息">
            <BasicInfoEdit
              ref="basicInfoRef"
              v-model:form-data="formData.basicInfo"
              :errors="errors.basicInfo"
              :supplier-id="supplierId"
              @save="handleSaveBasicInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="account" title="账户信息">
            <AccountInfoEdit
              ref="accountInfoRef"
              v-model:form-data="formData.accountInfo"
              :errors="errors.accountInfo"
              :supplier-id="supplierId"
              @save="handleSaveAccountInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="brand" title="品牌信息">
            <BrandInfoEdit
              ref="brandInfoRef"
              v-model:form-data="formData.brandInfo"
              :errors="errors.brandInfo"
              :supplier-id="supplierId"
              @save="handleSaveBrandInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="contact" title="联系人信息">
            <ContactInfoEdit
              ref="contactInfoRef"
              v-model:form-data="formData.contactInfo"
              :errors="errors.contactInfo"
              :supplier-id="supplierId"
              @save="handleSaveContactInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="agreement" title="协议信息">
            <AgreementInfoEdit
              ref="agreementInfoRef"
              v-model:form-data="formData.agreementInfo"
              :errors="errors.agreementInfo"
              :supplier-id="supplierId"
              @save="handleSaveAgreementInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="attachment" title="附件信息">
            <AttachmentInfoEdit
              ref="attachmentInfoRef"
              v-model:form-data="formData.attachmentInfo"
              :errors="errors.attachmentInfo"
              :supplier-id="supplierId"
              @save="handleSaveAttachmentInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="submitter" title="提交人信息">
            <SubmitterInfoEdit
              ref="submitterInfoRef"
              v-model:form-data="formData.submitterInfo"
              :errors="errors.submitterInfo"
              :supplier-id="supplierId"
              @save="handleSaveSubmitterInfo"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import BasicInfoEdit from './components/BasicInfoEdit.vue';
import AccountInfoEdit from './components/AccountInfoEdit.vue';
import BrandInfoEdit from './components/BrandInfoEdit.vue';
import ContactInfoEdit from './components/ContactInfoEdit.vue';
import AgreementInfoEdit from './components/AgreementInfoEdit.vue';
import AttachmentInfoEdit from './components/AttachmentInfoEdit.vue';
import SubmitterInfoEdit from './components/SubmitterInfoEdit.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save-success']);

const visible = ref(props.visible);
const activeTab = ref('basic'); // 默认显示基础信息
const saving = ref(false);
const supplierId = ref(null);

// 表单引用
const basicInfoRef = ref();
const accountInfoRef = ref();
const brandInfoRef = ref();
const contactInfoRef = ref();
const agreementInfoRef = ref();
const attachmentInfoRef = ref();
const submitterInfoRef = ref();

// 表单数据
const formData = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: [],
  contactInfo: [],
  agreementInfo: [],
  attachmentInfo: {},
  submitterInfo: {}
});

// 错误信息
const errors = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: {},
  contactInfo: {},
  agreementInfo: {},
  attachmentInfo: {},
  submitterInfo: {}
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initFormData();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    activeTab.value = 'basic';
  }
});

// 监听supplierData变化，实时更新表单数据
watch(() => props.supplierData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    initFormData();
  }
}, { deep: true });

// 初始化表单数据
const initFormData = () => {
  const data = props.supplierData;
  console.log('初始化表单数据，原始数据:', data);
  console.log('原始supplierRelation数据:', data.supplierRelation, typeof data.supplierRelation);

  // 设置供应商ID
  supplierId.value = data.id;
  
  // 基础信息
  formData.basicInfo = {
    supplierName: data.supplierName || '',
    supplierCode: data.supplierCode || '',
    companyNature: data.companyNature || '',
    mainProductIds: data.mainProductIds || [],
    cooperationType: data.cooperationType || '',
    cooperationStatus: data.cooperationStatus || '',
    cooperationAgreement: data.cooperationAgreement || '',
    supplierGroup: data.supplierGroup || '',
    address: data.address || '',
    detailedAddress: data.detailedAddress || '',
    productionAddress: data.productionAddress || '',
    registerTime: data.registerTime || '',
    creditCode: data.creditCode || '',
    supplierRelation: (() => {
      const relation = data.supplierRelation || [];
      console.log('设置supplierRelation:', relation, '类型:', typeof relation, '是否为数组:', Array.isArray(relation));
      return relation;
    })(),
    contactPerson: data.contactPerson || '',
    position: data.position || '',
    orderCount: data.orderCount || '',
    registeredCapital: Number.parseFloat(data.registeredCapital) || 0,
    phone: data.phone || ''
  };

  // 账户信息
  formData.accountInfo = {
    paymentTerms: data.paymentTerms || '',
    invoiceType: data.invoiceType || '',
    taxRate: data.taxRate? Number(data.taxRate) : 0 ,
    accountName: data.accountName || '',
    settlementMethod: data.settlementMethod || '',
    accountNumber: data.accountNumber || '',
    bankName: data.bankName || '',
    registeredCapital: data.registeredCapital ? Number(data.registeredCapital) : 0,
    contactPerson: data.contactPerson || '',
    position: data.position || '',
    phone: data.phone || ''
  };

  // 品牌信息
  formData.brandInfo = data.brands ? [...data.brands] : [];

  // 联系人信息
  formData.contactInfo = data.contacts ? [...data.contacts] : [];

  // 协议信息
  formData.agreementInfo = data.agreements ? [...data.agreements] : [];

  // 附件信息
  formData.attachmentInfo = data.attachments ? { ...data.attachments } : {};
  formData.attachmentInfo.remark = data.remark || '';

  // 提交人信息
  formData.submitterInfo = {
    department: data.department || '',
    submitter: data.submitter || '',
    submitTime: data.submitTime || data.created_at || '',
    reviewer: data.reviewer || ''
  };
};

// 基础信息保存
const handleSaveBasicInfo = (updatedData) => {
  // 组件内部已经处理了API调用和消息提示
  // 这里只需要触发保存成功事件
  emit('save-success', updatedData);
};

// 账户信息保存
const handleSaveAccountInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 品牌信息保存
const handleSaveBrandInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 联系人信息保存
const handleSaveContactInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 协议信息保存
const handleSaveAgreementInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 附件信息保存
const handleSaveAttachmentInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 提交人信息保存
const handleSaveSubmitterInfo = (updatedData) => {
  emit('save-success', updatedData);
};

// 关闭处理
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.supplier-edit {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-edit-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.arco-drawer-body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

:deep(.arco-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.arco-tabs-content) {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  height: calc(100vh - 120px);
}

:deep(.arco-tabs-tab-list) {
  flex-shrink: 0;
  padding: 0 20px;
  background-color: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border-2);
}

:deep(.arco-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 20px;
}

:deep(.arco-tabs-content-item) {
  height: 100%;
  overflow-y: auto;
}
</style>
