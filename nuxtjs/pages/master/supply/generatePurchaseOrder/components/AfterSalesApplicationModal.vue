<template>
  <a-modal
    v-model:visible="visible"
    title="申请售后"
    width="1000px"
    @cancel="handleCancel"
    class="after-sales-application-modal"
  >
    <div class="modal-content">
      <a-form
        :model="formData"
        layout="vertical"
        ref="formRef"
        class="after-sales-form"
      >
        <!-- 主要内容区域 -->
        <div class="form-main">
          <!-- 左右分栏布局 -->
          <a-row :gutter="24">
            <!-- 左侧表单区域 -->
            <a-col :span="14">
              <div class="form-left">
                <!-- 选择售后订单 -->
                <a-form-item
                  label="选择售后订单"
                  field="orderNumber"
                  :rules="[{ required: true, message: '请输入订单号' }]"
                   
                >
                  <a-input
                    v-model="formData.orderNumber"
                    placeholder="请输入订单号"
                  />
                </a-form-item>

                <!-- 售后类型 -->
                <a-form-item
                  label="售后类型"
                  field="afterSalesType"
                  :rules="[{ required: true, message: '请选择售后类型' }]"

                >
                  <a-tree-select
                    v-model="formData.afterSalesType"
                    :data="afterSalesTypeOptions"
                    placeholder="请选择售后类型"
                    allow-search
                    :filter-tree-node="filterTreeNode"
                  />
                </a-form-item>

                <!-- 采购员 -->
                <a-form-item
                  label="采购员"
                  field="purchaser"
                >
                  <a-input
                    v-model="formData.purchaser"
                    placeholder="系统自动获取"
                    readonly
                    :loading="purchaserLoading"
                  />
                </a-form-item>

                <!-- 售后员 -->
                <a-form-item
                  label="售后员"
                  field="afterSalesStaff"
                  :rules="[{ required: true, message: '请选择售后员' }]"

                >
                  <a-select
                    v-model="formData.afterSalesStaff"
                    placeholder="请选择售后员"
                    allow-search
                    :loading="afterSalesStaffLoading"
                  >
                    <a-option
                      v-for="staff in afterSalesStaffList"
                      :key="staff.id"
                      :value="staff.name"
                    >
                      {{ staff.name }} ({{ staff.role_name }})
                    </a-option>
                  </a-select>
                </a-form-item>

                <!-- 附件上传 -->
                <a-form-item label="附件上传">
                  <ma-upload
                    v-model="fileList"
                    type="file"
                    :multiple="true"
                    :limit="10"
                    accept=".doc,.xlsx,.xls,.jpg,.png,.pdf"
                    :draggable="true"
                    :size="10 * 1024 * 1024"
                    tip="支持文件类型：.doc、.xlsx、.xls、.jpg、.png、.pdf，最多上传10个文件，单个文件大小不能超过10MB"
                    return-type="url"
                    :request-data="{
                      dir: 'after-sales',
                      module: 'supply',
                      bizType: 'after-sales-application',
                      isPublic: 'true'
                    }"
                  />
                </a-form-item>
                <span class="color-gray m-w-200">支持文件类型：.doc、.xlsx、.xls 、.jpg、 .png、.pdf最多上传10个文件大小不能超过10MB</span>
              </div>
            </a-col>

            <!-- 右侧文本域区域 -->
            <a-col :span="10">
              <div class="form-right">
                <!-- 售后内容 -->
                <a-form-item
                  label="售后内容"
                  field="afterSalesContent"
                  :rules="[{ required: true, message: '请输入售后内容' }]"
                   
                >
                  <a-textarea
                    v-model="formData.afterSalesContent"
                    placeholder="请详细描述售后内容"
                    :rows="6"
                    :max-length="500"
                    show-word-limit
                  />
                </a-form-item>

                <!-- 客户诉求 -->
                <a-form-item
                  label="客户诉求"
                  field="customerDemand"
                  :rules="[{ required: true, message: '请输入客户诉求' }]"
                   
                >
                  <a-textarea
                    v-model="formData.customerDemand"
                    placeholder="请输入客户的具体诉求"
                    :rows="6"
                    :max-length="500"
                    show-word-limit
                  />
                </a-form-item>
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 商品信息表格 -->
        <div class="product-section">
          <h3 class="section-title required-name">选择售后商品</h3>
          <a-table
            :data="productData"
            :pagination="false"
            :scroll="{ x: 600 }"
            class="product-table"
            row-key="id"
            v-model:selectedKeys="selectedRowKeys"
          >
            <template #columns>
              <a-table-column
                :width="50"
                align="center"
                :body-cell-class="'checkbox-column'"
              >
                <template #title>
                  <a-checkbox
                    :model-value="isAllSelected"
                    :indeterminate="isIndeterminate"
                    @change="handleSelectAll"
                  />
                </template>
                <template #cell="{ record }">
                  <a-checkbox
                    :model-value="selectedRowKeys.includes(record.id)"
                    @change="handleRowSelect(record.id, $event)"
                  />
                </template>
              </a-table-column>
              <a-table-column title="商品编码" data-index="productCode" :width="100" align="center">
                <template #cell="{ record }">
                  {{ record.productCode }}
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="productName" :width="250" />
              <a-table-column title="数量" data-index="afterSalesQuantity" :width="120" align="center">
                <template #cell="{ record, rowIndex }">
                  <a-input-number
                    v-model="record.afterSalesQuantity"
                    :min="1"
                    :max="record.originalQuantity"
                    :precision="0"
                    size="small"
                    style="width: 80px;"
                    @change="handleQuantityChange(rowIndex, $event)"
                  />
                </template>
              </a-table-column>
              <a-table-column title="是否退货" data-index="isReturn" :width="100" align="center">
                <template #cell="{ record, rowIndex }">
                  <a-switch
                    v-model="record.isReturn"
                    @change="handleReturnChange(rowIndex, $event)"
                  />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </a-form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmitWrapper" :loading="submitting">
          提交申请
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { purchaseOrderApi } from '@/api/master/csm/purchaseOrder.js'
import { afterSalesApi } from '@/api/master/csm/afterSales.js'
import systemApi from '@/api/master/system.js'
import MaUpload from '@/components/base/ma-upload/index.vue'
import { max } from 'radash'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单引用
const formRef = ref(null)

// 提交状态
const submitting = ref(false)

// 采购员加载状态
const purchaserLoading = ref(false)

// 售后员相关
const afterSalesStaffLoading = ref(false)
const afterSalesStaffList = ref([])

// 售后类型树形数据配置
const afterSalesTypeOptions = ref([
  {
    key: '物流信息',
    title: '物流信息',
    children: [
      { key: '物流不送货', title: '物流不送货' },
      { key: '物流索取费用', title: '物流索取费用' }
    ]
  },
  {
    key: '搬运卸货',
    title: '搬运卸货',
    children: []
  },
  {
    key: '产品安装',
    title: '产品安装',
    children: []
  },
  {
    key: '产品问题',
    title: '产品问题',
    children: [
      { key: '无法使用', title: '无法使用' },
      { key: '产品破损', title: '产品破损' },
      { key: '少发漏发', title: '少发漏发' },
      { key: '产品丢件', title: '产品丢件' },
      { key: '退换货', title: '退换货' }
    ]
  },
  {
    key: '废止订单',
    title: '废止订单',
    children: [
      { key: '退款', title: '退款' },
      { key: '退款退货', title: '退款退货' }
    ]
  },
  {
    key: '其他',
    title: '其他',
    children: []
  }
])

// 树形选择器过滤函数
const filterTreeNode = (searchValue, nodeData) => {
  return nodeData.title.toLowerCase().includes(searchValue.toLowerCase())
}

// 表单数据
const formData = reactive({
  orderNumber: '',
  afterSalesType: '',
  purchaser: '',
  afterSalesStaff: '',
  afterSalesContent: '',
  customerDemand: ''
})

// 文件列表
const fileList = ref([])

// 商品数据
const productData = ref([])

// 表格选择配置
const selectedRowKeys = ref([])

// 全选状态计算
const isAllSelected = computed(() => {
  return productData.value.length > 0 && selectedRowKeys.value.length === productData.value.length
})

// 半选状态计算
const isIndeterminate = computed(() => {
  return selectedRowKeys.value.length > 0 && selectedRowKeys.value.length < productData.value.length
})

// 处理单行选择
const handleRowSelect = (id, checked) => {
  if (checked) {
    if (!selectedRowKeys.value.includes(id)) {
      selectedRowKeys.value.push(id)
    }
  } else {
    const index = selectedRowKeys.value.indexOf(id)
    if (index > -1) {
      selectedRowKeys.value.splice(index, 1)
    }
  }
  console.log('选择商品:', selectedRowKeys.value)
}

// 处理全选
const handleSelectAll = (checked) => {
  if (checked) {
    selectedRowKeys.value = productData.value.map(item => item.id)
  } else {
    selectedRowKeys.value = []
  }
  console.log('全选商品:', selectedRowKeys.value)
}

// 获取采购员信息和商品信息
const fetchPurchaserInfo = async (orderNumber) => {
  if (!orderNumber) {
    return
  }

  try {
    purchaserLoading.value = true
    const response = await purchaseOrderApi.getPurchaserByOrderNumber(orderNumber)

    if (response.code === 200 && response.data) {
      // 自动填充采购员信息
      formData.purchaser = response.data.purchaser || ''

      // 处理商品信息
      if (response.data.products && response.data.products.length > 0) {
        // 将所有商品项合并到一个数组中
        const allItems = []
        response.data.products.forEach((productGroup, groupIndex) => {
          if (productGroup.items && productGroup.items.length > 0) {
            productGroup.items.forEach((item, itemIndex) => {
              allItems.push({
                id: `${groupIndex}-${itemIndex}`, // 生成唯一ID
                productCode: item.productCode || '',
                productName: item.productName || '',
                sku: item.sku || '',
                specification: item.specification || '',
                productImage: item.productImage || '',
                quantity: item.quantity || 0,
                originalQuantity: item.quantity || 0, // 原始数量
                afterSalesQuantity: 1, // 售后数量，默认为1
                unitPrice: item.unitPrice || 0,
                splitType: productGroup.splitType || 'original',
                splitNumber: productGroup.splitNumber || '',
                originalId: item.id,
                // 物流信息
                shippingStatus: item.shippingStatus || 0,
                shippingNumber: item.shippingNumber || '',
                shippingInfo: item.shippingInfo || '',
                // 供应商信息
                suggestedSupplierId: item.suggestedSupplierId,
                suggestedSupplierName: item.suggestedSupplierName || '',
                actualSupplierId: item.actualSupplierId,
                actualSupplierName: item.actualSupplierName || '',
                // 时间信息
                createdAt: item.createdAt,
                updatedAt: item.updatedAt,
                isReturn: false // 默认不选中退货
              })
            })
          }
        })

        // 更新商品数据
        productData.value = allItems
        console.log('获取商品信息成功:', allItems)
      } else {
        // 如果没有商品信息，使用空数组
        productData.value = []
      }

      console.log('获取订单信息成功:', response.data)
    } else {
      // 如果没有找到采购员信息，清空字段
      formData.purchaser = ''
      productData.value = []
      console.log('未找到该订单的采购员信息')
    }
  } catch (error) {
    console.error('获取采购员信息失败:', error)
    formData.purchaser = ''
    productData.value = []
    // 不显示错误消息，因为可能是新订单还没有采购员信息
  } finally {
    purchaserLoading.value = false
  }
}

// 获取售后员列表
const getAfterSalesStaffList = async () => {
  try {
    afterSalesStaffLoading.value = true

    // 调用系统用户API获取售后员列表
    const response = await systemApi.user.getList({
      page: 1,
      pageSize: 100,
      status: 1 // 只获取启用状态的用户
    })

    if (response.code === 200 && response.data && response.data.items) {
      // 处理用户数据，只保留必要字段
      afterSalesStaffList.value = response.data.items.map(user => ({
        id: user.id,
        name: user.nickname || user.username,
        username: user.username,
        role_name: user.role_name || '售后员'
      }))
    } else {
      Message.error(response.message || '获取用户列表失败')

      // 使用备用数据
      afterSalesStaffList.value = [
        { id: '1', name: '赵六', username: 'zhaoliu', role_name: '售后员' },
        { id: '2', name: '钱七', username: 'qianqi', role_name: '售后员' },
        { id: '3', name: '孙八', username: 'sunba', role_name: '售后员' },
        { id: '4', name: '李九', username: 'lijiu', role_name: '售后员' }
      ]
    }
  } catch (error) {
    console.error('获取售后员列表失败:', error)
    Message.error('获取售后员列表失败')

    // 使用备用数据
    afterSalesStaffList.value = [
      { id: '1', name: '赵六', username: 'zhaoliu', role_name: '售后员' },
      { id: '2', name: '钱七', username: 'qianqi', role_name: '售后员' },
      { id: '3', name: '孙八', username: 'sunba', role_name: '售后员' },
      { id: '4', name: '李九', username: 'lijiu', role_name: '售后员' }
    ]
  } finally {
    afterSalesStaffLoading.value = false
  }
}

// 组件显示就加载
watch(() => visible.value, async (newVisible) => {
  if (newVisible) {
    // 重置表单状态
    resetForm()

    if (props.orderData) {
      // 自动填充订单号（使用系统订单编号）
      const orderNumber = props.orderData.id || ''
      formData.orderNumber = orderNumber

      // 获取采购员信息和商品信息
      if (orderNumber) {
        await fetchPurchaserInfo(orderNumber)
      } else {
        // 如果没有订单号，清空数据
        productData.value = []
      }
    }
  }
})

// 处理退货状态变化
const handleReturnChange = (rowIndex, value) => {
  console.log(`商品 ${rowIndex} 退货状态变更为:`, value)
  productData.value[rowIndex].isReturn = value
}

// 处理售后数量变化
const handleQuantityChange = (rowIndex, value) => {
  console.log(`商品 ${rowIndex} 售后数量变更为:`, value ,'原始数量:', productData.value[rowIndex].originalQuantity)
  const product = productData.value[rowIndex]

  // 确保数量在有效范围内
  if (value < 1) {
    product.afterSalesQuantity = 1
  } else if (value > product.originalQuantity) {
    Message.error(`数量不能大于原始数量${product.originalQuantity}`)
  } else {
    product.afterSalesQuantity = value
  }
}



// 提交表单包装函数
const handleSubmitWrapper = async () => {
  console.log('=== 按钮被点击，开始处理 ===')
  try {
    await handleSubmit()
  } catch (error) {
    console.error('=== handleSubmit执行出错 ===')
    console.error('错误详情:', error)
    console.error('错误堆栈:', error.stack)
    Message.error('提交过程中发生错误：' + error.message)
  }
}

// 提交表单
const handleSubmit = async () => {
  console.log('=== 开始提交售后申请 ===')
  console.log('当前表单数据:', formData)
  console.log('formRef.value:', formRef.value)

  try {
    // 表单验证
    console.log('开始表单验证...')
    try {
      const validationResult = await formRef.value?.validate()
      console.log('表单验证结果:', validationResult)

      // 在Arco Design Vue中，验证成功时返回undefined，失败时抛出错误
      console.log('表单验证通过')
    } catch (validationError) {
      console.log('表单验证失败:', validationError)
      console.log('停止提交')
      return
    }

    console.log('表单验证通过，设置提交状态为true')
    submitting.value = true

    // 商品必填项校验 - 基于勾选状态
    const selectedProducts = productData.value.filter(product => selectedRowKeys.value.includes(product.id))
    console.log('选中的商品:', selectedProducts)

    if (selectedProducts.length === 0) {
      Message.error('请至少选择一件商品')
      return
    }

    // 构建提交数据
    console.log('所有商品数据:', productData.value)
    console.log('勾选的商品ID:', selectedRowKeys.value)

    const submitData = {
      orderNumber: formData.orderNumber,
      afterSalesType: formData.afterSalesType,
      afterSalesContent: formData.afterSalesContent,
      customerDemand: formData.customerDemand,
      purchaser: formData.purchaser,
      afterSalesStaff: formData.afterSalesStaff,
      products: selectedProducts.map(product => ({
        ...product,
        quantity: product.afterSalesQuantity // 使用售后数量作为提交数量
      })),
      attachments: Array.isArray(fileList.value) ? fileList.value : (fileList.value ? [fileList.value] : [])
    }

    console.log('提交售后申请数据:', submitData)

    // 调用售后申请API
    console.log('开始调用售后申请API...')
    console.log('afterSalesApi对象:', afterSalesApi)
    console.log('createAfterSalesApplication方法:', afterSalesApi.createAfterSalesApplication)

    const response = await afterSalesApi.createAfterSalesApplication(submitData)
    console.log('售后申请API响应:', response)

    if (response.code === 200) {
      Message.success(response.message || '售后申请提交成功')

      // 触发提交成功事件
      emit('submit', {
        success: true,
        data: response.data,
        message: response.message
      })

      // 关闭弹窗并重置表单
      visible.value = false
      resetForm()
    } else {
      Message.error(response.message || '售后申请提交失败')
    }

  } catch (error) {
    console.error('=== 售后申请提交失败 ===')
    console.error('错误对象:', error)
    console.error('错误消息:', error.message)
    console.error('错误堆栈:', error.stack)

    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }

    // 根据错误类型显示不同的错误信息
    let errorMessage = '售后申请提交失败，请稍后重试'

    if (error.response) {
      const { status, data } = error.response
      if (status === 400) {
        errorMessage = data.message || '请求参数错误，请检查输入内容'
      } else if (status === 404) {
        errorMessage = data.message || '订单不存在，请检查订单号'
      } else if (status === 409) {
        errorMessage = data.message || '该订单已申请过售后'
      } else {
        errorMessage = data.message || errorMessage
      }
    } else if (error.message) {
      errorMessage = error.message
    }

    Message.error(errorMessage)

    // 触发提交失败事件
    emit('submit', {
      success: false,
      error: error,
      message: errorMessage
    })
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  // 重置所有表单字段
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })

  // 重置文件列表和选择状态
  fileList.value = []
  selectedRowKeys.value = []

  // 重置商品退货状态和售后数量
  productData.value.forEach(product => {
    product.isReturn = false
    product.afterSalesQuantity = 1
  })

  // 清空商品数据
  productData.value = []

  // 重置表单验证状态
  formRef.value?.resetFields()
}

// 测试API调用的函数
const testApiCall = async () => {
  try {
    console.log('测试API调用开始...')
    const testData = {
      orderNumber: '测试订单号123',
      afterSalesType: '退货',
      afterSalesContent: '测试售后内容',
      customerDemand: '测试客户诉求',
      purchaser: '测试采购员',
      afterSalesStaff: '测试售后员',
      products: [],
      attachments: []
    }

    const response = await afterSalesApi.createAfterSalesApplication(testData)
    console.log('测试API调用成功:', response)
  } catch (error) {
    console.error('测试API调用失败:', error)
  }
}

// 组件挂载时获取售后员列表
onMounted(() => {
  console.log('组件挂载，检查API导入:', afterSalesApi)
  console.log('afterSalesApi.createAfterSalesApplication:', afterSalesApi.createAfterSalesApplication)

  // 测试API调用
  // testApiCall()

  getAfterSalesStaffList()
})
</script>

<style scoped>

.required-name::before{
  content: '*';
  color: red;
}

.after-sales-application-modal {
  .modal-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-main {
    margin-bottom: 24px;
  }

  .form-left {
    padding-right: 12px;

    :deep(.arco-form-item) {
      margin-bottom: 20px;
    }

    :deep(.arco-form-item-label) {
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 8px;
    }
  }

  .form-right {
    padding-left: 12px;

    :deep(.arco-form-item) {
      margin-bottom: 20px;
    }

    :deep(.arco-form-item-label) {
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 8px;
    }

    :deep(.arco-textarea) {
      min-height: 120px;
    }
  }

  .form-item-required :deep(.arco-form-item-label) {
    &::before {
      content: '*';
      color: #f53f3f;
      margin-right: 4px;
    }
  }



  .product-section {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e5e6eb;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }

    .product-table {
      border: 1px solid #e5e6eb;
      border-radius: 6px;

      :deep(.arco-table-th) {
        background-color: #f7f8fa;
        font-weight: 500;
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;
  }
}

/* 全局样式调整 */
:deep(.arco-form-item-label) {
  font-size: 14px;
  line-height: 22px;
}

:deep(.arco-input),
:deep(.arco-select-view),
:deep(.arco-textarea) {
  border-radius: 6px;
  border-color: #d9d9d9;

  &:hover {
    border-color: #165dff;
  }
}

:deep(.arco-select-option) {
  padding: 8px 12px;

  &:hover {
    background-color: #f2f3ff;
  }
}

/* 树形选择器样式优化 */
:deep(.arco-tree-select) {
  .arco-tree-select-view {
    border-radius: 6px;
    border-color: #d9d9d9;

    &:hover {
      border-color: #165dff;
    }

    &:focus,
    &:focus-within {
      border-color: #165dff;
      box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
    }
  }

  .arco-tree-node {
    padding: 4px 0;

    .arco-tree-node-title {
      padding: 6px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f2f3ff;
      }
    }

    &.arco-tree-node-selected .arco-tree-node-title {
      background-color: #e8f4ff;
      color: #165dff;
    }
  }

  .arco-tree-node-indent {
    width: 16px;
  }
}
</style>
