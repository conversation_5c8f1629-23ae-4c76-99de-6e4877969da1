.order-list-container {
  background-color: #fff;
  border-radius: 4px;
  /* 添加底部内边距，避免分页组件遮挡内容 */
  padding-bottom: 70px;
}

.order-table {
  border: none;
}

/* 表头样式 */
:deep(.arco-table-th) {
  background-color: #f5f7f9 !important;
  color: #4E5969;
  /* 调整表头字体颜色 */
  font-weight: 500;
  font-size: 14px;
  text-align: center;
}

:deep(.arco-table-td),
:deep(.arco-table-th) {
  border: none;
  vertical-align: top;
}

:deep(.arco-table-container) {
  border: none;
}

:deep(.arco-table-thead) {
  display: none;
}

.header-checkbox {
  margin-right: 8px; 
}

.order-checkbox {
  margin: 0px 15px ;
}

.copy-btn-inline {
  background: transparent;
  border: none;
  color: #999;
  padding: 0;
  height: auto;
  margin-left: -12px;
}

.order-time-inline {
  margin-left: 12px;
  color: #999;
}

.order-header-cell {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0; /* 左右内边距为0 */
  background-color: #f5f7f9;
  font-size: 13px;
  color: #666;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
}

.order-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 0; /* 确保左侧没有内边距 */
}

.order-header-right {
  margin-right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-right: 0; /* 确保右侧没有内边距 */
}

.product-info-cell-content {
  display: flex;
  align-items: flex-start;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.product-details {
  font-size: 13px;
  flex-grow: 1;
  min-width: 0;
}

.product-name {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.product-meta {
  color: #999;
  font-size: 12px;
  line-height: 1.6;
}

.product-meta>div {
  margin-bottom: 2px;
}

.product-tags,
.store-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 5px;
}

.risk-tag,
.days-tag,
.speed-tag,
.store-tag,
.goods-tag {
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  border-radius: 2px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.risk-tag {
  background-color: #FFE8E6;
  color: #FF4D4F;
}

.days-tag,
.speed-tag {
  background-color: #F2F3F5;
  color: #86909C;
}

.store-tag,
.goods-tag {
  color: #86909C;
  border: 1px solid #E5E6EB;
}


.price-cell-content {
  text-align: center;
}

.price {
  font-weight: 500;
  color: #333;
  font-size: 13px;
}

.quantity {
  color: #999;
  margin-top: 3px;
  font-size: 12px;
}

.return-link {
  font-size: 12px;
  margin-top: 5px;
}

.return-link:deep(.arco-link-text) {
  text-decoration: none;
}


.status-tag {
  display: inline-block;
  font-size: 13px;
  color: #FF7D00;
}


.payment-cell-content {
  text-align: right;
}

.payment-method {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.highlight {
  color: #FF7D00;
  font-weight: 500;
  font-size: 13px;
}

.payment-detail,
.address {
  color: #999;
  font-size: 12px;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


.consumer-info {
  font-size: 13px;
  color: #333;
}


.status-text {
  font-weight: 500;
  color: #FF7D00;
  font-size: 13px;
}

.shipping-status-cell-content {
  text-align: center;
  padding: 8px 4px;
}

.shipping-status-text {
  font-weight: 500;
  color: #165DFF;
  font-size: 13px;
}

.delivery-time {
  font-size: 12px;
  color: #FF7D00;
  margin-top: 3px;
}

.delivery-detail {
  font-size: 12px;
  color: #999;
  margin-top: 3px;
}

.operation-cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ship-btn {
  width: 70px;
}

.operation-links {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 固定分页在屏幕底部 */
.fixed-pagination {
  position: fixed;
  bottom: 0;
  left: auto; /* 不再使用左侧固定定位 */
  right: 0;
  width: calc(100% - var(--ma-sidebar-width, 242px)); /* 减去左侧菜单栏的宽度，增加到240px */
  max-width: calc(100% - 240px); /* 设置最大宽度为屏幕宽度减去菜单栏宽度 */
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  margin-bottom: 0;
  padding-right: 16px; /* 增加右侧内边距，避免内容贴近右边缘 */
  
  /* 响应式处理，当左侧菜单折叠时调整宽度 */
  @media (max-width: 992px) {
    width: 100%;
    max-width: 100%;
  }
}

/* 表头单元格样式 */
:deep(.arco-table-th) {
  background-color: #f5f7f9 !important;
  color: #4E5969;
  /* 调整表头字体颜色 */
  font-weight: 500;
  font-size: 14px;
  text-align: center;
}


:deep(.arco-table-td ),
:deep(.arco-table-th) {
  border: none;
  vertical-align: top;
}


:deep(.arco-table-tr:not(:has(td[colspan])) .arco-table-td) {
  padding: 16px 8px;
  /* 调整详情行内边距 */
  border-top: none;
}


:deep(.arco-table-tr:hover > td) {
  background-color: transparent !important;
}


/* 样式定义 */
:deep(.arco-table-th .arco-table-cell),
:deep(.arco-table-td .arco-table-cell) {
  display: flex;
  align-items: center;
  vertical-align: middle !important;
  height: 100%; 
}

:deep(.arco-table-td ){
  vertical-align: middle !important;
}

:deep(.arco-table-td .arco-table-cell) {
  padding-top: 0; 
  padding-bottom: 0;
}

/* 修改表头单元格内边距 */
:deep(.arco-table-td[colspan]) {
  padding: 0 !important;
}

/* 确保订单头部单元格内容没有内边距 */
:deep(.arco-table-td[colspan] .arco-table-cell) {
  padding: 0 !important;
}

.message-btn,
.add-btn {
  color: #165DFF;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 4px;
}

/* 搜索区域样式 */
.search-area {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
}


.left-actions {
  display: flex;
  gap: 8px;
}

.right-actions {
  display: flex;
  align-items: center;
}

/* 统计卡片样式 */
.stats-cards-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stats-cards {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  background-color: #fff;
  border-radius: 4px;
  flex: 1;
  
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.stats-card {
  flex: 0 0 auto;
  width: 250px;
  margin: 5px;
  padding: 18px;
  border-radius: 4px;
  text-align: left;
  border: 1px solid #e5e6eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  
  &:hover {
    border-color: #d9e1ff;
    background-color: #f9fafc;
  }
  
  &.active {
    border: 1px solid #165dff;
    background-color: #F0F6FF;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
    
    .stats-title {
      color: #165dff;
      font-weight: 500;
    }
    
    .stats-value {
      color: black;
      font-weight: 500;
    }
  }
}

.stats-corner-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 24px 24px 0;
  border-color: transparent #165dff transparent transparent;
}

.check-icon {
  position: absolute;
  top: 1px;
  right: -23px;
  color: white;
  font-size: 14px;
}

.stats-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 500;
  color: #1d2129;
}

.stats-scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
  
  &:hover {
    background-color: #f2f3f5;
  }
  
  &.stats-scroll-left {
    margin-right: 8px;
  }
  
  &.stats-scroll-right {
    margin-left: 8px;
  }
}

/* 订单标签样式 */
.order-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  align-items: center;
}

.tag-item {
  padding: 4px 8px;
  font-size: 12px;
  color: #4e5969;
  cursor: pointer;
  
  &:hover {
    color: #165dff;
  }
  
  .tag-count {
    color: #86909c;
    margin-left: 4px;
  }
}

.tag-right {
  margin-left: auto;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #4e5969;
}

/* 操作标签 */
.operation-tags {
  display: flex;
  margin-top: 16px;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  
  .tag-item {
    padding: 6px 12px;
    background-color: #f2f3f5;
    border-radius: 4px;
    font-size: 12px;
    color: #4e5969;
    cursor: pointer;
    
    &:hover {
      background-color: #e5e6eb;
    }
  }
  
  .add-order-btn-mobile {
    margin-left: auto;
  }
}

.add-order-btn {
  background-color: #ff7d00;
  border-color: #ff7d00;
  
  &:hover {
    background-color: #ff9033;
    border-color: #ff9033;
  }
}

/* 顶部标签页 */
.order-tabs {
  display: flex;
  border-bottom: 1px solid #e5e6eb;
  padding: 0px 16px;
  .tab-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #4e5969;
    cursor: pointer;

    &.active {
      color: #165dff;
      border-bottom: 2px solid #165dff;
    }
  }
}

/* 采购员信息样式 */
.purchaser-cell-content {
  text-align: center;
  padding: 8px 4px;
}

.purchaser-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.purchaser-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  line-height: 1.2;
}

.purchaser-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.follower {
  font-size: 12px;
  color: #86909c;
  line-height: 1.2;
}

.loading-purchaser {
  font-size: 12px;
  color: #86909c;
  font-style: italic;
}

.no-purchaser {
  font-size: 14px;
  color: #c9cdd4;
}